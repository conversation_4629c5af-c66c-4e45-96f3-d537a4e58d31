Role: data scientist, ML and timeseries expert.
Task: Build notbook python scripts for the following tasks using the dataset in /Dataset/data_01.csv

using conda env plots

Create a jupyter notebook in /Notebook

1- load the dataset~
2- clear the data set and extract features related to rainfall (Perciptation) for istabul city
3- generate the following plots in Python

a) The original time series plot
b) The plot of the first difference of the time series
c) The plot of the growth rate (rate of change) of the time series
d) The correlogram of the original time series
e) The correlogram of the first difference of the time series
f) The correlogram of the growth rate (rate of change) of the time series