{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "view-in-github"}, "source": ["<a href=\"https://colab.research.google.com/github/nachi-hebbar/ARIMA-Temperature_Forecasting/blob/master/Temperature_Forecast_ARIMA.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "rgI5xsf7eUVw"}, "outputs": [], "source": ["pip install pm<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {}, "colab_type": "code", "id": "sJUCGgJAeXmn"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "kzMWL_bGvvuY"}, "source": ["#Read Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 243}, "colab_type": "code", "id": "rKYgCgvcebUM", "outputId": "8d11db02-ce47-432f-a9f6-dd8b06c79dd3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of data (1821, 5)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>AvgTemp</th>\n", "      <th>Sunrise</th>\n", "      <th>Sunset</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DATE</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-01-01</th>\n", "      <td>33.0</td>\n", "      <td>46.0</td>\n", "      <td>40.0</td>\n", "      <td>657</td>\n", "      <td>1756</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-01-02</th>\n", "      <td>35.0</td>\n", "      <td>50.0</td>\n", "      <td>43.0</td>\n", "      <td>657</td>\n", "      <td>1756</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-01-03</th>\n", "      <td>36.0</td>\n", "      <td>45.0</td>\n", "      <td>41.0</td>\n", "      <td>657</td>\n", "      <td>1757</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-01-04</th>\n", "      <td>32.0</td>\n", "      <td>41.0</td>\n", "      <td>37.0</td>\n", "      <td>658</td>\n", "      <td>1757</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-01-05</th>\n", "      <td>24.0</td>\n", "      <td>38.0</td>\n", "      <td>31.0</td>\n", "      <td>658</td>\n", "      <td>1758</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            MinTemp  MaxTemp  AvgTemp  Sunrise  Sunset\n", "DATE                                                  \n", "2014-01-01     33.0     46.0     40.0      657    1756\n", "2014-01-02     35.0     50.0     43.0      657    1756\n", "2014-01-03     36.0     45.0     41.0      657    1757\n", "2014-01-04     32.0     41.0     37.0      658    1757\n", "2014-01-05     24.0     38.0     31.0      658    1758"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df=pd.read_csv('MaunaLoaDailyTemps.csv',index_col='DATE',parse_dates=True)\n", "df=df.dropna()\n", "print('Shape of data',df.shape)\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "kVswd7W7vyi4"}, "source": ["#Plot Your Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 334}, "colab_type": "code", "id": "KppUuT8-ejqD", "outputId": "aabcf955-c76b-436f-8f4d-b1f1f35a975c"}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DATE'>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df['AvgTemp'].plot(figsize=(12,5))"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "2YK8qw2Gv1bT"}, "source": ["#Check For Stationarity"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "8TqnvNbBexKT"}, "outputs": [], "source": ["from statsmodels.tsa.stattools import adfuller\n", "\n", "def adf_test(dataset):\n", "  dftest = adfuller(dataset, autolag = 'AIC')\n", "  print(\"1. ADF : \",dftest[0])\n", "  print(\"2. P-Value : \", dftest[1])\n", "  print(\"3. Num Of Lags : \", dftest[2])\n", "  print(\"4. Num Of Observations Used For ADF Regression and Critical Values Calculation :\", dftest[3])\n", "  print(\"5. Critical Values :\")\n", "  for key, val in dftest[4].items():\n", "      print(\"\\t\",key, \": \", val)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 150}, "colab_type": "code", "id": "muTlXcZYe3Jk", "outputId": "80456da0-cc9f-46a0-dbbc-875d9f8b5c51"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. ADF :  -6.554680125068781\n", "2. P-Value :  8.67593748019951e-09\n", "3. <PERSON><PERSON> Of Lags :  12\n", "4. Num Of Observations Used For ADF Regression and Critical Values Calculation : 1808\n", "5. Critical Values :\n", "\t 1% :  -3.433972018026501\n", "\t 5% :  -2.8631399192826676\n", "\t 10% :  -2.5676217442756872\n"]}], "source": ["adf_test(df['AvgTemp'])"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "XE7o8ZDEv6TV"}, "source": ["#Figure Out Order for ARIMA Model"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {}, "colab_type": "code", "id": "0Y1yq4A5e6j8"}, "outputs": [], "source": ["from pmdarima import auto_arima\n", "# Ignore harmless warnings\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 497}, "colab_type": "code", "id": "6ikhqu-mfHCA", "outputId": "12c50b57-0a9c-4c14-c4a3-3061d4ebd954"}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>SARIMAX Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>           <td>y</td>        <th>  No. Observations:  </th>   <td>1821</td>   \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>           <td>SARIMAX(1, 0, 5)</td> <th>  Log Likelihood     </th> <td>-4139.901</td>\n", "</tr>\n", "<tr>\n", "  <th>Date:</th>            <td>Sun, 06 Jul 2025</td> <th>  AIC                </th> <td>8295.802</td> \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                <td>20:37:38</td>     <th>  BIC                </th> <td>8339.859</td> \n", "</tr>\n", "<tr>\n", "  <th>Sample:</th>                  <td>0</td>        <th>  HQIC               </th> <td>8312.055</td> \n", "</tr>\n", "<tr>\n", "  <th></th>                      <td> - 1821</td>     <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>        <td>opg</td>       <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>         <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>intercept</th> <td>    1.3611</td> <td>    0.397</td> <td>    3.429</td> <td> 0.001</td> <td>    0.583</td> <td>    2.139</td>\n", "</tr>\n", "<tr>\n", "  <th>ar.L1</th>     <td>    0.9707</td> <td>    0.009</td> <td>  113.485</td> <td> 0.000</td> <td>    0.954</td> <td>    0.987</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L1</th>     <td>   -0.1220</td> <td>    0.024</td> <td>   -5.088</td> <td> 0.000</td> <td>   -0.169</td> <td>   -0.075</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L2</th>     <td>   -0.2156</td> <td>    0.024</td> <td>   -8.841</td> <td> 0.000</td> <td>   -0.263</td> <td>   -0.168</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L3</th>     <td>   -0.2029</td> <td>    0.024</td> <td>   -8.430</td> <td> 0.000</td> <td>   -0.250</td> <td>   -0.156</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L4</th>     <td>   -0.1344</td> <td>    0.023</td> <td>   -5.884</td> <td> 0.000</td> <td>   -0.179</td> <td>   -0.090</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L5</th>     <td>   -0.0458</td> <td>    0.024</td> <td>   -1.874</td> <td> 0.061</td> <td>   -0.094</td> <td>    0.002</td>\n", "</tr>\n", "<tr>\n", "  <th>sigma2</th>    <td>    5.5003</td> <td>    0.172</td> <td>   31.930</td> <td> 0.000</td> <td>    5.163</td> <td>    5.838</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Ljung-Box (L1) (Q):</th>     <td>0.00</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>21.32</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(Q):</th>                <td>0.99</td> <th>  Prob(JB):          </th> <td>0.00</td> \n", "</tr>\n", "<tr>\n", "  <th>Heteroskedasticity (H):</th> <td>0.81</td> <th>  Skew:              </th> <td>-0.18</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(H) (two-sided):</th>    <td>0.01</td> <th>  Kurtosis:          </th> <td>3.40</td> \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/latex": ["\\begin{center}\n", "\\begin{tabular}{lclc}\n", "\\toprule\n", "\\textbf{Dep. Variable:}          &        y         & \\textbf{  No. Observations:  } &    1821     \\\\\n", "\\textbf{Model:}                  & SARIMAX(1, 0, 5) & \\textbf{  Log Likelihood     } & -4139.901   \\\\\n", "\\textbf{Date:}                   & Sun, 06 Jul 2025 & \\textbf{  AIC                } &  8295.802   \\\\\n", "\\textbf{Time:}                   &     20:37:38     & \\textbf{  BIC                } &  8339.859   \\\\\n", "\\textbf{Sample:}                 &        0         & \\textbf{  HQIC               } &  8312.055   \\\\\n", "\\textbf{}                        &      - 1821      & \\textbf{                     } &             \\\\\n", "\\textbf{Covariance Type:}        &       opg        & \\textbf{                     } &             \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lcccccc}\n", "                   & \\textbf{coef} & \\textbf{std err} & \\textbf{z} & \\textbf{P$> |$z$|$} & \\textbf{[0.025} & \\textbf{0.975]}  \\\\\n", "\\midrule\n", "\\textbf{intercept} &       1.3611  &        0.397     &     3.429  &         0.001        &        0.583    &        2.139     \\\\\n", "\\textbf{ar.L1}     &       0.9707  &        0.009     &   113.485  &         0.000        &        0.954    &        0.987     \\\\\n", "\\textbf{ma.L1}     &      -0.1220  &        0.024     &    -5.088  &         0.000        &       -0.169    &       -0.075     \\\\\n", "\\textbf{ma.L2}     &      -0.2156  &        0.024     &    -8.841  &         0.000        &       -0.263    &       -0.168     \\\\\n", "\\textbf{ma.L3}     &      -0.2029  &        0.024     &    -8.430  &         0.000        &       -0.250    &       -0.156     \\\\\n", "\\textbf{ma.L4}     &      -0.1344  &        0.023     &    -5.884  &         0.000        &       -0.179    &       -0.090     \\\\\n", "\\textbf{ma.L5}     &      -0.0458  &        0.024     &    -1.874  &         0.061        &       -0.094    &        0.002     \\\\\n", "\\textbf{sigma2}    &       5.5003  &        0.172     &    31.930  &         0.000        &        5.163    &        5.838     \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lclc}\n", "\\textbf{Ljung-Box (L1) (Q):}     & 0.00 & \\textbf{  <PERSON><PERSON><PERSON><PERSON>ra (JB):  } & 21.32  \\\\\n", "\\textbf{Prob(Q):}                & 0.99 & \\textbf{  Prob(JB):          } &  0.00  \\\\\n", "\\textbf{Heteroskedasticity (H):} & 0.81 & \\textbf{  Skew:              } & -0.18  \\\\\n", "\\textbf{Prob(H) (two-sided):}    & 0.01 & \\textbf{  Kurtosis:          } &  3.40  \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "%\\caption{SARIMAX Results}\n", "\\end{center}\n", "\n", "Warnings: \\newline\n", " [1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:                      y   No. Observations:                 1821\n", "Model:               SARIMAX(1, 0, 5)   Log Likelihood               -4139.901\n", "Date:                Sun, 06 Jul 2025   AIC                           8295.802\n", "Time:                        20:37:38   BIC                           8339.859\n", "Sample:                             0   HQIC                          8312.055\n", "                               - 1821                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept      1.3611      0.397      3.429      0.001       0.583       2.139\n", "ar.L1          0.9707      0.009    113.485      0.000       0.954       0.987\n", "ma.L1         -0.1220      0.024     -5.088      0.000      -0.169      -0.075\n", "ma.L2         -0.2156      0.024     -8.841      0.000      -0.263      -0.168\n", "ma.L3         -0.2029      0.024     -8.430      0.000      -0.250      -0.156\n", "ma.L4         -0.1344      0.023     -5.884      0.000      -0.179      -0.090\n", "ma.L5         -0.0458      0.024     -1.874      0.061      -0.094       0.002\n", "sigma2         5.5003      0.172     31.930      0.000       5.163       5.838\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JB):                21.32\n", "Prob(Q):                              0.99   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               0.81   Skew:                            -0.18\n", "Prob(H) (two-sided):                  0.01   Kurtosis:                         3.40\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "\"\"\""]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["stepwise_fit = auto_arima(df['AvgTemp'], \n", "                          suppress_warnings=True)           \n", "\n", "stepwise_fit.summary()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {}, "colab_type": "code", "id": "i2OiS6-gfKHs"}, "outputs": [], "source": ["from statsmodels.tsa.arima_model import ARIMA"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "y-JQDC6yv9_y"}, "source": ["#Split Data into Training and Testing"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 234}, "colab_type": "code", "id": "h-SgGft2fYMF", "outputId": "44f0e4aa-b673-4f81-e3cb-a9ee81948ee4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1821, 5)\n", "(1791, 5) (30, 5)\n", "MinTemp      36.0\n", "MaxTemp      52.0\n", "AvgTemp      44.0\n", "Sunrise     640.0\n", "Sunset     1743.0\n", "Name: 2018-12-01 00:00:00, dtype: float64 MinTemp      39.0\n", "MaxTemp      52.0\n", "AvgTemp      46.0\n", "Sunrise     656.0\n", "Sunset     1754.0\n", "Name: 2018-12-30 00:00:00, dtype: float64\n"]}], "source": ["print(df.shape)\n", "train=df.iloc[:-30]\n", "test=df.iloc[-30:]\n", "print(train.shape,test.shape)\n", "print(test.iloc[0],test.iloc[-1])"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "emUTK7M6wCmx"}, "source": ["## Train the Model"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 470}, "colab_type": "code", "id": "LKr5XHt0fbWT", "outputId": "78d6a43a-ed4b-48b3-9bea-20fb185ed8e4"}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>SARIMAX Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>        <td>AvgTemp</td>     <th>  No. Observations:  </th>   <td>1791</td>   \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>            <td>ARIMA(1, 0, 5)</td>  <th>  Log Likelihood     </th> <td>-4070.198</td>\n", "</tr>\n", "<tr>\n", "  <th>Date:</th>            <td>Sun, 06 Jul 2025</td> <th>  AIC                </th> <td>8156.395</td> \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                <td>20:39:01</td>     <th>  BIC                </th> <td>8200.320</td> \n", "</tr>\n", "<tr>\n", "  <th>Sample:</th>                  <td>0</td>        <th>  HQIC               </th> <td>8172.614</td> \n", "</tr>\n", "<tr>\n", "  <th></th>                      <td> - 1791</td>     <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>        <td>opg</td>       <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "     <td></td>       <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>  <td>   46.5856</td> <td>    0.758</td> <td>   61.454</td> <td> 0.000</td> <td>   45.100</td> <td>   48.071</td>\n", "</tr>\n", "<tr>\n", "  <th>ar.L1</th>  <td>    0.9856</td> <td>    0.005</td> <td>  188.230</td> <td> 0.000</td> <td>    0.975</td> <td>    0.996</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L1</th>  <td>   -0.1412</td> <td>    0.023</td> <td>   -6.124</td> <td> 0.000</td> <td>   -0.186</td> <td>   -0.096</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L2</th>  <td>   -0.2268</td> <td>    0.024</td> <td>   -9.635</td> <td> 0.000</td> <td>   -0.273</td> <td>   -0.181</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L3</th>  <td>   -0.2168</td> <td>    0.023</td> <td>   -9.251</td> <td> 0.000</td> <td>   -0.263</td> <td>   -0.171</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L4</th>  <td>   -0.1479</td> <td>    0.023</td> <td>   -6.491</td> <td> 0.000</td> <td>   -0.193</td> <td>   -0.103</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L5</th>  <td>   -0.0595</td> <td>    0.024</td> <td>   -2.438</td> <td> 0.015</td> <td>   -0.107</td> <td>   -0.012</td>\n", "</tr>\n", "<tr>\n", "  <th>sigma2</th> <td>    5.5093</td> <td>    0.174</td> <td>   31.624</td> <td> 0.000</td> <td>    5.168</td> <td>    5.851</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Ljung-Box (L1) (Q):</th>     <td>0.00</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>14.88</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(Q):</th>                <td>0.97</td> <th>  Prob(JB):          </th> <td>0.00</td> \n", "</tr>\n", "<tr>\n", "  <th>Heteroskedasticity (H):</th> <td>0.82</td> <th>  Skew:              </th> <td>-0.15</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(H) (two-sided):</th>    <td>0.01</td> <th>  Kurtosis:          </th> <td>3.33</td> \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/latex": ["\\begin{center}\n", "\\begin{tabular}{lclc}\n", "\\toprule\n", "\\textbf{Dep. Variable:}          &     AvgTemp      & \\textbf{  No. Observations:  } &    1791     \\\\\n", "\\textbf{Model:}                  &  ARIMA(1, 0, 5)  & \\textbf{  Log Likelihood     } & -4070.198   \\\\\n", "\\textbf{Date:}                   & Sun, 06 Jul 2025 & \\textbf{  AIC                } &  8156.395   \\\\\n", "\\textbf{Time:}                   &     20:39:01     & \\textbf{  BIC                } &  8200.320   \\\\\n", "\\textbf{Sample:}                 &        0         & \\textbf{  HQIC               } &  8172.614   \\\\\n", "\\textbf{}                        &      - 1791      & \\textbf{                     } &             \\\\\n", "\\textbf{Covariance Type:}        &       opg        & \\textbf{                     } &             \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lcccccc}\n", "                & \\textbf{coef} & \\textbf{std err} & \\textbf{z} & \\textbf{P$> |$z$|$} & \\textbf{[0.025} & \\textbf{0.975]}  \\\\\n", "\\midrule\n", "\\textbf{const}  &      46.5856  &        0.758     &    61.454  &         0.000        &       45.100    &       48.071     \\\\\n", "\\textbf{ar.L1}  &       0.9856  &        0.005     &   188.230  &         0.000        &        0.975    &        0.996     \\\\\n", "\\textbf{ma.L1}  &      -0.1412  &        0.023     &    -6.124  &         0.000        &       -0.186    &       -0.096     \\\\\n", "\\textbf{ma.L2}  &      -0.2268  &        0.024     &    -9.635  &         0.000        &       -0.273    &       -0.181     \\\\\n", "\\textbf{ma.L3}  &      -0.2168  &        0.023     &    -9.251  &         0.000        &       -0.263    &       -0.171     \\\\\n", "\\textbf{ma.L4}  &      -0.1479  &        0.023     &    -6.491  &         0.000        &       -0.193    &       -0.103     \\\\\n", "\\textbf{ma.L5}  &      -0.0595  &        0.024     &    -2.438  &         0.015        &       -0.107    &       -0.012     \\\\\n", "\\textbf{sigma2} &       5.5093  &        0.174     &    31.624  &         0.000        &        5.168    &        5.851     \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lclc}\n", "\\textbf{Ljung-Box (L1) (Q):}     & 0.00 & \\textbf{  <PERSON><PERSON><PERSON><PERSON>ra (JB):  } & 14.88  \\\\\n", "\\textbf{Prob(Q):}                & 0.97 & \\textbf{  Prob(JB):          } &  0.00  \\\\\n", "\\textbf{Heteroskedasticity (H):} & 0.82 & \\textbf{  Skew:              } & -0.15  \\\\\n", "\\textbf{Prob(H) (two-sided):}    & 0.01 & \\textbf{  Kurtosis:          } &  3.33  \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "%\\caption{SARIMAX Results}\n", "\\end{center}\n", "\n", "Warnings: \\newline\n", " [1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:                AvgTemp   No. Observations:                 1791\n", "Model:                 ARIMA(1, 0, 5)   Log Likelihood               -4070.198\n", "Date:                Sun, 06 Jul 2025   AIC                           8156.395\n", "Time:                        20:39:01   BIC                           8200.320\n", "Sample:                             0   HQIC                          8172.614\n", "                               - 1791                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const         46.5856      0.758     61.454      0.000      45.100      48.071\n", "ar.L1          0.9856      0.005    188.230      0.000       0.975       0.996\n", "ma.L1         -0.1412      0.023     -6.124      0.000      -0.186      -0.096\n", "ma.L2         -0.2268      0.024     -9.635      0.000      -0.273      -0.181\n", "ma.L3         -0.2168      0.023     -9.251      0.000      -0.263      -0.171\n", "ma.L4         -0.1479      0.023     -6.491      0.000      -0.193      -0.103\n", "ma.L5         -0.0595      0.024     -2.438      0.015      -0.107      -0.012\n", "sigma2         5.5093      0.174     31.624      0.000       5.168       5.851\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JB):                14.88\n", "Prob(Q):                              0.97   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               0.82   Skew:                            -0.15\n", "Prob(H) (two-sided):                  0.01   Kurtosis:                         3.33\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "\"\"\""]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from statsmodels.tsa.arima.model import ARIMA\n", "model=ARIMA(train['AvgTemp'],order=(1,0,5))\n", "model=model.fit()\n", "model.summary()\n"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "QnJXQ4iBwFec"}, "source": ["#Make Predictions on Test Set"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 331}, "colab_type": "code", "id": "2ZtJTygKfg5i", "outputId": "46053cf4-b267-4cb8-c89d-fbb7f1fc079c"}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='DATE'>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["start=len(train)\n", "end=len(train)+len(test)-1\n", "#if the predicted values dont have date values as index, you will have to uncomment the following two commented lines to plot a graph\n", "index_future_dates=pd.date_range(start='2018-12-01',end='2018-12-30')\n", "pred=model.predict(start=start,end=end,typ='levels').rename('ARIMA predictions')\n", "pred.index=index_future_dates\n", "pred.plot(legend=True)\n", "test['AvgTemp'].plot(legend=True)\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 331}, "colab_type": "code", "id": "grTT31Tdfqfb", "outputId": "8caf1342-cc91-496f-c4ca-9159007add83"}, "outputs": [{"data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x7f15c37b9f60>"]}, "execution_count": 14, "metadata": {"tags": []}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXAAAAEpCAYAAACKmHkAAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nOydd3xUZfb/33cmfdJJA8KkQKgJhARCEFmkKSoqiNjb7mJZdXWr61rWdV1d96tr/bmi2Bu6IFJUwAYKSg3FJAQDCSGF9IT0OnN/fzwzQ8qUOyHJJOG+Xy9eM3Pvc+99wsycOfc853yOJMsyKioqKiqDD42rJ6CioqKi0jNUA66ioqIySFENuIqKisogRTXgKioqKoMU1YCrqKioDFJUA66ioqIySHHrz4uFhITI0dHR/XlJFRUVlUFPWlpahSzLoV2396sBj46OZv/+/f15SRUVFZVBjyRJJ61tV0MoKioqKoMU1YCrqKioDFJUA66ioqIySOnXGLg12traKCwspLm52dVTUeknvLy8iIyMxN3d3dVTUVEZ1LjcgBcWFuLn50d0dDSSJLl6Oip9jCzLVFZWUlhYSExMjKuno6IyqHF5CKW5uZlhw4apxvscQZIkhg0bpt5xqaj0Ai434IBqvM8xhuz7XXwYVHlmlX5kQBjwgcD69euRJImjR49atuXl5eHt7U1iYiITJ07k5ptvpq2tDYDt27ezePFiAN5++20kSeLrr7/udr61a9datlVUVODu7s7KlSv76a8CX19fAE6dOsVVV11ld+zzzz9PY2Oj5fUll1zC6dOn+3R+Q4bc7fDqL8Sjiko/oRpwE6tXr+b8889n9erVnbaPHj2aQ4cOkZ6eTmFhIf/73/+sHp+QkMBHH33U6XxTpkzpNGbNmjWkpqZ2u4aztLe3O33MiBEjOv2YWKOrAf/iiy8IDAx0+lrnJIc/Fo81Ba6dh8o5hWrAgfr6enbu3Mkbb7zRyQh3RKvVkpKSQlFRkdX9s2fPZu/evbS1tVFfX8/x48dJTEzsNGb16tX85z//oaioiMLCQqvniY6O5v777ychIYGUlBSOHz8OwK233sqdd97JjBkzuP/++8nJyWHRokUkJycze/Zsy53DiRMnmDlzJgkJCTz88MOW8+bl5REfHw+AwWDgT3/6E/Hx8UyePJmXXnqJF198kVOnTjF37lzmzp1rmUtFRQUAzz77LPHx8cTHx/P8889bzjlhwgRuu+02Jk2axIUXXkhTUxMAL774IhMnTmTy5Mlce+21jt+EwUxbE2RtEs8byl07F5VzCpdnoXTksU2ZHDlV26vnnDjCn0cvm2R3zIYNG1i0aBFjx45l2LBhpKWlkZyc3GlMc3Mze/bs4YUXXrB6DkmSWLBgAVu3bqWmpobLL7+cEydOWPYXFBRQXFxMSkoKV199NR9//DF//OMfrZ4rICCA9PR03n33XX73u9/x2WefASJj58cff0Sr1TJ//nxWrlxJXFwce/bs4a677uLbb7/lvvvu4ze/+Q0333wzL7/8stXzv/baa+Tl5XHo0CHc3NyoqqoiODiYZ599lm3bthESEtJpfFpaGm+99RZ79uxBlmVmzJjBnDlzCAoK4tixY6xevZpVq1Zx9dVX88knn3DjjTfy1FNPceLECTw9PYd+GObYl9BaJ57XqwZcpf9QPXCEZ2z2Eq+99tpOIY6cnBwSExMJDw9n+PDhTJ482eZ5rr32Wj766CM++ugjrrvuuk77Pv74Y66++mqr1+iK+djrrruOXbt2WbYvX74crVZLfX09P/74I8uXLycxMZE77riD4uJiAH744QfL8TfddJPV83/99dfccccduLmJ3+/g4GCbcwHYuXMnS5cuRafT4evry5VXXsmOHTsAiImJsdxpJCcnk5eXB8DkyZO54YYbeP/99y3XGbKkrwHfcAjUqx64Sr8yoL5ZjjzlvqCqqopvv/2W9PR0JEnCYDAgSRJPP/00cCYGXlFRwaxZs9i4cSOXX3651XOlpKSQnp6Oj48PY8eO7bRv9erVlJSU8MEHHwBiUfHYsWPExcV1O0/HLI2Oz3U6HQBGo5HAwEAOHTpkdR79meXh6elpea7Vai0hlM8//5zvv/+eTZs28cQTT5Cenj40DXlzDWR/CdN+BUVp0FDm6hmpnEOc8x742rVruemmmzh58iR5eXkUFBQQExNj8TDNhISE8NRTT/Gvf/3L7vmeeuopnnzyyU7bsrOzqa+vp6ioiLy8PPLy8vjrX/9q0wv/+OOPLY8zZ87stt/f35+YmBjWrFkDiOKYw4cPAzBr1ixLHN/8Y9GVhQsX8uqrr1oWQ6uqqgDw8/Ojrq6u2/jZs2ezfv16GhsbaWho4NNPP2X27Nk2/w+MRiMFBQXMnTuXf//739TU1FBfX29z/KAm6zMwtEDCcvANg4YKV89I5RzinDfgq1evZunSpZ22LVu2zKpxXbJkCY2Njd2Me0cuvvhiyyJgT64BUF1dzeTJk3nhhRd47rnnrI754IMPeOONN5gyZQqTJk1iw4YNALzwwgu8/PLLJCQk2FxwXbFiBXq9nsmTJzNlyhQ+/PBDAG6//XYWLVrUbf5JSUnceuutpKSkMGPGDFasWMHUqVNt/h8YDAZuvPFGEhISmDp1Kvfee+/QzWZJXwNBMTAyCXQhaghFpV+R5H4sPJg2bZrcVQ88KyuLCRMm9NscBjpmzfSuC4lDjSHxvteVwrPjYfYfYd7D8O0TsOMZeKQCNFpXz05lCCFJUposy9O6bj/nPXAVlR6T+SnIRhE+AdCFiteNVa6dl8o5wxBcVRrcmLM4VAYBGWshIgFCx4nXvqaOVw1lZ56rqPQhqgeuotITqk5A4T6I7yBPoDMbcDUOrtI/qAZcRaUnZJhkCeKXndlmNuBqMY9KP6EacBUVZ5FlSF8L+vMgcNSZ7aoHrtLPKDLgkiTlSZKULknSIUmS9pu2BUuS9JUkScdMj0F9O1UVlQFCaSaUH4WEZZ23ewWCxk014Cr9hjMe+FxZlhM7pLI8AHwjy3Ic8I3p9aDFmpysUp544gkSExNJTExEq9Vanr/44ot9MFMVl5O+RhjqiZ1z+9FohBeuVmOq9BNnE0K5AnjH9PwdYMnZT8d12JKTVcJDDz3EoUOHOHToEN7e3pbn9957bx/MVMWlGI2Q8QmMnge6Yd3360IGVzWmoR0+vglOfO/qmaj0AKUGXAa+lCQpTZKk203bwmVZLjY9LwHCrR0oSdLtkiTtlyRpf3n5wLy17Conu2XLFpYvX27Z37F5wxtvvMHYsWNJSUnhtttu45577rF6ToPBwJ///GemT5/O5MmTefXVVy3nmjNnDldccQWxsbE88MADfPDBB6SkpJCQkEBOTg5wRj522rRpjB071qJIqOJiCvcKze94G80xdGFQP4g88BPbIWsjHPrQ1TNR6QFK88DPl2W5SJKkMOArSZI6xRlkWZYlSbJa0inL8mvAayAqMe1eZfMDUJKucEoKiUiAi5+yO6SrnGxQUBB79uyhoaEBnU7Hxx9/zLXXXsupU6d4/PHHOXDgAH5+fsybN69b0wYzb7zxBgEBAezbt4+WlhZmzZrFhRdeCMDhw4fJysoiODiY2NhYVqxYwd69e3nhhRd46aWXOult7927l5ycHObOncvx48fx8vLq3f8fFedIXwNu3jD+Euv7daFQcax/53Q2pJuyafJ3u3YeKj1CkQcuy3KR6bEM+BRIAUolSRoOYHocRG5HZ7rKya5Zs4ZFixaxadMm2tvb+fzzz7niiivYu3cvc+bMITg4GHd3905eele+/PJL3n33XRITE5kxYwaVlZUcOya+2NOnT2f48OF4enoyevRoi2FPSEjoVMhz9dVXo9FoiIuLIzY2tkfxeZVexNAmqi/HXQyeftbH6EJEDNyBRIXRKNOfMhZWMTeicNdB9QmoK3HtfFScxqEHLkmSDtDIslxnen4h8A9gI3AL8JTpccNZz8aBp9wX2JKTfeutt3j55ZcJDg5m2rRp+PnZ+MLaQJZlXnrpJS666KJO27dv395JglWj0VheazSaTu3SusrCDtlmwIOF3O3QWHmmdN4avmHQ3gyt9baNPPDntT9RVtfMe7+e0fvzVEr2VjHP+X+Db/4hvPBJg3op65xDiQceDuyUJOkwsBf4XJblLQjDvVCSpGPAAtPrQYctOVk3NzcOHDjAqlWrLN759OnT+e6776iurqa9vZ1PPvnE5nkvuugiXnnlFUsT5OzsbBoaGpya25o1azAajeTk5JCbm8u4ceN6/oeqnD3pa8ErAMbMtz1GQS545qkaPjlQSEZRTS9P0EnMjShS7xZhITWMMuhw6IHLspwLdAv0yrJcCdj5JA8OVq9ezV/+8pdO25YtW8ZHH33E4sWLefvtt3nnHZFsM3LkSB588EFSUlIIDg5m/PjxBAQEWD3vihUryMvLIykpCVmWCQ0NZf369U7NTa/Xk5KSQm1tLStXrlTj366ktRGOfgbxV4Kbp+1xujDxWF8OwbFWhzz/tQilVTe20dpuxMPNBfV0TadFK7jpK8DdCyKnQf4ux8epDCxkWe63f8nJyXJXjhw50m3bQKaurk6WZVlua2uTFy9eLK9bt65PrnPLLbfIa9as6ZNzDwQG2/sup38iy4/6y3Lud/bHFR0U445ssrr7cEG1HPWXz+QLnt4mR/3lM7mwurEPJquAtHfFPAv3i9ffPC7Lfw+S5eY618xHxS7AftmKTVVL6Z3k73//O4mJicTHxxMTE8OSJWrM8JwgfS34RkDULPvjfE0euI1inue+yibQx53fLxQt90prm3tzlsrJWCsaUYxIEq/1qSAboGi/3cO+zy7nDx8fwmh08QKsCqDKyTrNM8880y/Xefvtt/vlOioKaKqG41/B9NscN2rwMTXisFLMk3aymm0/l3P/onHEhoj+pmW1Lb09W8fUlYjCndl/AvPCeOR0QBJx8NgLrB/W3Maf1hymrK6Fu+aOYUyYb3/NWMUGqgeuouKIrE1gaIUEG8U7HXHzEAudVop5nv86m2E6D26ZGU24v1jPKKtzgQduaUTR4e/xCoDweLtx8Ge/yqasTvzgHMyv7utZqihgQBhw2dX5sCr9yqB7v9PXQPBoGGG7D2gndGHdslD2nqhix7EK7pwzGp2nG8N0Hmg1kms88PQ1nRtRmNGnQsE+UV7fhcxTNbzzYx7Xz9Dj5+XGgfzT/TRZFXu43IB7eXlRWVk5+L7UKj1ClmUqKysHT0ZNbTGc2CG8VaV5+LrQbiGUZ7/6mVA/T25MjQJAo5EI8fXofw+8KheK0qznsutToa0BSjtXQxuNMg+vzyBY58FfLhpP4qhA1QMfILg8Bh4ZGUlhYSEDVSdFpffx8vIiMjLS1dNQRuangGxb+8QavqFQlmV5+ePxCnbnVvHoZRPx9jgTQw/396K0vz3wdFPtQvyy7vv0M8Vj/p5Odxsf7SvgYP5pnr16CgE+7kzVB/HSt8eob2nH19PlJuScxuX/++7u7sTExLh6Gioq1klfA8OnQOhY5cfoQqFBqPvJssyzX2UT4e/FdSn6TsPC/DwpOt2PHrgsi78nahYEWPkBDRgJAaNEHDz1TgAq61v495ajzIgJZunUkQAk6QORZThccJpZY0L6b/4q3XB5CEVFZcBSmQOnDjjnfYOIgTdVg6GN749VsP9kNXfPG4OXe+cMljB/L8r7M4RSmgEVP1v3vs3oU0Umiimk+a/NR2loaeefS+ItUg5TR4neLWoYxfWoBlxFxRYZnwCSfYNnDZ3wSuWGcp79KpuRgd5cM21Ut2Fhfp5U1LfSZjD2wmQVYGlEYad2QZ8K9SVQncfeE1WsTSvktl/EEhd+RtclwMed0aE6dSFzAKAacBUVa1jCDeeJ0IIzmPRQ9mb8zOGC0/x23hir5fJhfmIht6K+H+LgRqOIf4+eb70RhRlTHLw9bxcPr09nZKA3986L6zYsSR/EwfxqNfnAxagGXEXFGiU/QUW2stzvrpiqMT/78TD6YB+WJVtfsA33F5oq/bKQWbAbagvtKykChE4AzwCy939Fdmk9f798UqeFVzNT9UFUN7aRV9nYRxNWUYJqwFVUrKEk3GALkwdeX1XCvfPjcNda/5qZPfCy/iinT18rFAfHXWx/nEZD8/BkPIr2smBCOAsnWm20RVJUIAAHTqpxcFeiGnAVla4YjZCxDsYsAJ9g5w83ldOP821iSeIIm+PMHri5urHPMDeiGH8JeDouf99SG80YqZB/LIywOSYuzA9fTzcOFqgG3JWoBlxFpSv5u6C2yHG4wQZfZNfTIrszX6/BzYb3DTDM1xON1A8eeM42aKpS9Pd8e7SUD4tFzH9Ene32hlqNxJRRARw4qS5kuhLVgKuodCV9Dbj7OA43WMFglHn+m+Oc1gQy2sd+fFirkRjm69n3Hnj6GvAKFAuYdmhqNfC3DZk0hExG1rg71AdP0gdxtKSWhpbupfcq/YNqwFVUOtLeCkfWw7hLwEPn9OGbDp/ieFk9noHhaBq7KxJ2Jdzfs28lZVsb4ejnMPEKIbRlh5e3HaewuolHliYjjZgqKjLtMFUfiFGGnwpd3FnoHEY14CoqHcndJopwehA+aTcYeeGbY4yP8CNg2AirioRdCfPz6lsPPHuz0Ddx8PccL6vn1e9zuHLqSFJjh4F+hihiarP942Iu6DmgFvS4DNWAq6h0xBJumOf0oZ8eLOJERQN/WDgWyTfMqiZ4V8L9+ziEkr4W/EaIfHYbyLLMI+sz8HbX8uClE8RG/UwhoXvqoM3jgnQexIboOKgW9LgM1YCrqJhpbRDhhklLHIYbutJmMPLit8dIGBkgUu90IUJS1kGhS6ifFxX1LbT3RTVmUzUc+0r08bTTiGLj4VPsyq3k/kXjCfE19fscNUM8OoiDJ+oD1YIeF6IacBUVMz9vhrbGHoVPNh0+RUFVE79bECc0Q3zDwNgGzfa90zA/T2QZKhtaezpr2xzZKOZgpxip3WDkn59nMSUyoLPYli4EQsY67FSfpA+isqGVgqqm3pq1ihOoBlxFxYw53KC3HW6whizLrNpxgrgwX+aNN/XENBXzOAqjmDvz9MlCZvoaGDYGhifaHFJc00x5XQvXpujRarronetToWCPyIu3wVS9qaBHjYO7BNWAq6gANFbB8a9N4QbnvhY/5lSSVVzLitkxFsU+iwF3sJAZ5mcq5untcvraU5C3U9xN2GlEUVgtPOdRQT7dd45KFXcQFT/bPH5cuB8+HlrVgLsI1YCrqABkmcMNzodPXt+RS4ivB1ckdhC9snjg9huVhPVVNabCRhSF1SJXfVSwd/ed+lTxaCcO7qbVMCUyUF3IdBGKDbgkSVpJkg5KkvSZ6fV8SZIOSJJ0SJKknZIkjem7aaqo9DHpa2FYnGje4ATHy+rY9nM5N8+M7qz3rdCAh/h6Ikl9EEJJXyNCJyH2v5aF1U1IEgwPsGLAg2OFtrmDOPhUfSBZxbU0tRrOZsYqPcAZD/w+IKvD61eAG2RZTgQ+BB7uzYmpqPQbNUWmcIMTfS9NvL7jBJ5uGkuvSws+wwDJoQF312oYpvPoXQ+8Mkek/ym4myisbiLC38uq3C2SZGrw4Lgis90ok16kFvT0N4oMuCRJkcClwOsdNsuAv+l5AHCqd6fWR1TmwOsLoK7U1TNRGShkrsPpvpdAeV0L6w4WsSw5kmBdl7RDrZsQwnJgwMFUzNObHnjmOkQjiisdDi2obiQyyIr3bUY/E07ni5i6DRLVhUyXodQDfx64H+i4HL0C+EKSpELgJuApawdKknS7JEn7JUnaPyAaF2dtgsJ9cPwrRcPf332SD/ac7ONJqbiU9LWiia+DcENX3t99ktZ2I78+30ZPV12YsmrM3i7mKcuC4Bjwt62EaKaouolIawuYZvTmfHDbYZQQX0+ihvmo0rIuwKEBlyRpMVAmy3Jal12/By6RZTkSeAt41trxsiy/JsvyNFmWp4WGhp71hM8a8wfRwW0hiOKM/9tylP/37fE+npSKy6g4BsWHnPa+m9sMvLf7JAsmhDE61IZEqy5EUTVmmJ8nZb3ZG7Om0HrT4i60GYwU1zQxyp4HHjFZCHspyAc/WHBaLejpZ5R44LOAyyVJygM+AuZJkvQ5MEWWZbPazceAc8mzrsBoFJ1JwOEHEmBXTiW1ze0U1zRz6rRaqDAkSV+L0nBDR9YdKKKqoZVfnx9re5BvGDQ49sDD/b0or2vBYOwl41dTKLrLO6CkphmjjH0PXOsOkdMcOjxT9YGU17VY0hJV+geHBlyW5b/Kshwpy3I0cC3wLXAFECBJ0ljTsIV0XuAcmFQeE+XFIeOg8rhD72hLZollTWu/ens49JBlyFgL0ecrCjeYMRplXt+ZS/xIf1Jj7TR80IUq9sCNMlQ29EIYxdAGdcWKPPACUwqh3Rg4iDh4aQY019ockqQ3daovUNMJ+5Me5YHLstwO3AZ8IknSYUQM/M+9ObE+wexFnP8702vbXrjBKPNlZikXTYzA212rxveGIsWHxA+5k7nf27PLyC1v4LbZsWcKd6yhC4WWWruKfgBh/ubWar1gwOuKQTYqMuCFpvJ3ux44iEwU2SjWjmwwLsIPL3eN+j3pZ5wy4LIsb5dlebHp+aeyLCfIsjxFluULZFnO7Zsp9iL5u8EnBCZdCVpPu7eFaSerqahv4dLJw0kcFUia+sEceqSvBY07TLzcqcNWfX+C4QFeXJIw3P5ApcU85mrM3oiD1xSKRyUGvLoRjQTDA73sD4ycDpJGlNXbwF2rYXKkELZS6T/OrUrM/F3Cm3D3gpFJdj3wLRkleLhpmDs+jOSoII4Uq51HhhRGA2R8AnELwTtI8WEZRTXsyq3k1vOibTYrtqC4GrMXPXCLAXccAy+sbmJ4gLfjv8PTD8LjFeWDZ56qpblNLejpL84dA15XAtV5Ip4HwpAXHxIdS7ogyzJbM0v4RVwIvp5uJEcHYTDKHC5U43tDhpM/inCDHaU+a7y+Ixedh5ZrOyr32cLXJGzlwICHmiRcS3vFgBeIR/+R9schDPhIR/FvM/qZULhfxNhtMFUfSLtRJkMt6Ok3zh0Dbva2LQZ8JhjbRdeRLqQX1VB0uomLJomu3EnmziNqGGXokL4G3HUwVnnfy+KaJj77qZhrpusJ8HZ3fIBOdKd3ZMA93DQE6zx6KYRSJKpAPRzEtREhFIcLmGb0qUJqt+Qnm0PMyoSqLkr/cW4ZcDdvGD5ZvB6VYtre/bZwS0YJWo0khPmBAB934sJ81UyUoUJ7KxzZAOMvVWTozLz9Yx5GWeaXs6KVHaBQkRBEHLx3PHBlOeCt7UaKa5sdL2CasQhb2Q47hvl5ERnkrVZk9iPnkAHfJfJZtSbPyTsIQid0+0DKssyWjBJmxg4j0OdMefS06CAOnKzG2Fu5uiquI+cbIZPqRPZJfUs7H+7J5+L44YwKVmj0PHTCy1eSSujvRXlvLWL6OzbgxTVNyLKCFEIz/iMgUK+ooOeA2qGn3zg3DHhLnbj1M3sRZvSpULBXLGiZOFZWT25FA4viIzoNTdIHUdvcTk55fX/MWKUvSV8D3sEweq7iQ/63r4C65nZWzLZRNm8L31CFeii9VE6v0AO3qwNuC/1MYcDtGOckfSCltS0U1/RBgwqVbpwbBrxwv8hj7WbAZ4o83bIjlk2b00XxzoWm8ImZ5CgRB1fDKIOclno4+oXoe6lVEMdG1AS8+cMJpkUFMVWvPGMFMBXzKKnG9KS8ruXs7vCaa6ClRnEKITjhgYP4/jSUQZXtjGHz/48aRukfzg0Dnr9b5LFGpnTebiWutyWzhGR9kCW1y0xMiI5gnYeaDz7Y+XkztDc5FT7ZmllCYXWT8943OFGN6UW7Uaaq8Sx6Y9YUiUclVZhVTWg1EsMDHOSAd8ScAGAnjDJhuD+ebhp1IbOfODcMeMFuCJ8EXv6dtwfqRQ9E0wfyZGUDWcW13cInAJIkifieasAHN+lrRIx4VKrjsSZW7cglapgPCyd2/1w4RBeqaBEz3N+cSngWoQencsAbifD3ws1RDnhHQsaBV6DdfHAPNw0JIwNUD7yfcHP1BPocQzsU7IOpN3TfJ0lCLtNkwLdklABY0ge7khwVxNdZpVTWtzDMlLt7TtJYJbI4jAoLmyQJxi5S5Bn2KY1VYgEz9S7FfS/TTlZzMP80j10+qXvTXyXoQqGxQgip2blmqJ+pmKeuhUnOX0VgzgFXGAN3KnwCYv6jZkDudti7yuawFV4l7M6tom13pigSGneJU1ozKsoZ+ga8NB3aGsQHzxr6maJ/4OkCtmSWkDAywGaWwbRoc3zvtCXF8JzDaICPb4STPzh3XO53cM17fTMnpXz7T/GjM/kaxYe8viMXfy83rkru4Y+Pb5hYf2mqOpMXbgVzOX352aQS1hQKaQBfx5/Nwuomzo+zPR+bxC2EY1vhiz/ZHLIIWKQFtpg2FB2EJS87fy0Vhwx9A961gKcrpjh49c/fczA/kD9fNM7mqRJGBuCulUg7WX3uGvAfXxLGe/FzMP4yZcdsewIOfSjU7LqGsfqL7C9h/xsw8x6IiFd0SH5lI1szS7hjzmh0nj38qnQs5rFnwHsrhOI/wuHdRUu7gdK6Zuc9cICU2yB+WafMra6U1Tdz8fM7+P3CsdxY/JQi7X2VnnEOGPBdEKCHABulxWGTwMOP0ozvgCusxr/NeLlrmTQigLSTVX0z14FO8WHhxU64HJJ/qbx/ZOL1kPYWHP1MPO9vGipgw93ivZ73iOLDXv0+B61G4tbzont+bV3HcvoJNod5umkJ9HE/u1RChTrgp043m3LAnUgh7IiPHQldIMwXvAIj2FWq4caY2aL7VX3ZGWkBlV5jaC9iyjLk7+mePtgRrRtETsO7eC9xYb62u6uYmBYVxOHCGlrbjXbHDTnammDd7aJM+7IXnGr+WxaQQLv/KOT0NX04QRvIMmy8VxTuXPmaEDJTwOc/FfPBnnyuS9ET7u9EpkZXnKjGDPfzOjsPvFZpDngPUgidJFEfyMGT1YoyV1R6ztA24NV5UF9i34ADjRHTGdWWxxXjdQ5PmRwVRGu7kcxT55hgz9d/h/KjIpbpwAPryBsNFuEAACAASURBVI85Fcz+v+2srErCmLOdP7z5Jf/v22PsPFZBbbNtYaRe4+B78PPnMP9vikMnGUU1/HHNIZKjgnjoUttesyIsioRKqjHPopjHaBCNh50o4ulLA56kD+JUTTMlPuPAzUs14H3E0A6hWOLf9g34HsNY5koylwUXAtPsjjUX9KSdrHa+qGOwcvwb2LMSUu6AMQsUH5Z2sooV7+wnapgP+kk3of1xA9GlX/FM9gWWMaNDdSSOCiJRH8jUUYGMi/BzLG+qlMoc2PwARM+G1LsVHVJe18Lt7+4nyMeDlTcm4+mmPbs5eAeBpFVUzBPq50lueUPPrlNfKhZobYUKO1BY3YibRiLibO4sHJBkFrY61cjFI5LUOHgfMcQN+C7wDBCaJ3ZYfSqM2WjQ1/8ELLE7Nszfi1HB3qSdrGbF7F6c60ClsQrW3yVygBc+pviwjKIabn1zH+H+Xry/YgZhfl5wfBL3ehzilvue5Kei0xzKP82hgtNs/7mMTw6IHGZPNw2z40J46bokvD3Ownga2uHTO0DjBktXKkobbGk3cOf7aVQ1trL2zvMI9euFVFGNxtTc2HE5fbi/F2V1zciybL/TjzWc1QEPdDIH3EkmjvDHQ6vhQH41F+tT4YcXoLVB6MOo9BpD3IDvFnnedr68tc1tbMttoCxwPCPsdBzpSLI+iB9yKnv2RRtMyDJsug8aK+GG/4G7slvun0vquOmNPfh7u/OB2XiD0N7+5jECWoqYHRfN7LhQ02VkCqubOFRwml25lXy4J59Nh09x9XTHxsgmO58VLcCWvaEorCDLMg9/mkHayWpevj6J+JEBPb92V3RhUK9MD6XNIFPd2EawzsPh+E44kQNeUNVIZGAPFzAV4ummJX6kv6jInD9TvB9FaRDziz697rnG0I2BN1ZBxc8OwyffZpXRZpDRRqVC0X4hNeqA5Kigc6MD9+HVkLUR5j4Iw6coOiS3vJ4bXt+Dh5uGD2+bwYjADkY/fpl4zPik0zGSJDEq2IfLpozgiSXxjAnz5YO9+T2fd2EabH9KlMsrbNjw1g95rEkr5N55Y7h0soNWac7ihAcOPUwlNHvgChs59GX828xUfRA/FdXQOnwaIKlx8D5g6BpwszdtK//bxJaMEsL9PQmdNAfam0WqnAOSo8Qi3pDWRanOgy/uB/15MOs+RYcUVDVyw+t7kGWZD1bMIGpYl9vloChRUJW+1uY5JEni+hQ9hwtO92yhuLUB1t0GfsPhkmcUHfJ9djn//PwIF00K53cLxjp/TUf4hilWJAR6tpBZUwheAQ7z7JvbDJTVtfQ8hdAJkvRiwT/rtAbCJlri4AajTFldM0dO1fJ9djnrDhTy2vc5PPlFFs9s/RmDKtmsmKEbQsnfBVoPGJFkc0hTq4Ht2WVcPW0UGn3omeNGTbd76nERfug8tKSdrGbJVMcez6DDaIB1d4jnS1eCxnEsuqSmmRte30NDSzsf3T6TMWF+1gcmLBdVfKWZQp/GCsuSIvn3lqN8uCefJ5YmODf3rQ8JtbxbNoJ3oMPhueX13PPhAcaG+/Hs1YloelIu7widUklZc2/MHnrginLATTKywX3vgSdFif//Rzdmck9jDOeVfcPcx7dQ1miwqkjrppFoN8qcN3oY543pQZXoOcgQNuC7YXii3bzf77LLaG4zsmhSBPiFQHCsOG7WvXZPrdVITNUHDV1p2R+eFwJgS18VXrMDKupbuOH13VTWt/D+ihlMHGHHC5y4BDb/RXjhNgx4gI87l04ezoZDp3jwkgnKqyB/3iIKhs77raJYa01TGyve3Y+bVsOqm6f1vNrSEboQ0Y7MwSKeuRqzZx54gZMphH3vgQ8P8GZ2XAh5lQ2ku01gAZ9xQ2wD7aHxhPh5EuIr/oX6eRLi64GbRsPUx79kS2aJasAVMjRDKG1NUHTAYfx7S0YJQT7upMSY8pr1M4XhUtBNJDkqiJ9Laqnrj1zm/uTUIdj2pDC0CjRDTje2ctMbeyk63cSbt053nFrpGyoaKaSvtfv/fMMMPfUt7Ww6fErZvOvLYeM9onu6gmpLg1Hm3tUHya9s5JUbkpR32ekJ5mpMB8U8Xu5a/L3czsIDV7CA2Q9FPB1579cz2HH/PH7/q5sBuHdMJX+4cBw3z4zmkoThpMQEExOiw8/LHW8PLReMDWNrZona+Uohig24JElaSZIOSpL0mem1JEnSE5IkZUuSlCVJkn23tT85dRCMbXbj3y3tBr7JKmPhxPAz6VSjZoiMi8rjDi+RHBWEUYZDBUNI97i1UcSPdaFC68RBhk1dcxu3vLWPnLJ6XrtpGjNihym7TvxVUJMvuiHZIEkfxLhwPz5Uspgpy7Dxt0Jr5crXwM1x+t9Tm7P4Lrucx5fEK593T3GimCfc38v53pitDdBUrdgDd9NIZ1dd2hMCRokFVgf54IviIyitbeFQ4RD6XvUhznjg9wFZHV7fCowCxsuyPAH4qBfndXaYV7ttKRACP+ZUUtfSzsXxHTIOLGW/josOEvWBSNIQW8j8+lGoyIYl/3VYbdnUauDXb+8ns6iGl29I4hdjQ5VfZ/ylojovw/5i5nUpo/ipsIaMIgeLmQfegezNsOBRm2GZjqxNK2TVjhPcMjOK61L0yufdU3zNBlxBc2N/T+e701saOSjLAR8R6N0zadyzQZLEHXH+Lrt3XnPHh+GuldhqknZWsY8iAy5JUiRwKfB6h82/Af4hy7IRQJZlx5/O/iJ/tyg80dn2rLZmlODn6cZ5YzqMCYkTvRIVpDv5e7kzLtxv6BjwY1/D3tdgxm9g9Dy7Q1vaDdz+3n72n6ziuWsSnVdm9PIX+uAZ60TBjQ2WJkXi5a7hgz12vPDKHNjyV4iZI+bugLST1Ty4Lp1ZY4bxyOKJzs27p1g8cGULmU7HwM054IpSCBv7ZQHTKvqZUFcMp22/nwHe7pw3OoQtmSVqY2QzdpQflXrgzwP3Ax0VnEYD10iStF+SpM2SJMX1fIa9iNEo4th24t/tBiNfHill3oSwzqXSkmRq3Kqs7Dc5KoiD+acHf9pTQyVsuEtUrC541P7QlnZWvLOfHccq+PeyyVw2pYdC/QlXiUYHJ7bbHBLg7c7iySPYeKiI+hYrht7QJgS2tO6w5BWH1Zaltc3c8V4awwO9ePn6pD6tROyEMwbc35Oy2hbnjJelClNhI4c+LuKxifmO2IGDtCg+gpOVjRwtqeuHSQ1w6svhSdvfMYefYEmSFgNlsiynddnlCTTLsjwNWAW8aeP4201Gfn95ueMP8FlTflQ0d7VjwPflVVPV0CqyT7qiTxVpaArU45KjgqhvaSe7dBB/0GQZNt0rCp+ufM1utWVlfQvXr9rNjzmVPH3VZJZPO4tKyTELhcxB+id2h10/Q09Dq4GNh6wsZn7/jCi+WvycIg2Qv2/MpK65jddvnkagj5OVjmeDm6f4WxVVY3rRajBS0+TE4nhNoej56me/AKm5zUB5XUu/LWB2I1xIN1Ng34AvmBCOJJ3pkHVOU5Yp6lNsoMQFmQVcLklSHiLOPU+SpPeBQmCdacynwGRrB8uy/Josy9NkWZ4WGupEnLSnmL1nOwZ8S0YxXu4a5oyzMh8rjY5tMW0oFPQc+kDodM9/BIZbfQsBceu9fOUujpbU8eqNyWdnvEGkd068DLI2iawhG0wdFcj4CD8+3Huyy4T2w/dPi0wZc4WnHb49WsrmjBLunR9HXLiNHPW+RHE1prmxgxNhlJpC0dtVaz8N0pJC6KoQikYLo1IcfrdC/TyZHhXM1kzVgFN6xO5uhwZcluW/yrIcKctyNHAt8K0syzcC64G5pmFzgOyzm2kvkb9btJQKst5B3GiU2ZpZypyxofh4WPnAD5+iWP5yVLA3Ib6eg9eAV50QOdlR54tONTbILq1j2Ss/UmHK817QW92IEpZDax1kb7U5RJIkbpihJ6Oolp/MmQkt9SJbxn8EXPK0w8s0trbzyPpM4sJ8uW12bO/M3VkUV2Oae2M6sZCpOAdcpBCO6occcJvoZ0LZEZE1Y4eL4iM4WlJHXkUP1RmHCmWZ4GM7J/5sgoBPAcskSUoH/gWsOItz9R7m+LeNFLifimooqW223XnHzRNGJiuKg0uSRHJU4OA04Ga1PkkDS1+xWW2ZdrKK5SvF/8X/7pzJ9GjlWuAOiZ4tfmztZKMAXDF1JN7uWj40L2ZufVD8+CxdKcrHHfDCN8coOt3EE0sT8HBzUemDQg/cUk7vrAc+wIp4bGK+w7WTQgpw0SThJJzzXnjpEQi3vdju1KdZluXtsiwvNj0/LcvypbIsJ8iyPFOWZcciIn1NTZFY4baT//3DcZGLO2esnfZO+lShidLq+Nd/WlQw+VWNzqd+uZofnhN6MZf+BwKtp9J9e7SUG17fQ7DOg7V3nsf4iF7uZ6nRwqQrRb/KJtt5v/5e7lw2ZTgbD5+iMX2jSBucdS9En+/wEkdLanljxwmunhZ5pmDLFejCFK2rWHpjKv08GY1QW6S4iMddK1l+JFzCyGQh8evAQYoM8iFhZABbzmUDbjSKNb0w26mxQ6sSs8Bx/vfu3ErGR/jZl+vUzwTZIOKsDkgyNXg4MJi88KIDQq1v0pUijGGFT9IKue3dNOLC/Fhz58y+q1RMWA6GFhGHt8P1M6Lwaa1Cs+leCE+AuQ85PLXRKPPgunT8vd3568Vn2VnnbNGFis70dtImAXw83PDzdFPugTeUg6FVsQc+MtC7b/RelOLhI8KU+Y6lmxfFR3Aw/zQlNYPMOeotTucJCYbe8sAHPPm7wV0HEdYX49oMRvbnVZPqqPIucjognVE0tEP8SH883DSDJ4zS2ihS73zDYfGzVkNNr32fwx/XHCY1NpjVt6cS4tuHHtvIJLFe4aBf5pSR/vzX7000rfXICqstP9pXwIH80zx4yQSCnNXX7m3MxTyNlQ6Hhvl7Uq40F7zWuUYOLg2fmNHPFNrg7fb/xotMWWJfHjlHvXDzAmbYOWPAd0HkNJur8T8V1tDUZiA11sGttHdgJ/lLe3i6aZk8MmDwGPCvHoHKY6La0ruzboksy/zriyye/OIol04ezpu3Tse3rwSezEiSyAk/8T3UldoelvYWKW37+FfbtRxudZx7Xl7XwlObs0iNDWZZ0gBQjNQ5UY3pTHNjJ3LAi1xZxNMRfaq46zp1yO6wMWG+jAnzPXfTCctMBjx0vM0hQ8eAN9cKiVI78e/ducL7SYlRoH2hTxULLQ5ueUHkg2cU1dLcZrtiakCQ/SXse11knMRe0GlXu8HIn9f+xKvf53LzzChevHbq2feDVErCcpCNkPmp9f0Vx+HLh2mPvoD/aS/hwz0nrY/rwBOfH6GpzcA/lyQMjK5JzhbzKPXALQbc/o9UU6uBivrWgeGBjzKn6jp2kBZNimDPiSqqGhw3WhlylGZCUDR4+tocMnQMeOE+YQTs5H/vzq1kXLiD+LcZ/UxorRdpPA5Ijgqi1WB0rNnhShoqYMPdYkGki1pfm8HIne8fYG1aIb9fMJbHLp/Uv1oZoeMgIsF6GMXQJlIGtR64XfkKl02JZNPhYrsd7Xceq2D9oVP8Zs5oxoTZ/vD3KxZFQmWdeUprm5VVY9YUgocveNnXPi/sZxVCu/iGQvBoRam6i+IjMBhlvs6yfXc2ZCk7YncBE4aSAc/fLbp/R1rvKt9mMJJ2stpx+MSMEwU9SR061fclFfVOllibMfe2bD5tqrbsrET3xOdZfJ1VymOXT+K+BXGu8VgTlouqyqoTnbd/939w6gBc9gL4j+D6GXqa2gysP1hk9TTNbQYe2ZBB9DAf7po7ph8mrhCdKZdXYSphS7uR2mbHd3+WHHAH79mZFMIBYMDBJN28R2Ra2GHSCH9GBnqfe+JWbc1C58fOAiYMKQO+CyLiwdN6lV16UQ2NrQbHC5hmAs3yl44NeIivJ9HDfPq0wcP+vCpmPPkND3yS7rwRP/ieqdryb+L/qAMf78vn7R/zWHF+DLecF917E3aWSVeKx4454QV7YcczMOU6mLQEgMmRgcSP9OfDPflW/x/+uz2HExUN/HNJAl7u/RQCUoJXgOgQpUiRUPzAlitJJVScA272wAdACAWEg9RUJdZj7CBJEoviI9hxrMK6Hs5QpSJbZMLZWcCEoWLADW0i5U9R/NuJXGAF8pdmkqOCOXCyuk8U1FraDTywLh03jcTH+wv4z5dOFL1W5cLmB0TRTOrdnXalnazi4fUZzI4L4YGLbS+U9AuBo0T/TXOjh5Y6EToJiISL/6/T0OtTojhaUseB/M654znl9azcnsMViSM4P26AdXSRJFNrNcea4OY8bUXl9E4U8Xi4aQjty4wiZ3BCunlRfAStBiPbjg4cwdM+p8xxBgoMFQNe/BO0NzmIf1cxNtyXYc58gBXIX5pJjgqisqGVk5WNys+vkFe253C8rJ6VNyVz7fRR/L9tx3n7hxOODzS0i96WGjdTb8szb3dxTRN3vHeAEYHevHTd1P5T5rNHwlWicKE0U0jEVp8Ubd26NOq9PHEEOg8tqzs0e5BlmYc+TcfLXcPDl/aTTKyzKO6NaW6t5sADb2sS51OsQujiHPCODBstSsSVhCj1QYT4ep5bRT2lmeKObdhou8MGwLe2F7AU8Fg34G0GI2l5VcrDJ2aciIMnm+LgvR1GOV5Wx3+3Ca9y7rgw/rkknoUTw3nssyN89pODdmM7n4XCvSLfu8OXvLnNwB3vpdHU2s6q/lbms8fEJeLHZtN9Iuxz/u8g6rxuw3w93bhi6kg+++mURbVv3YEidudW8ZeLxxPqykpDe+hCFVZjihCKQw+81vT+K8gBL6huZORAiX9D5wYPDtBqJC6cFM62o2UDP9Ortyg7InoaaN3tDhsaBjx/l0i38bcup5lRVEODM/FvM2ETwdNf0YcsLswXPy835xYyy3+GvB9s7jYaZR74JB0fT62l+YCbVsNL101lWlQQv//4kEUaoBtFaaLaMmG58GxNyLLMX9el81NhDc9dk8hYVyjz2UI3TDSTKNovirEueNDm0OtT9DS3Gfn0QCHVDa088UUWSfpArpveDx12eorCEIqvpxs6D63jakxzIwelHvhAiX+b0adCdR7UOfasF02KoLHVwM5jjv//hgQONFDMDH4DLsvCQ7bhfYMIn4CT8W8QWh2xc0R6W9fsiK5DNRJJ+iDlJfUNlfDOZfDuFUJ3xQof7s1n/8lqHrpkQqdqSC93La/fPJ2YEB13vJfWPX2xtQE+uU3oQ1/yTKddb+w8wacHi/jDwrFcaE0P3dUk/xJ8hsGy18HN9p1B/MgAJkcG8OHefJ7afJSapjaeWJowcEIE1vANFYuYCtZJwv29HIdQLK3U7BvwhpZ2qhpaB04GihlLHNzxHW5q7DD8vdzOjTBKUzXUnXIY/4ahYMCrckUc0EH+d1yYb89Kwi96Uij2fXqn3dZGIMIo2WV1jsX4zU0UmqpF1ee627tpYpfWNvPvzUeZNWYYVyV3/4IG+Ljz7q9mEODtzq1v7eNkZQfhrS8fFv8vS18R5zfxfXY5T36RxcXxEdwzkFLsOjL+EvhzjsgNd8D1KXqyS+v5eH8BK86PYcLwXhbb6m10oUK3pKXW4dBQP08FHnghIAktcDsUnRafrT7Ts+kpEZPBzVuRAfdw07BgQjhfZ5XSZrCfejjoMZfQK+jvOvgNuKWBg/UMlHaDkf09iX+bCdQLL7ZgN+x8zu7QadFByLLwcu1y8H2R1jfvEbFIV34Uvv57pyGPbsik1WDkCTuVhBEBXrzzqxTajUZufnOv0M/I3gr734Tz7oGYX1jG5lU0cM+HBxgb7sczy6cMbE9VYR76ZVNG4OvpxshAb+5bMDA6+tnFiWKeMEUeeAH4Rdi9U4EBVsTTETcPUbehsIXhRfERnG5sY++Jqj6emItRmIEC/WzA+6R3ZP5uoekRMtbq7oxTtT2Lf3dk8tUiT3n7v+DUQZvDUmOGsXTqSF785hhPbz1qPaWw6gRsMaX1zbwHxsyHGXfCnpVw/BtAaCBvySzhdwvGEh2iszu1MWG+vHnrdEprm/ndm19iXH83hMd3qrasa25jxbv70WgkVt08DV1f65v0EzpPN968dTpv/XK69eYcAw0ninnC/TwpddQbs6ZQUSPjgqoBVsTTEX0qlKSLJh0O+EVcKN7u2qGvjVJ2RLTg83es+dOvBvzn0jre+uEEre29eAtkjn/baGjbo/zvrkiSyOTQhYnYcqv1VEGNRuI/y6dwXYqel7fl8I/PjnT+AlqaKGg7p/Ut+LsQrFl/F3XVpfxtQwYThvuzYrb1rkJdSdIH8cr1Sdxa+RztjadpvWKlRa3PaJT5/ceHOVHRwH+vTxp4t9FnSUpM8MBaiLWHr8kDV6iH0tRmsF+84kQRj+dAygHvyKhUUbBS5Fi62dtDywXjQtmaWYJxsDcSt4d5AVPBnWi/GnBvdy2PbTrChc99x5aM4rMvemmoEJVcevv632PCfM8+tcw7SMSUK4/BV3+zOUyjkXhyaTy/mhXDWz/k8dd16WfuPHZ2aKLQ8Yvn7i1K3BsrOfn27ZTXNfPUlQm4O5GbPbdxMws1afy77Rr+sL3N8gF/7utsvs4q5ZFLJ3DemAFW3HKu4YQiYbi/ubWajTi4LDtVxDMyyHtgiHp1ZZRJullBHBxEUU9ZXQsHC2w3ABnUyDKUZSkKn0A/G/CYEB1v/XI67loNd75/gKtf3cWhs3kjzG+63fi3E/onjoi9QFQz7lsFx76yOUySJB5ZPIF75o7ho30F/OF/h2gv2A/fPQXxV8FkK00Uhk+hcOofiK/ZzrPjspgyyr44UScqc2DLgxAzh9CFv+Ozn4r5x2dH+PynYl769jhXT4t0bZm8isDHFMZTkEoYaqnGtBEHb6wSxWsKdcBd2gfTHl4BIuSnMA4+d3wY7lpp6LZaqymElhpFKYTggkXMuePC2HzfbJ5cmsCJigaWvPwDv119kIKqHlQw5u8CrSeMmGp1d+apWupb2s8u/t2V+X8Tv44b7rb7RZQkiT9dNI4/XzSOLw/lUvHuLci6MLj0GavjW9oN/Co7lUPSRK449bzIj1WCoV1ksWjdYMkr3DFnDL8+P4a3f8zj3o8OkqQP5PEl8QPT+zrX0LqDd7CyYh4/sx6KDQ/ciRzwgurGgRn/NqNPhYJ9iqSb/b3cmTUmhC0ZJX0iW+FyLAuYjjNQwEVZKG5aDdfP0LP9z3P57bwxfHWkhPn/+Y5/fZHlOAWvIwV7REcXG91ZeiX+3RV3LxHuaKoWFYMOPkR3zx3DujGbiWgr5N/ev6PZzXqq28rtuWSXN9G0+GUkJEVpi4AQeyraD4ufg4CRSJLEQ5dMYHlyJJFB3qy8Mbn/dL1VHKOwnD7c34EHrrCRQ11zG6cb2wZeEU9H9KnQ1gCl6YqGL5oUQX5VI1nFdX08MRdQapKvDlPWAtClaYS+nm788cJxbPvTBVyeOILXduQy5+ltyhY6WxtFRw8H+d+jQ3UWb6bXiEgQWR5HPxMpgfbI/pIJhWs4GnMLrxZEcutbe7stTB0vq+Plbce5fMoIZiYnCS89fxf88Lz9cxfuF3Krk6+B+GWWzRqNxNPLp7DtjxdYyrJVBgi+YYoMuK+nG97udqoxa5S1UjPngA94DxwU9ckEWDAxHI3E0CzqKcsSmUXeykKoAyIPfHiAN88sn8Kme85n4nB/Htt0hEte3GHfGz91AIxtNisw25X2v+wpM+8RqYBbHhBFM9bo0ERh/A1P8/w1iezLq+amN/ZQ0yj+NqNRlLb7eGr522WmuNfka4QuyLYnbbedaqkXan3+I+CSp60OGdC53ucquhBFBlySJMLtdeapLRRFMD727y4LB3IKoZmASPFDpDAOHuLryfTo4KGpEV52RPECJgwQA24mfmQAH6yYwcobkzheVs9b9hT3zG/2qBSru48U11LX2/Hvjmg0sOQVkRK47o7u8TtZho33iiYKy1aBmydXJI7k5euTyCiq4bpVu6msb2H1vnz25XUpl5ckERLRhQojbS1t8cuHRE750pViIUhlcKBT5oGDg96Y5gwUh40cxGdnwKeP6lNFUoLCuPai+Ah+Lq3jREWD48GDBUOb0EdSuIAJA8yAg1nAfTgLJ4bz5s4Ttltn5e+G0Ak2PRBz/HtGb2WgWCNwlEgJLNzbvUrz4Hvw8+cw/9FOJbGL4iNYdfM0csrruea13Ty1+SjnjbZSLu8TLH4gKrLh60c77/t5M6S9DbPuhejz++ZvU+kbdKHQXOOwIztAqL3u9ApTCAuqm/By1zBMSRtBV6JPhfoSxYv35o71mzOK+3BS/UzlcRFVULiACU4YcEmStJIkHZQk6bMu21+UJMlxGZWT3Dc/jtrmdt7+Ia/7TqNBrFo70P+O7Yv4d1cmLxfx5+3/EgqAINL6Nj8gStlT7+p2yAXjwnjnVykUn26itd3Ik0ttlMuPngszfgN7X4NjX4tt9WWw4R4IT4C5D/XhH6bSJ1iqMR2nEoY79MAdV2EWVjcSGeQz8LOQnBC2AhgR6E1KTDBv7sxzLvFhIGNewOwjD/w+IKvjBkmSpgFBTpxDMfEjA1gwIYw3dp6grqsXXpYlciVt5H8bjDL7TpyF/omzXPofoUmx7nbhXX16hyWtz1aFaGrsMDbcM4sPb0u1Xy6/4FFxp7HhLvGl3/hb0a3GFJZRGWRYqjGV6IJ70tBqoKFrNWZ7q5BgVZgDPqDj32ZCJ4jy8QJlBhzgb4snUtXQwrNf/tyHE+tHyo6IkKwNWRBrKDLgkiRFApcCr3fYpgWeBu5XfDXZuRL6++aPpaapjXd+zOu8wyJgZb0C88ipPo5/d8U7SBjryuPw6i+gcB9c+qzDW9wxYX6WRhA2cfcWxrqxCl6fD9lbYOFjitOMVAYYlmpM5a3Vui1k1p0CZCd0wAeBAddoxHqWQg8chJN388xo3tt9kvTCWyjpOQAAH6hJREFUGscHDHRKj0BInFOOmVIP/HmEoe5oge8BNsqyrDwIpUC4vSMJkQHMGx/G6ztPdE69y98ttK4Do6weZ45/p/Zm/rcjYueIzJTqPEi4ulMThbMmIgHmPyLOHTsXUu7ovXOr9C9mA66gmCfc0pmnSxhFYQ54bXMbNU1tA7cKsyv6VKHMqUCt0cwfLhxLsM6Th9en941YXn9SdsRpx8yhAZckaTFQJstyWodtI4DlwEsKjr9dkqT9kiTtNzZUgtE5L/ze+XGcbmzj3V15ZzYW7BFvto243u7cSmJDdP2fAz3/b8ITX/xs75975j1CenbZGzbDMiqDAIsH7kxvzC4euMIc8DMphIPEgI9dJB6PrFd8iL+XO48snsDhwho+2ue4d+2ApaUOTp90agETlHngs4DLJUnKAz4C5gGZwBjguGm7jyRJx60dLMvya7IsT5NleZpGbhcZG06QOCqQOWNDWfV9rogFni4QZcR24t9786qY0V/hk464eULi9eDZB+p4Gi1MuVa0HVMZvHj6gruPQkVCk6BVNw/cVEbvQG50wOqA2yJ8koiFZ3zi1GGXTxnBzNhh/HvzUSrqHWf3DEjKjopHJxYwQYEBl2X5r7IsR8qyHA1cC3wry3KQLMsRsixHm7Y3yrLsuMWLpBHtyZzkvgVxVDe28d7uk8L7BpsZKFnFtdQ1t/eegJWKSm+jsJjH38sNTzeNdQ9cFyrWR+xQWD0Iing6Ikki9Ji/C04r96YlSeLxJfE0tRn41xdH+3CCfUiZuYS+lw14r+IVAJmfioR1J0jSBzE7LoRV3+fSduIH8PC1eathiX+7wgNXUVGCQj0USZII8/e04oErl5H1dtcSPNBzwDtiloRw0gsfE+bL7b+I5ZMDhewx2YBBRekRcNfZXNezhVMGXJbl7bIsL7ay3VfRCbyDoLEScr9z5rIA/G5BHJUNrdRm74DI6SJNzwq7cyuJCdFZFoBUVAYcujDFC3UiF7yrB16kuJHDqOABqgNui+AY8f1Od86AA9wzN46Rgd48siFj8PXNNC9gOrm+1b8euKe/8MJ7EEZJjgpmYaw3QfXHaRtpPX3QYJTZe6JKDZ+oDGwUhlBA5IJ36o0pyyIGriAHvKC6afAsYHYkYblQJizLcjy2A94eWh67fBLZpQ5kOAYasiyKeHqQGty/BlySYMLlQsXPRlsye/xpYg0aZL6st95qLKu4ltrmfsz/VlHpCWZFQgUZWWF+Xp0VCZtroLVeUS/MwoGuA26LSUtN62VrnT50wcRwFkwI5/mvj3HKpMQ44Kkvg6YqRV3ou9L/+WgJy8UH8NhWpw8d15KBAQ1PpfvS3NZdK9uifxKjGnCVAYwuVPSBbHbcjSrM35O6lnaaWk2fd4U54DVNbdQ1tw9OA+4bBjFzIGOtYnGrjjx62USMssw/Nh3pg8n1AT1cwARXGPDo88E3oke/ruTvpmnYJArqJT7c032VenduFdHDfIgIUOPfKgMYp3LBzb0xTWEUpTnglhTCQRhCAeHoVeed0RdyglHBPvx2XhxbMkvYdtRxwZTLKTX90FjxwJvbDFy/ynZ1av8bcI0W4q+EY1+KrjZKaW+Fov34xs1mRkwwK7/L6eSFG40y+/L6Uf9ERaWnOFWNae7MYwqjKGylVmAq4hk0VZhdmbBYtEvswXoZwG2zYxkdquPRjZlW79YHFGVHxMK2rnvT8f/tL+DHHNtZNa4p6Uu4CgytkLVJ+TElP0F7M+hTuW9BHGV1LXy8r8CyO6uklpqmNtWAqwx8ztYD13qcOYcNBl0RT1e8AmDshZCxTlGvzK54uGl4/Ip48qsa+e/2nD6YYC9Smmm1gKfNYOTV73Lt6iW5xoCPSILgWOfCKJYGDqnMjB1GSnQwr2zPoaVd/Lruzq0C+lj/W0WlN7AoEjrTG9PsgReKBUwH6WaF1U3oPLQE+rif1VRdSsJyodqYt6NHh583JoQliSNYuT2H3PJeV7zuHYwGof9ipa5l/cEiik43cc9c2zWSrjHgkiTenBPfKxe4yt8tjL5fOJIkce/8OEpqm/mfyQvfnVtJ1DAfhgcMUo9D5dzBO0hkWSgw4AHe7ni4aTp74IpVCAeBDrg94i4Uqcc9WS8z8eClE/B00/C3DZkDs4t9dZ6ILHRJITQYZV75LoeJw/25YJztuy3XqSLFXwXI4hbJEbIsPPAO/S9njRlGclQQ/90uYuF7T1SRqmafqAwGNFrwCVEUA5ckiVBfT8o7euCKdMBFEc+gxt0bJlwGWRuhzUZjCweE+Xnxp4vGsfN4BZ+nD8DuPTaaOGzJKCG3vIG7546x+yPsOgMeOhYiJotUIUdUHhcVnB30TyRJ4r75cRTXNPP4Z0dE/Hu0Gj5RGSToQhVpgoMIo5TWNYtYcF2xQw9clmWLBz7oiV8GLbVw/Ksen+LG1CgmjfDnH5uOdJalHgiUHQEkIeJlQpZlXt52nNhQHYviI+we7lpd0oTlIk2o0sEig1nkvYsC4ey4EKbqA/nAlFKo5n+rDBp8QxV15YEOxTz1JSJ/XEEOeH3LIM0B70rMHPFj18NsFACtRuIfV8RTVtfC+7tP9uLkeoHSTCEf4HHmx3Z7djlHimv5zZzRaDX2Q2CuNeDxV4pHR8I1+bvBO1h0q+iA2QsH0Af7MCJwCHxgVc4NwiZB0QEo3O9waLi/p2jqYMkBt1+FOehUCO2hdYNJV8LPW6C5tsenSY4Sgniv7zgxsNIKy450KuCRZZmXvz3OyEBvlkx1XG3rWgMeEAlRs8Svq70Fhvxdwvu2EguaMzaUBRPCWargj1VRGTDMuV/oea+7HVob7A4N8/eitrmd1ipT8dpQL+LpSsJVYGgREhxnwd1zx1BR3zn92KW0NUFVbqcCnr0nqth/sprbfxGLu9axeXZ9a5eEq6AiG0rSre+vL4OqHJv9LyVJ4vVbpvH7hcobgaqouBzvQFi6UnyBtz5kd2ioqTNPY1me2GBHB0WWZXLKxQ/CoC3i6UrkdAjUn1U2CsCMmGCmRQXx6nc5tLYPALXC8qOiT3CHDJSXt+cQ4uvBNdMdL1QDWNdk7U8mLoEv/iy88OGTu++3Ef9WURn0RJ8Ps+6FH16AsRfBuIutDjNLI7dWnUT2DuJ0uweF5TUUVDdSUNVIYXUTBdXisbC6keY2I4E+7vh7u/7r3SuY0453Pi8cOnMevdOnkbh73hh++dY+1h8s4mqFRrLPMKstmnLAfyo8zffZ5fxl0Xi83LWKTuH6d9gnGEbPF3HwBY91L1Ao2ANuXjB8imvmp6LSl8x9CI5/Cxvugbt2WTVO5t6Y2dlHqTQEcPHjnTMy/L3ciAzyYXSojgvGhhIZ5M206ODBnQPelYTlsOM/kLkeZtze49NcMDaU+JH+vPJdDsuSIx0uEvYppZlCLiA4FoD/bsvBz8uNG1P1ik/hegMO4s05thUKdkPUeZ335e+Ckcmi36SKylDDzROWrYJX58DG38J1H3Vb64kN1TF/fBhRRVU0+EXy8LQJRAb5MCrYm8ggHwK8B3G1pVLCJghPNWPtWRlwSZK4+4Ix/OaDA3yeXszlU+z3Fe1Tyo5A6DjQunGstI4tmSX8dt4Y/LyUv5+uj4GDuHV08+6eKtTaAMWHYZT1+LeKypAgbAIsfAyyt0Da2912e7ppeePW6YzSVjJ+3ERWzI5lUXwEk0YEnBvG20zCVeKOvDrvrE5z0aQIRofq+O+24xiNLqzOLD1iWcB8ZXsO3u5afjnLeq8DWwwMA+7pC+MvEbdHHftlFqWBsV2Nf6sMfVLugNgLYOuDUHG8+/6WOtHMQUEZ/ZClh/0yu6LRSNz1/9u78+iqynOP498nCUOQGZmnMCg0EkBFUdSKU51QUEChtddbtdSxanVV6+2t93b13qWrLocOilhbqcUJwVkcrkKxDmhkRkAQkEEgDMosQ/LcP/aOREzICWefc7JPfp+1srLPPvu8+yFwnry8533fZ3BPFq3bxluZ2m525+ZgXn+bQlZt3skLc77ghwO71Lh+ae1I4BAMo+zaDJ9N3X9u5QeAQefjMhaWSFrk5MCwh4KdBp8b893C31vWBN/rcgJv0TXYTiPJ2SgAF/bvQKcW+fxp6tLM7JFSYQn92H9+Rq4ZPz2le42bqT0JvMcZ0LD5t4dRVn4QTHLPr3o7RZGs0bQDXHB/8D/P6b//9nMJFnLIekUjgrHj8gR4iOrl5nD1qT2Ys+or3l2agSr2JUERh435PZhYvJrhx3Y6pEI0tSeB59WHwqGw6JVg7LusFFZ9+K39T0Sy3lEXQb/RMP0eWPXR/vMJFnLIeoXDwHIj6YWPOLYTbZo04M9TKxmySrWST6Bhc8bN3sW+sjKuPrXmvW+oTQkcgmGUvTtg8ZTgN+yebUrgUvece3ewWGfyT2F3uI/1ltWQkweN22Y2tkxr3Bp6nHbI9TIralgvlzHf7877yzbx8ec1qA4WhfWfsPfw7/GPGSu5oF8HurY67JCaqV0JvOsgaNIh+JDimwU8SuBSxzRsBhc/HMy2eP1Xwbktq4P3Rk5iCzyyWp8R8NVKWP1R9ddW44cDu9CiUb309sLdoWQhC/Z1YueeUq4dXHXBhuoknMDNLNfMZpnZy+HjCWa22Mzmm9lfzSz5+Uzf1Mt8Ez6dEvRC6vqYn9RNXQfByTfBzL8Hw4oJFnKoE3qfHyzuS2KHwnKN6udxxUndeHtRCQu+2BJBcAn4aiXs2cZL61pwVmFberVrcshN1aQHfiOwsMLjCUBvoAjIB6465CgqKhoBZXvhs7eD3nc2rSYTqYnBdwR75r94A2xcrARermFTOPKcQ66XeaB/G1RAkwZ5PDg1TbUzww8wZ+3uwLWDeyTVVEIJ3Mw6AecDfyk/5+6vegj4EIjmX1f7/tAq/C+F5n9LXZZXHy5+JPhQf8cGJfCKikbCzo2wfFrSTTXLr8ePT+zKq/PXsrQk9bUz966dD0DLbn05uktyM+wS7YHfD/wS+M4WXuHQyY+B15KKZH+DwV8OaPxbpE1vOOu3wXHzxPfIyHpHnAUNmkUyGwXgypO70SAvh4fSUMH+i4UfsNoP54ozkt/fqdoEbmZDgBJ3/7iKSx4Eprt7paWjzWyMmRWbWfGGDdUXcQVg0A1wyePQriix60Wy2fFjYOT4YHhRAnkNoPACWPhysK92klo1bsDo47vw/Ow1rNq8M4IAK7d2w0barH+HhY2O58TuyVcQS6QHfhJwoZmtAJ4CTjezfwCY2Z1Aa+AXVb3Y3ce5+wB3H9C6ddXVlb+l/mFQeGFi14pkOzM4ahg0OPQPu7JS0chgqvGnr0fS3JjvdyfH4OHpqemFl5Y5z0wYRz67KTr3qkh2i6w2gbv7r9y9k7sXAKOAt939MjO7CjgbGO3utWB3dBGpUwpOCebFJ1IYPQHtm+Uz4thOPFO8mpKtX0fSZkXjpi/jqE1vsLNhW9r1OS2SNpOZBz4WaAu8b2azzew3kUQkIpKInNygXuanb8CuryJp8upTe7CvtIxH3lkWSXvl5q3ewqNvFHNa7lzyj77ku3UPDlGNWnH3ae4+JDzOc/ce7t4//PptJBGJiCSqaGQk9TLLdW11GBf268CEGSv5cseeSNrcuWcfNz49i+H5M8mlFOs7MpJ2obatxBQRqYmOx0CLbpEs6il3zeCe7NxTyth/RjMW/rtXFrJ84w6uPXwWHH5kMLc/IkrgIhJfZsHsnOXTYdv6SJrs1a4JFx/TkYenL+PeNz9NarvZNxas44kZK7ll4GE0W/9hsA1AhIsTlcBFJN6KRgbV3Rc8F1mTdw/vyyUDOvGHt5Zw+6R57Cut+TyNkq1fc9ukuRzVoSlXt5oDeORTQZXARSTeWvcK1oxEOIxSLzeHu4f35YbTe/J08SrGPP4xO/ckvmy/rMy5ZeIcdu0t5YFR/cn7ZBJ0OAZaJbd0/kBK4CISf31GwJpi2Lw8sibNjFt+0IvfDevDtMUl/PCRGWzavjuh1z723greWbKRX59fSE9bG9T2LYruw8tySuAiEn/f1MuMZk54RZed0JWHLjuWhWu3MmLs+9Wu1Fy4dit3TVnEmd9rw48GdgljsqBYR8SUwEUk/pp3hi6DYO7EpAs9VObso9ox4aqBbN6xh4sefI/5ayrfevbrvaXc9NRsmubX4+7hfTEI9mvpdgo0bR95XErgIpIdioYH2+6un5+S5gcUtGTSNSfSIC+HSx9+n3eWfHdvp7umLGLx+m3cM7IvrRo3gC9mwebPUjJ8AkrgIpItCi8Kys5FtENhZXq2acLkawfRuWUjfvK3j3hu1upvnpu2uITH3lvBvw8qYHCvNsHJec9Cbn343gUpiUcJXESyw2GtoMfpQUnGstRtz9S2aUOeufpEjitoyc1Pz2HsPz9j0/bd3DpxLr3aNuH2c3sHF5aVwoLJ0PMsyE9u3++qKIGLSPboMwK2rIJVM1J6m6YN6/HYFccxpG977pqyiCF//Bdbv97LA6P707BeWLf083dh29qUbgOsBC4i2aP3eZCXn5LZKAdqkJfLH0YdzZUnd2Ptlq+5/Zze9G7XdP8F8yZC/cZB+bcUyUtZyyIi6dagCfQ6N1iVec5dkJt8rfWDyckx/nNIIVec3I2OzfP3P7FvN3zyYlCAuX6j1N0/ZS2LiGRC0UjYuQmWTUvbLb+VvAGWvgVff5Wy2SfllMBFJLv0PAMaNot0aX2NzZsIjVpB98EpvY0SuIhkl7wGUDgUFr0Ce1JX37JKu7fD4inBystUD+GktHURkUwoGgl7tsOnr6X/3otfhX27ghkxKaYELiLZp+tJ0LhdShf1VGneRGjWGToPTPmtlMBFJPvk5AYbXC19E3Z9mb777tgEn70d3DuiupcHowQuItmpaASU7oGFL6Xvnp88D2X7Uj77pJwSuIhkpw5HQ8vu6Z2NMu9ZaN0b2h6VltspgYtIdjILesLL34Gta1N/v69Wwcr3gp5/hHUvD0YJXESyV58RgEdaL7NKCyZXuGd6KIGLSPZqfSS075eeYZR5E6HjAGjZLfX3CiWcwM0s18xmmdnL4eNuZjbDzJaa2dNmVj91YYqIHKI+I+CLmbDps9TdY8NiWDcvbR9elqtJD/xGYGGFx3cD97l7T+BL4MooAxMRiUSf4YAF+4SnyrxnwXJSUvfyYBJK4GbWCTgf+Ev42IDTgfJZ8uOBYakIUEQkKc06Bgt75j6TknqZuAfDJ91OhSZto2//IBLtgd8P/BIoL3PRCvjK3feFj1cDHSOOTUQkGkUjYNOSYJfAqH36Ony5PKWFG6pSbQI3syFAibt/fCg3MLMxZlZsZsUbNny3CKiISMr1GwWH94IXr4edm6Nrd8dGePEGaHNUWmeflEukB34ScKGZrQCeIhg6eQBobmblBSE6AWsqe7G7j3P3Ae4+oHXr1hGELCJSQ/Xy4eJxsGMDvHxzNEMp7vDSjcG+38MfgXoNk2+zhqpN4O7+K3fv5O4FwCjgbXf/ETAVKP+VcznwQsqiFBFJVof+cNodwXL3uU8n396sf8Cil+GM36Rt5eWBkpkHfhvwCzNbSjAm/mg0IYmIpMhJN0GXE+GVW+HLzw+9nc3LYMptUHAKnHBddPHVUI0SuLtPc/ch4fEydz/e3Xu6+0h3352aEEVEIpKTCxc9HBw/dzWUlda8jdJ9MPlnkJsHF41Ny66DVdFKTBGpW1p0hfN+H+xb8u4DNX/9v+6F1R/C+fdCs07Rx1cDSuAiUvf0GxWUXZv6v/DF7MRft/pjmHZXsOIyA9MGD6QELiJ1jxkMuT8oPDx5DOzdVf1r9uyAyT+FJu3hvHtSH2MClMBFpG5q1BKGPQgbF8Obd1Z//Ru/Dj68vOghyG+e+vgSoAQuInVXzzNg4NXw4cOw9P+qvu7T16H4rzDoeuj2/fTFVw0lcBGp2878r6CKzvPXVb5Kc/sGeOE6aNsHTv/PdEd3UErgIlK3la/S3LkJXvr5t1dpugfnvt4aXJPXIHNxVkIJXESkfT84/T+CAsizn9h/fuZ4WPwqnHlnxlZbHowSuIgIwKCfB9vOTrkNvlwRFIB47Y5gm9iB12Q6ukrlVX+JiEgdkJMbrKx86KRgpWXZvmC15bCHMrra8mBqZ1QiIpnQvEuwSnPVB7CmGIbcFxSEqKXUAxcRqajvpUF9y5y8sBxb7aUELiJSkRmc/T+ZjiIhGkIREYkpJXARkZhSAhcRiSklcBGRmFICFxGJKSVwEZGYUgIXEYkpJXARkZgyr7h1YqpvZrYNWJzg5c2ALTVovibXp7Lt2hSL2k5v27UpFrWd3rZTHUsvd2/ynbPunrYvoLgG146rYdsJX5/KtmtTLGpbf/dqO2v+7ivNnbV5COWlFF6fyrZrer3azp62a3q92s6etmt6fU3brlS6h1CK3X1A2m4oIpIFqsqd6e6Bj0vz/UREskGluTOtPXAREYlObR4Dr5KZnWNmi81sqZndHp571MzmmNlcM3vWzBpnOs7azMz+amYlZja/wrmWZvammS0Jv7fIZIy1WRU/v6fNbHb4tcLMZmcyxtrOzDqb2VQz+8TMFpjZjQc8f4uZuZkdnqkYa7vYJXAzywX+DJwLFAKjzawQuNnd+7l7X2AlcH0Gw4yDx4BzDjh3O/CWux8BvBU+lso9xgE/P3e/1N37u3t/YBIwOROBxcg+4BZ3LwROAK4L38uYWWfgBwTvZalC7BI4cDyw1N2Xufse4ClgqLtvBTAzA/IBjQ0dhLtPBzYfcHooMD48Hg8MS2tQMVLFzw/45t/gJcCTaQ0qZtx9rbvPDI+3AQuB8vpl9wG/RO/jg4pjAu8IrKrweHV4DjP7G7AO6A38Mf2hxV5bd18bHq8D2mYymBg7BVjv7ksyHUhcmFkBcDQww8yGAmvcfU5Gg4qBOCbwKrn7T4AOBL/JL81wOLHmwafb6v0cmtGo952w8POqScBNBMMqdwC/yWhQMRHHBL4G6FzhcafwHADuXkowrFK7q5HWTuvNrD1A+L0kw/HEjpnlARcDT2c6ljgws3oEyXuCu08GegDdgDlmtoLg/T3TzNplLsraK44J/CPgCDPrZmb1gVHAi2bWE74Zf7wQWJTBGOPqReDy8Phy4IUMxhJXZwKL3H11pgOp7cL36qPAQne/F8Dd57l7G3cvcPcCgiHSY9x9XQZDrbVil8DdfR/BDJPXCYZKngm/jzezecA8oD3w24wFGQNm9iTwPtDLzFab2ZXAXcBZZraEIBHdlckYa7Mqfn4QdCg0fJKYk4AfA6dXmH55XqaDihMt5BERianY9cBFRCSgBC4iElNK4CIiMaUELiISU0rgIiIxpQQuIhJTSuAiIjGlBC4iElNK4CIiMaUELiISU0rgIiIxpQQuIhJTSuAiIjGlBC4iElNK4CIiMaUELiISU3lRNWRmpQTVcOoRFCb9O3Cfu5dFdQ8REdkvsgQO7HL3/gBm1gZ4AmgK3BnhPUREJJSSIRR3LwHGANdbINfMfm9mH5nZXDP7Wfm1Znabmc0zszlmphqMIiIJirIH/i3uvszMcoE2wFBgi7sfZ2YNgHfN7A2gd/jcQHffaWYtUxWPiEi2SVkCP8APgL5mNiJ83Aw4gqDy+d/cfSeAu29OUzwiIrGXsgRuZt2BUqAEMOAGd3/9gGvOTtX9RUSyXUrGwM2sNTAW+JO7O/A6cI2Z1QufP9LMDgPeBH5iZo3C8xpCERFJUJQ98Hwzm83+aYSPA/eGz/0FKABmmpkBG4Bh7v6amfUHis1sD/AqcEeEMYmIZC0LOsgiIhI3WokpIhJTSuAiIjGVVAI3s85mNtXMPjGzBWZ2Y3i+pZm9aWZLwu8twvO9zex9M9ttZrce0NbNYRvzzexJM2uYTGwiItku2R74PuAWdy8ETgCuM7NC4HbgLXc/AngrfAywGfg5cE/FRsysY3h+gLv3AXKBUUnGJiKS1ZJK4O6+1t1nhsfbgIVAR4LVlePDy8YDw8JrStz9I2BvJc3lEcxkyQMaAV8kE5uISLaLbAzczAqAo4EZQFt3Xxs+tQ5oe7DXuvsagl75SmAtwbL7N6KKTUQkG0WSwM2sMTAJuMndt1Z8LlzIc9C5iuEY+VCgG9ABOMzMLosiNhGRbJV0Ag9XV04CJrj75PD0ejNrHz7fnmA5/cGcCSx39w3uvheYDAxKNjYRkWyW7CwUAx4FFrr7vRWeehG4PDy+HHihmqZWAieYWaOwzTMIxtNFRKQKSa3ENLOTgXcIKvGUV965g2Ac/BmgC/A5cIm7bzazdkAxQaGHMmA7UOjuW83sv4FLCWa2zAKucvfdhxyciEiW01J6EZGY0kpMEZGYUgIXEYkpJXARkZhSAhcRiSklcBGRmFICl6xkZqVmNjvc4XKOmd1iZjkHXPO8mX0QHp8dXj/bzLab2eLw+O9mNtjMtlR4fraZnZmZP5nIfppGKFnJzLa7e+PwuA3wBPCuu98ZnmtOsH5hO3C+uy+r8NppwK3uXhw+Hhw+HpLWP4RINdQDl6zn7iXAGOD6cKUvwMXAS8BTaOtiiSklcKkTwh52LtAmPDUaeDL8Gp1AE6ccMITSI0WhiiQsyqr0IrFgZm2BI4B/ubub2V4z6+Pu8w/ysnc0hCK1jXrgUieYWXeglGBnzEuAFsByM1sBFJBYL1ykVlECl6xnZq2BscCfwv3pRwPnuHuBuxcAx6JxcIkhDaFItso3s9lAPYIdLh8H7g0rR3UFPii/0N2Xh9MEB7r7jCraOyVsr9zv3P3Z1IQukhhNIxQRiSkNoYiIxJQSuIhITCmBi4jElBK4iEhMKYGLiMSUEriISEwpgYuIxJQSuIhITP0/ZgCmaRJfhBcAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light", "tags": []}, "output_type": "display_data"}], "source": ["pred.plot(legend='ARIMA Predictions')\n", "test['AvgTemp'].plot(legend=True)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 33}, "colab_type": "code", "id": "3R2eTZHDfz5H", "outputId": "8dcb8e41-f96d-4adf-9784-21342aacb1a4"}, "outputs": [{"data": {"text/plain": ["45.0"]}, "execution_count": 15, "metadata": {"tags": []}, "output_type": "execute_result"}], "source": ["test['AvgTemp'].mean()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 33}, "colab_type": "code", "id": "zQ7SB2lQux5x", "outputId": "740e2eeb-e2b0-42ac-ec76-58da663146e9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.326343714317439\n"]}], "source": ["from sklearn.metrics import mean_squared_error\n", "from math import sqrt\n", "rmse=sqrt(mean_squared_error(pred,test['AvgTemp']))\n", "print(rmse)\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 226}, "colab_type": "code", "id": "DgJUq_lGvV10", "outputId": "c4087b3d-d92b-44ce-b116-fa1c7ffd3b47"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>AvgTemp</th>\n", "      <th>Sunrise</th>\n", "      <th>Sunset</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DATE</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2018-12-26</th>\n", "      <td>35.0</td>\n", "      <td>45.0</td>\n", "      <td>40.0</td>\n", "      <td>654</td>\n", "      <td>1752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-12-27</th>\n", "      <td>33.0</td>\n", "      <td>44.0</td>\n", "      <td>39.0</td>\n", "      <td>655</td>\n", "      <td>1752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-12-28</th>\n", "      <td>33.0</td>\n", "      <td>47.0</td>\n", "      <td>40.0</td>\n", "      <td>655</td>\n", "      <td>1753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-12-29</th>\n", "      <td>36.0</td>\n", "      <td>47.0</td>\n", "      <td>42.0</td>\n", "      <td>655</td>\n", "      <td>1753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-12-30</th>\n", "      <td>39.0</td>\n", "      <td>52.0</td>\n", "      <td>46.0</td>\n", "      <td>656</td>\n", "      <td>1754</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            MinTemp  MaxTemp  AvgTemp  Sunrise  Sunset\n", "DATE                                                  \n", "2018-12-26     35.0     45.0     40.0      654    1752\n", "2018-12-27     33.0     44.0     39.0      655    1752\n", "2018-12-28     33.0     47.0     40.0      655    1753\n", "2018-12-29     36.0     47.0     42.0      655    1753\n", "2018-12-30     39.0     52.0     46.0      656    1754"]}, "execution_count": 17, "metadata": {"tags": []}, "output_type": "execute_result"}], "source": ["model2=ARIMA(df['AvgTemp'],order=(1,0,5))\n", "model2=model2.fit()\n", "df.tail()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "hQ4chXSmvSZw"}, "source": ["#For Future Dates"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 552}, "colab_type": "code", "id": "ZsjzMPfKqVag", "outputId": "8de3772d-8613-4db7-91d4-673d3a1909e9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-12-30    46.418064\n", "2018-12-31    46.113783\n", "2019-01-01    45.617772\n", "2019-01-02    45.249555\n", "2019-01-03    45.116984\n", "2019-01-04    45.136771\n", "2019-01-05    45.156280\n", "2019-01-06    45.175516\n", "2019-01-07    45.194482\n", "2019-01-08    45.213183\n", "2019-01-09    45.231622\n", "2019-01-10    45.249802\n", "2019-01-11    45.267728\n", "2019-01-12    45.285403\n", "2019-01-13    45.302830\n", "2019-01-14    45.320012\n", "2019-01-15    45.336955\n", "2019-01-16    45.353659\n", "2019-01-17    45.370130\n", "2019-01-18    45.386370\n", "2019-01-19    45.402383\n", "2019-01-20    45.418171\n", "2019-01-21    45.433738\n", "2019-01-22    45.449087\n", "2019-01-23    45.464221\n", "2019-01-24    45.479143\n", "2019-01-25    45.493855\n", "2019-01-26    45.508362\n", "2019-01-27    45.522665\n", "2019-01-28    45.536769\n", "2019-01-29    45.550674\n", "Freq: D, Name: ARIMA Predictions, dtype: float64\n"]}], "source": ["index_future_dates=pd.date_range(start='2018-12-30',end='2019-01-29')\n", "#print(index_future_dates)\n", "pred=model2.predict(start=len(df),end=len(df)+30,typ='levels').rename('ARIMA Predictions')\n", "#print(comp_pred)\n", "pred.index=index_future_dates\n", "print(pred)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 372}, "colab_type": "code", "id": "hPgempSauwqo", "outputId": "3dc42a39-ff4d-4c6e-ac68-0cef1ffe7d01"}, "outputs": [{"data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x7f15c37985f8>"]}, "execution_count": 19, "metadata": {"tags": []}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 864x360 with 1 Axes>"]}, "metadata": {"needs_background": "light", "tags": []}, "output_type": "display_data"}], "source": ["pred.plot(figsize=(12,5),legend=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "JeeTSWbZww7r"}, "outputs": [], "source": []}], "metadata": {"colab": {"authorship_tag": "ABX9TyNyCEF0bu5JjuFIh9AZRrLX", "collapsed_sections": [], "include_colab_link": true, "name": "Temperature_Forecast_ARIMA.ipynb", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}