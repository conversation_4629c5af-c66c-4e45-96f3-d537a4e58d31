{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "Temperature_Forecast_ARIMA.ipynb", "provenance": [], "collapsed_sections": [], "toc_visible": true, "authorship_tag": "ABX9TyNyCEF0bu5JjuFIh9AZRrLX", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/nachi-hebbar/ARIMA-Temperature_Forecasting/blob/master/Temperature_Forecast_ARIMA.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "metadata": {"id": "rgI5xsf7eUVw", "colab_type": "code", "colab": {}}, "source": ["pip install pm<PERSON><PERSON>"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "sJUCGgJAeXmn", "colab_type": "code", "colab": {}}, "source": ["import pandas as pd\n", "import numpy as np"], "execution_count": 1, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "kzMWL_bGvvuY", "colab_type": "text"}, "source": ["#Read Data"]}, {"cell_type": "code", "metadata": {"id": "rKYgCgvcebUM", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 243}, "outputId": "8d11db02-ce47-432f-a9f6-dd8b06c79dd3"}, "source": ["df=pd.read_csv('/content/MaunaLoaDailyTemps.csv',index_col='DATE',parse_dates=True)\n", "df=df.dropna()\n", "print('Shape of data',df.shape)\n", "df.head()"], "execution_count": 2, "outputs": [{"output_type": "stream", "text": ["Shape of data (1821, 5)\n"], "name": "stdout"}, {"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>AvgTemp</th>\n", "      <th>Sunrise</th>\n", "      <th>Sunset</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DATE</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2014-01-01</th>\n", "      <td>33.0</td>\n", "      <td>46.0</td>\n", "      <td>40.0</td>\n", "      <td>657</td>\n", "      <td>1756</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-01-02</th>\n", "      <td>35.0</td>\n", "      <td>50.0</td>\n", "      <td>43.0</td>\n", "      <td>657</td>\n", "      <td>1756</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-01-03</th>\n", "      <td>36.0</td>\n", "      <td>45.0</td>\n", "      <td>41.0</td>\n", "      <td>657</td>\n", "      <td>1757</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-01-04</th>\n", "      <td>32.0</td>\n", "      <td>41.0</td>\n", "      <td>37.0</td>\n", "      <td>658</td>\n", "      <td>1757</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2014-01-05</th>\n", "      <td>24.0</td>\n", "      <td>38.0</td>\n", "      <td>31.0</td>\n", "      <td>658</td>\n", "      <td>1758</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            MinTemp  MaxTemp  AvgTemp  Sunrise  Sunset\n", "DATE                                                  \n", "2014-01-01     33.0     46.0     40.0      657    1756\n", "2014-01-02     35.0     50.0     43.0      657    1756\n", "2014-01-03     36.0     45.0     41.0      657    1757\n", "2014-01-04     32.0     41.0     37.0      658    1757\n", "2014-01-05     24.0     38.0     31.0      658    1758"]}, "metadata": {"tags": []}, "execution_count": 2}]}, {"cell_type": "markdown", "metadata": {"id": "kVswd7W7vyi4", "colab_type": "text"}, "source": ["#Plot Your Data"]}, {"cell_type": "code", "metadata": {"id": "KppUuT8-ejqD", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 334}, "outputId": "aabcf955-c76b-436f-8f4d-b1f1f35a975c"}, "source": ["df['AvgTemp'].plot(figsize=(12,5))"], "execution_count": 3, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x7f15e0036400>"]}, "metadata": {"tags": []}, "execution_count": 3}, {"output_type": "display_data", "data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAr8AAAEtCAYAAAABXwg7AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nOx9d6AkRbX3r3tm7r2bgA2AJFkJCiJRkKDiU9EniPrAgBn9nuIzPn3fe+9b86pgBhRUFBFMSFABgcXdJSywu8AuG9nA5rzL5nz3hpnu+v7oPtWnqqs6zMwNe2///rlzZ7qrqyucOnXqd85xhBAoUKBAgQIFChQoUGAwwO3rChQoUKBAgQIFChQo0FsolN8CBQoUKFCgQIECgwaF8lugQIECBQoUKFBg0KBQfgsUKFCgQIECBQoMGhTKb4ECBQoUKFCgQIFBg0L5LVCgQIECBQoUKDBoUO7Nh40ZM0aMHTu2Nx9ZoECBAgUKFChQYBBi9uzZ24UQh+vf96ryO3bsWMyaNas3H1mgQIECBQoUKFBgEMJxnLWm7wvaQ4ECBQoUKFCgQIFBg0L5LVCgQIECBQoUKDBoUCi/BQoUKFCgQIECBQYNCuW3QIECBQoUKFCgwKBBofwWKFCgQIECBQoUGDQolN8CBQoUKFCgQIECgwaF8lugQIECBQoUKFBg0KBQfgsUKFCgQIECBQoMGhTK7wDEwo17MHbcBKzbcaCvq1KgQK9j8qLNGDtuAvZ2Vvu6KgUK9Ci27O3E2HETMHP1zr6uSoECBxUK5XcA4m+zNwAAHntxSx/XpECB3scvp6wAAKza1t7HNSlQoGfx3KodAIA/PrumT+tRoMDBhkL5HYBoKQfd2u35fVyTAgX6AI4DAPCF6OOKFCjQs3DCsV6gQIF8KJTfAYiWUqj81grlt8DgQ6EOFChQoECBJBTK7wAEWX6rheW3wCBGYfgtMFhQDPUCBfKhUH4HICqF5bfAIIYbmn5Fof0WGOCQpxzFUC9QIBcK5XcAgiy/XYXyW2AQgniQhT5QYKCjoPwWKFAfCuV3AKKgPRQYzCj0gQKDDaLY6hUokAuF8juA8NKeDizetBfl8Ny3oD0MHCzYsAdPLduGZVv2Yf3O7PGb93ZWcdfMdYMq5rMjaQ99W49moer5eGrZtr6uRoEGMXfdLjyzYjuAgJLz4PxNueayCU6x1etTPLVsGyYv2gwAWLO9HSu27m9q+V01D1OXF3O/J1Du6woUaB4u/METAIBr/+01AArL70DCu34xTfl/zQ/fmem+z/xxNp4NY4FmvedgBykEAyXU2Y2PLsOvnlyJu6+5ABecMLqvq1OgTlzxq2cABPNw6ZZ9+NJdc3HWcYfhgc+/vo9rVqAe7GzvxtW3zwQAPPF/34S3XP8UgObK2R/+cwnumL4GD33hDTj92EObVm6BwvI7IEGLfhEDssDCjXv6ugq9jwFm+aVkHTvbu/u4JgWahc5qYJhYtKk583OgjPWDCZ4fNXpH1euRZ5AleXdHMfebjUL5HYCoeaHy28f1KFCgLyCjPQwQHiS9RzGfBw5Icap6zRmjhfLb+3DZhGwtF6rUwYaixwYgBspxb4EC9YBoDwNlGtB7FAc5AwO+L5omo4sx0XfwWB/29CnrQJFl/QmF8jsAwY9jChz8KOLV1oeBsglkS2wf1qJAs1DzhTydaxQ0IgbKKcfBBC5e/B5ecwvn9eYjk/LrOM4ax3EWOI4zz3GcWeF34x3H2Rh+N89xnMt6tqoFsqJGE7FYKwcEmnU0OljghlJtoGwCC8vvwIIvmmf5HRgj/OAEly9eD2+0e4pTPJiRJ9rDm4UQ27XvbhRC/LSZFSrQOAbKol8gQCH48oFoDwNnHgTv4Rba74CA54umjc2BM8YPPvANDO+HqufLLKuNgh5RrAHNR0F7GIAoQpwNLHQWgi8XSEccKIpBcZAzsFBrovJLCtgAYfgcVPDZMtvTkR+KNaD5yKr8CgCTHceZ7TjONez7LziO84LjOLc7jjOyB+o3qPH76asx7u8v5L5PpjUuBOKAQEd3NsH35NKt+NCtz/U4/+xgQZ6j5Rc27Ma7bp6Wua2TsHjTXlx+81Ts76o1XBbQvzjff3p2Df73b/P7uho9iu37uzB23AS8W4ut3Sz4TVB+Jy3ajKtvn5m7nH2dVVz286lYunlfQ88voMqXnz+2XH5uhgzRcaAHyhzsyKr8vkEIcQ6ASwF83nGciwHcAuBEAGcBeAnA9aYbHce5xnGcWY7jzNq2rchUkgfjH1qMu59fn/s+2iX2NA+pQO8gqyX/c3fOwbOrdgz6IzLyvK7lUAy+9/BiLNi4By9s2N3w8384cQkWbtyL59fsbLgsINrDuv3gnO6b/1iEe2dt6Otq9CgenLcJAPDChp6JkV3zhTI269ncfOZPs/HUsm1S+c1awvQVO7D4pb346eSluZ9ZQAVfXx9fslV+7omT14FyitWfkEmcCiE2hn+3ArgfwOuEEFuEEJ4QwgfwWwCvs9x7qxDiXCHEuYcffniz6l0gAV1hAPViwgwMZN3EmPp7MI4AogfkGf/NTBNLykyzSpQObwXxoVfQ0gMxW7mCqzu8dTXgyS+V38E40fsYtk2L30Tdl6J49KfTn4GC1FnuOM4wx3FG0GcAbwew0HGco9hlVwBY2DNVLJAXXbXA8jdQQj0NdmQNi0TdbXPEGCwgzm9fj/9mxf6ktyj83XoHPGFBs5QOPg11h7dGjsmrg3B+9xfYmr7WTO035VkF6keWaA9HArg/FORlAH8RQkx0HOdPjuOchUA2rwHwmR6rZYFcIEvCYFR8BiKyKnF0HZe9PSGI+zsiy2/+e5s5Y5pn+S3SlfcmuOXX8wXKpcbbnc/DmPJb9VCvw0wt5yAvhlDzYFtfm7npNhk0CjQHqcqvEGIVgDMN33+sR2pUoGFEym8fV6RAU5B1E0MCktMkBmOMYFISvTyKfxOVgp6Ky1scffYOWssl+bnmC7B/64YeGaBZ0QEifmkxNnobNoW0metuEc2j59APXCgKNBtEeygWy4GB7Jbf4O9gt/g3YvltBoin1yyOLnV/MZ17B5z20CznJb4h9YRQ/m+I9jAIN7f9Bba9dTNP23w594t+bjYK5XcAopMc3ooJMyCQNxXqYD8ik5bfOtqhGU3X7OaXdJZB3q99gWalIfZYObrlt5EYrvUq58VQahy2+dhUhzc595tXZoEAhfLbh1i340CPxARc8tJeAGYLoO8LzFi1Q8Yg3dtZxabdHcpv+zqrTa9TX2PZln39fve8fMs+Y4zevEqczfLbWfWwZnu78t2Krft6xFK8ctv+3HxEAFi1bX/D1jaZ5CKhnEWb9uClPR3RPRnK9XyBFVvT46Ou3XFAqUejiHh/zSmvQDKUbF11ajIkb4QQmLVmJ6avjJKj6srv/A17sH7ngcxlL98SjUFSzk0iorPqYd0Otdy0Ibl1byd2tXdnrstghk0u12t0MsmXKJRdfWXu2N+FOet21XXvQEeh/PYhLv7JFHz6j7OaXm57tz3aw4zVO3HVrc/h+4+8CAC49GdTcdEPnwAAzFq7C1fd+hy+9/DiptepLzF1+Ta8/cance+s/DGTewsLNuzB2258GrdOXRX7Le/6y/t9RFtE6/9/f38B//LTJ9EebnxWb2/HJTc8jeubHPNz4+4OvPX6p/CjiUty3bd1byfecv1T+O5DjY0/SXuwrBedVQ/vvGka3nfLs7nKvenx5bjkhqcV5UNHV83DxnAz2TSHt3DhG+x0lt4Cnz/1WH65vFm5bT/e9+tn8bk758jfPV8Ndfa9hxfjjT+ekqns2Wt34m03Pi3/J+XcVMvP/nk2Lv7JlFyb/td9/3Gcc+2jma8fzLC1ay5fA4afh/KFK8B+gxvf//jzbFz5q2ekTCoQoVB++wg0caat2J5yZf0wLZa7DwS7+rU7AgsgnxRk8V22ZX+P1akvsDq0di7cuLePa2LHutDyM399PMlCXg4Zv/y4kUMBAKcdfQieWbkDAKTyuyscC9PD75uFnfuDcmeszpfkYU9HMP6eWdmcOWFbnOi0xbQgJFlY5oZ9syFhIVHmXJMtv/395GKgoFHll7KnLduyH/s641n+PF/UTadYsVWVzUnlTFkaJJXKO2yKYZYNtoOleg+uZoVJcbbs7ZLfNUp5en5NYPVtb1K2yYGEQvntI/SGFcekM5FncUsp3vUD1XmCOKD9mTNJSpdrOCvPW29+7MbvpT6naCBtoRt7V5MzwkWB2fPdR/3UrF6ytZvpKDsLRaHihpnjenme0NP62vA7WJRv3s710B5ofrWWXaOc97QkF/WULeuXQdMyP2tw9GVPwh7tob62NTm2ymc02F29LbMOBhTKbx8hDy/IxAPVYVqYTM8g5bfVEL9noMaEJb2mr5WHJFDdTEpYXksCF76cL9paUZVf+r8RhxsT6t1Ehbpl0yxP1iD0ddavJJVfe4coz2zWeOsnsT7zpIs+mMHnTz1jpZPJWJvfhTEbY4b+peydhKrk/NrvHSTd1uuwrcuNGrb46ROJmkbnfl/Ljv6IQvntI+TRM7MsOkYha1J+wyNfUwrPgbo7JGtqf7ZcJSUyyMsh4/3O02OS5ZcWZ2qXRtKrmlCvJbnZFnrbIpQ4zhN+qoTtl5RVy1es7sn1ywrqw75ewAaqfNDB27ke50t5slJxjQaImi+MMj1L91IYS71+SWOtXmepAsmwtXmjUZb4PGtWtIfCXyCOQvntI+SxsmZZ9EzC1LQzjawSJtrDwLT8EsOjr5WHJFDV3CZbfqM4kUBrJbD20xig9mi25bfeoP3NDuhu2+zU68FPmb6SLL+CJzNo0ov0lyxP9bbbwQaF81uH0tDFZKzN8muSzVn6t1Oz/Nak8mu/l/9UZAlsHmzzO8tJbRK4/JTRHhqlPRTKbwyF8ttHoHXEpOzoyLJrMwm/JNqDzi0VwmyNGAigZAP9+fWo/0yc30ZCnUmFEgKt4S6AxgAVqy+ojaJu5ddvroWz2bSHsuum3q9afpuk/FJ5fax7DhrLL08PXodBgOZTa8VMe9CTXMjn1mP5DW9KWiNM47Af2wEOGtjmd6PrKA9/2qwY3329ce6PKJTfPoKXoOzoqJf2YJLbHd1mS0HVE3UJ+oMB1MT9WQAkc37z9YvymorlN5juB6RwDS2/tSZbfsPy8x630pxoHufXYvmtc5xXQstvVyLnN35k2Sj6S5KLgSofdDSaHpwU1LaKa+yzmoXzm6V/dYoS9UnSGmF0oCrQMHqK88uNB83K8FbQHuIolN8+AtEespxCZTlGsR2v/eCRF3Hz48vldzSxPCEwdfk2pT5pgv6XU1ZgZs7wVf0BEee3d5/7p2fX4PEXt8S+e2zxlti1tCjdN2cjtu7tVH7LonPcNXNddL3B+ugzzq9UTplirOPeWevxs8eW4boJi42C96+z1uOh+ZuMddEty1lRy+C8kwXSUmqz/Bp+yJKKmGgPnZbENEII/PCfUWzjrLri+p0H8Ok/zsK89bsxadFm/Pm5tVq56t+suGHyUmPovHqRxHUeSODjrx6lgRRU13GM1nKbw9uNjy5LL9vi8EZrRGfVw+fvnINHFrwUPa/JG7IXX9qLH/zzxX7tQ9EbsA0Nvb2vfXgxrr59JpYZ4oNv3N2BbzywAN97eDGWhr8v3bwPn/rDLCzcuIed3DUGzxf48cQlWLhxT4MlDRyU0y8p0BMgY56Ng6UI4AxCxna89pung6QJX3zryQAiq5cQwMd+N1NeW/WEVMhtT/vJpCAZwpofvjO1Pv0J4Wl1rwvrb/5jEQC1vUzfAVAa/doJL+KmD50t/8+y+fnqfQvkZyPnF5Hy1u0l9zMA/O/fXpCfP/H6V+CYw4Yov/9P+Pu7zjw6dm93nQ50kaJe1+1ROb6qEOio14JJm6huy/37u2r46+wNUT0yjrcZq3fi0cVbMHJoBffOCu7/6AXHx67LQ38RQuCmJ1bgpidWNG2+VpvsGNlfwbu3ngg4VZZ1LY/l9zdPr8JXLzs1sWx940bynMbGym37MWHBS0qsbH5LM4z3H/vdDGzf343/uPhEjBzW0niBBymsDrXs+x3t3bht2moAQYzm6ePeolz73/fOx7Or1DjrTyzZio27O3DkIa1NO/Xp6PbwqydX4nfTVmPptZc2VNZAQWH57SNEtAfz7/x4K5Pl18QhSzha0yduzUu3/B6sIKWlP78eF26kpBLycn7N0R4Y9zmnE0VeZbbeIza6r1n8NjvtIeGIOKFc6XhmeT/966yOL+RwWHLN4rge2k5PHHMO1FCIOhrlbQtprRNGZbPmibplrU4lksovs/wCGu++6bQHR3n2YIVuTHn7q48EoM77NFqjaU5xXjf93Gi3tXfXwuf140Wwl1Eov30EmiC241ZOes8yYPU51FJyE8Pp6EKw5osBK8wOhiQXvKv0SBx5BZZi+ZXCM36SkJWTm1v5rdOCG+WxbwxpKUFNC04WBVNuHG3X6MpvxhdJisBST3lAzyxyVUMIpoEIPgbqEYmSduObx1rN91HzfckhV+7N2a5Eq6C5Qz4dPIlRs50waZw2O0TiwQZdDlD4UD73+CmTqb9N05SoLSXXadppmCnT4GBHofz2EWiC2Cy/pnAnyeWpgqhcchSlRQ+JE3d486UgNVmsDubFTrZxP34FrojqCUjyhs7h1/M4kVLB62HLb1R+fUp7o2MtCplmoz0kKbj2cql+tvmoz6msFntSftsq8cQzHHnahTayzYxspcYfbV65/Q18/tRjQecy1qRsVkPLb5sh0VDq47TfdcuvzODJylaV+cY3mKTENTtE4sEGva9I+eXjh/Pky4asqqbxxTnjzZKJRXrjOArlt4/gSeXXYvmtxsOdJEE3MAihHp901tRg6Pr1NU/IOJ4mC/DBTIlwDwLLLxeCugUw7wKsOrwFfwWEVIRo45W1PfTwSmmo5VSuCRHtId99OmzUHkLSCUdSm1S95HJ1ZTfrgtUhaQ/J1+UZB6Solpqo/fI4v/15LjUKLurqeU/OszdttGqej5rny+gr6r35nqePySh9fdTvvMRmHAiQkldvSMOBAt0oQdZ2Lge45bdssHSZZAT5FLiOo8RpbwT7C+U3hkL57SNIIWez/OakPegLry+EcixF5dmObmt+ZPk1Pe9g5vu5dXAmext8c9Go8ssvl9nB/Ihikzdwet7jTVoU8o6ZKNRZg5ZfmRLU/HtyWCj7b/Q+WYPbZ+23KPyg+fd60nOToupmCSSeEVyRG8jUwUajPQg2jo2WXz+IqW5KMZ/2PP1XfUx2GjJ4KrSHJnScVH4tUU8GC/S+NdEeuFyvGHa3Sd3hOs0Lc1gov3EUym8fgRYSm+WXHyllC3Wmcr0Cy2+kfHTK8FNmBYPH+TV5wx/Mll8cBEkueJu3asffuR3eDNEeAEgtKq8gzbvIUX3r5So3y/KbJ85vRAmxlyv5lZa5oL9v1vfIGhouz6agJyy/fIwO5JS5piQxeRD5VZg5w4FzsW/keOd9nB7qLKI9RGXzMpthAKiUCssvEN+o0NrL5S83AOiOzKYyOJrJ+S1oD3EMeuVXCIE9HdVee153zcdLezpYRq/ot53t3djXGdRlKYsJaFJ+9nfVlMWIPpLQCzyNo/tIUNFcjEd7EJKfRAKV2qW9qyZTdtaD3mxfE6iN+zNvmStOeSy/ni/kmDFdzzc7NNTy0hKSFrl9nVXs7awqbUvlV2t+rG4mCCGwfueBpvHb6HZejhACe8O6kGJo0gvN2bAEtu3rwr5wAbFtRvR+okWwq+ZhxdZ9xk3sjv1dWB7O9bT33ra/K9aeprm1p6MqE5mUclp+u2s+DnSbF8qqMq6AXe3dsk0HEng3mcZD2jvzNN2mBDU1T6DmCbMylDIG9mr9TRu5mqb8cisjLzLrRloIgc6qZ6Q8kZI32Dm/elPS2svlAD8tqRiiuaRtRqisRudZe9fg7isTBr3y++cZ63DmdyZj9fb2Xnnel++Ziwt/8ATW7zwAQI3ze873HsVlN03FjFU78PX7F8rvTbyx13x7Er5y73z5P02SM487FABw3MihyvU67UFfh6u+Ly1aNd/HlCVbceZ3JuO5VTtw2rcn4eo7nq/rfacu34YzvzMZ05ZvT7+4h9HXlt/EI3XWxy05lN9vP7gQp4+frF4vVCUF0BZ06fSS8Vg+YZE7ffxknDF+Mn791KpY+Zv2dOL08ZOx50Cy4P7r7A1444+n4O7n1yt1rhemMf7bqatwxvjJ2LynU1pjTFZRU1P//pk1OO+6x/D0siApjD3UmW75Df7/+v0LcckNT+PPM9TkFb4v8NprH8OstbsApB95/+apVXjXzdPk/3+fvQFnfmcylmzeK7+7b07w3SU3PAUgW/p0jit+NR2v/tYk4298s+0LgfO//zguuf6pfA84CJAU7WH22l04Y/xkTFq02Xo/j6hj6tNqGO2hnFMZWrxpLx5fslUtixyZZbSHuPKrRnuwFq+VK3DKNyfiLT+N92/B+Q2gbySozXmf81OmIS0mB8dkwwb9/OjiLVi7o34dhWgP/dkA1NsY9MrvlFCYrNy6v1eeN2lRkN1re3s3gGhxokmyfmcHtu8PfnvjyWMAGI5Tw/95hi2acFdfOBYPfuH1uPiVhyv36OGnYqHOPCGf4/nArLVBJrfnw4xuL760F/WA7qfy+gL0pn3N+U2ijnALka6vkNJx16cvwMlHDFeU43uf3wAdKu0hUnRpo0XyOGtzZOE9TmTKgK4w7DrQnXjv5j1BRrtV25ozB2ms88Vpcjjv1u86IPuBW0WJD21aHCYuVBUdG51D/56ev2N/FwBg694u5feqZhXk99vafM2OA/Lzk6EyvnRzdEr05NJtyvV5Lb+LNtnneSfLLiZE4JizdV+X9fqDFcr80fqBMuY9u1JNTKDcz05bTFOe4vwaQ18lzDVTdi5JxdGcPIWIy4C08jnI4rtxd0fstyhT5MHrB9IM6PRAcmjjRgU+p0cZEoIkdUdAm4ku4HM/L4j2YOIdD1YM+paIEiD0jmJEgojCR5FCwnfRZJm65NQgaLY+yToNR1FU/3LJwRnHHhbjEuvhp+LKr6+lwg12qY3GcqSnZEkf21MQmuLfV0hyADNFaCBU/WChvPDE0XjLqUcoLWkat6ayeJmkaGdtjkxOP4q1TH3PtLsr8hi1OYtp5CEdPZmOmKuhpz1g9r42vapuILZtonTFgv7Vj6Sj69X7+fjgFiNbFkgZwY89Vq9ZXuU3CXkj0Bys4POnnnVB0m5goz2Elt+cDlBdBgIxRQaQYfgMfHsT59e0yePfJVl1C8tvAN2YQc6lvA/5PDb5GiRtRmyh8uoBWX71U8XBjEHfEiR/muEFmwU0lqXyG37fyZyKaFINCR2f9ElGR1t8TdRDp+lyVbf46goNeSADUMLw2FK5ZoUMatF3uq+RA9oXSLL88sXKtDGhI1LXcVQOn2Hcmjm/0RjPG1WhkVBrWZ5DY6NZHEK52WFDlxTsKjvhMCmGWRabrKHOqL1p0dOVBX0zpHAFM7Q5tRu3NOltnZZhKg9U5bdpxfY7+CnzK/1+Gn/mDG9VP7D8mjZfSc8zpZfWk1yYYlGnyQvTb50JVl1SoAY75zcWW18qv2wee+bPhLSkOk1XfgvLr8SgbwlaAHvL8kugBdE1WX7D39pCjpA+yaJYjlH3keCSSpImWHVPel0G1jxfLta+iMpuxNGNow9136bFSsz1TMMiY4qiYbo+ZvllzjE8/I0NiiWQKf7Rxif8LrGUCJkUAKZk5d0vUfkHmhQ6yRTtgRYmnsZboT3kiIRhpT1oixuVRd93dqdYfpWFMr0Rs8ypZiq/Sv0HsvLbYLQHzjk33U9xfk1H0EkbRZMhQoY605ReW1a3pNfhwzrJqivXrEEe6kw3ZpA84W3M127TyV+S3uH5ommbzPbC8hvDoG8JSXvoZVNGpPwG/3NhQ17VZPnVF9VOQzgbafkNv9KdeXS6g66cVT2hCNCmWX77wSpJdejNPjYJtSRrHm9mfQEMUqFGlt+sHsIA5/xyq79vfI4NeRWAvLQHPUxTo6C25H1Q5pZfj9KH1hdqKqvDG/VD1fJ++hhRHWXs1lwdSbSHZqYsHyy0h0bj4gr2V5fdLSVXnj7kjfZgyrSoJ7mgvwrtIWP5/L2T5qKeUGOwIgpXGvwfKb/xeVwpOcaTv6TQijrntxFEDm9NKW5AYNArvyXDUUVvIMb57Y5bfiPag2b5DY+kWg2BzEuS9qBZftnxN79ePtP3FUehyPI7cGgPvdnHRi/vBEWEK4wmZ0SyXDos64+1LBFf+ISIjmDzOrzlj9er/p/2nLzlp0FoYx2IUrJ2e75URk0ngJloD5ZL9D6nomoW2kMs3KBCe+Djwfw8kh1KlbVr86amTsJgUX5Vzq/6WxY5xjnnurLZVnFR8/2Q9pCP85soPzQ6m816zTfD8Wcz5TfBqkvPGuzKr+f7cJzotLVssvyGA6itUjJafpOjPTRv7pLi3WxZezCjUH41D/jegszfHfaASnsILb8t8Ywx/NqKifZQipQkjhjnV5sDNc3yS1Zlk5NFHtBjbE47vYEkgd9TMCm/Js6XvD7J4c0TzPIbfJdkDTQtfL6IW/8Vi1BGLmAWxAV68v3NVqTMtIeIp0jKqOmxWV7Vtijp7SSdj8J+1xWK2PVc+c1g+ZUOb+w7/aSl0fnLwTmevU0T600oDmL1WH41nj03RLRVSmGcXz93tIekjYyvnXaYwh2mlc9/SuLzehYaz2BD1RdB7F5p+Q2TXHDl148MWUbLb8I8Slov6sVA3rTmxaBXfqWHZh/RHigKArewUggkSn8Zs/yGgqnmC9z0+HLUPF/GCpUOb5ZoDzZv36rnG50lJi2Mx7Pc31XDdx5ahAUbotA7983ZgMUsTNKiTXvwwNyN8v8H5m7Eok3xUD30mx7G5x/z4t8t2bwXf5sdhPZ6cP4m5flZMHP1TiWA/8MvbMK8MHRRVtQ8Hzc/vjw1XSRffGaHYd4Soz2wn0xW+YjzG/xNCjHlCYFV2/Zj/IOLZAQFboUypTd+aP4mfPW+F/D76atjYyPL3Ni8pwO3T1sd1ldX6oBbn16Jrfs6zfVt8twz8RXxnoQAACAASURBVNqp/X795EqsC2Ns0893TF8tQzqZFge92zxf4I/PrpGxugHguVU78L0JL2r1CMqi+Txj9U7lXXUFks/zJWH4sr2dVcy3jfMM+8numo+7Zq7DigyhHHenhKRTlF99Q97t4abHlzeVZmGrw88fW46umof756oyp178Y95GRZZ4vpCbzDQl/+EXNsnwZwR+0lTzhSKLKwrtIb78/mXmOmP93nvLM5iaECu92/Px0dtmyJjafMw+vyaUP56Pm59Yodzn+wK/nLICO9u7cR0bv3qG0a/e9wJufnw5gKhNTFGHDiZMXb5Nxu6uBzUvkMvUu9J5no2ZW55cCSCI8bttXxdueXIlhBD4xRPL8ciCl7Blr12OVw1ycfeBbvxyyopMMtm0ad7Z3p0Yo7oZuDujvOlrDHrlV1p+e3lHREoJHZV0Giy/bRbOLzmhbdvXhRseXYYH5m2SC/HLRwXJLeLRHiILIBAI+ONGDYme6auepTXLMcnLDmnD7LW7cMf0Nfj+I5Gw/K975+Oym6bK/9950zR8+Z55ciFYvnU/3nnTNJjw5Xvm4fKb1d/+8+74d+/42VT891+DxB5fumsu3vULc3k6+Hv9/LHl8vMX/jIX//bL6ZnKIDz0wiZc/+gyXD95afIzWbu995ZnAaRxftUYqhyc9vCyQ9sAIDFpiO8L/PHZtfj9M2uUMunxUYa36EFfvmce7pq5HuMfWoxtmmKd5ahsy94ufPfhxdi0uyMmmBdt2oPvP7IEX7lnnvHe5iu/ccsvzfNV29vx8AsvAQjef/v+LnznocVYta1dfqejTQtOv6+zhm/9YxE+eOtz8rsP3vpcTAmSSWPY/F3DAtXzdmqruEo7LNgQlHXtw4ut72mKTaxX//UnjcZX71uAd2eYKzNXJ8fi5ht0XSbd/MRy3PDoMvx1VjzudDNx29RVuPGxZfjzc+vwlXtUmVMv/vPueYos8UWkmKaNzS/8ZS7eo8kQ/bTFcYAPnHss3nLKEWgpuwH1xvNRcR1cdOJo5V5SlvT6zV67C4tTYq1PW7EdExYEY5tvtL963wIAwMpt7bHQlVOWbsVPJi3FJ3//PO5iijdXbKet2I67Zq7H9Y8uQ0e3J8dts0IT9hU+9ruZ+PjtM+u+nyJ2kEGC/tKI2d9Vk5vYow5tw+rt7fjRxCV4dtUO/HTyMnzuzjnJ5Rss/d9+cBF+Mmkppq5ITxplk9uf+dPs1Hsbwbj7FuDymxuflz2NQa/8kuW3tx3e9ncFO/RW4vUqR55htIcK0R7USaBbp7pqHoQQOH70UIwe3grAHu2Bx7x9+aihGDt6qHwmX9BsFpzRw1vk8W2WtMX9wuGNVaFRntq+zsDim3YkZRI8ydQC4MhDgr6LOyNGDm//EiYvMVld3nvOsfI5+pgRrNwow5sZusUhz1GZ58ezWpGn8X5Lik39en3s5oX+noCZziBEfJxnEQPUt/omQQe1Y83zMby1HHzHnkfl3HjVmThsSIvG+Q0+tyccLUehziLoXUV+A1kiaSSF4gN0ao56LZXf0zzQXaFlsyfDFvp+5PNQz4mg5PwiMCiUXAc/ft+ZuP0T56GtUgqpN4HD218+fQF+87HXKvdnfTfq/zOPOyz2m0l881MvmnMkz+Jpk80nFAJRqMCetvL3d5AjMvWD6zhwnTi97BvvPBXDWyvRjRmHlMnZnDagB1JOHoGeoU2k4WDaGA165dd0VNEboFzb5LTGQxtV/WBHyWOTcpgUK1+op6Ax2oN2FOz7Ar4fRYyoeqpzhk2web7IF9+xjmZt9sLGm6tR5jEJn7SQMaZFM0n59UXE69Uv457hZIU0OaQQh9AXiDnT8JiRuvNjWt3zbgz18UnWpopFqdVPXRqlh+v8dsC8CROIC2mTHIhF3wjnRlokFLqu6gvjKQ6Pze065ji/mZpC8I/6xiP7XE2i5fA66Z8BZvXqYTlKsqe1SSGbTPX1RUSTiceszl4mnbbwcHNDKi46uj0lyYXex1mn27CWYENlCkdpGsd8Y0I/UyY3k5Mtgccj5ieEfaFc9SfQBoZax3XJITns/3A6uY6j8LtNdBcTujQDhxDMFyeDI6ueQbI30BfPrBeF8ttHoc72hTu3SPmNnt9dC7hEPDYph6muAqqQ1aM96EfBpAzRZKr5vqL0dFsEm+eLXNadelq10axysTo0cUEmhSdt8TVmXksYYzWfK79xyy8ps2TJM21AeOQS3ZlGiHg4JJtVPs7Zzd5+wYKvXk91NYV2MpXfaGxaneJD9dIhhIhtIkyvGu+PbO3Bk8aQ8yrfVFK5ZdeF4zjGDG9JjqKRwxtT8rWqcWtfGtLei8uHeJ+Zn99skGygE7NGYdrAeEKwhAXab5m4lsFfP1QUeRcOaSmho+oFTqy2zWDG+TY03Agb4/8avuMhMukZtPkz+RkQ+Fris5OdtM3SQAdF7OC0h8DyG/xObeo6qsKbVbzpDo6+EHLdyRLFpS82J/TMvozulBWDXvntM9pDp057iAZzV81DxXXlhNGVEaNTjiZkY+mNdc6vCJZMU+ar4H+L5degMCShHsWz2cHTeQ0ajTrRZYixbELubD6hk43jmCyNkTJbKbkou45xA1JhPMVYuDEIKZTlWLdUR+/7XMov4rQHafm1WDyaPffoPXk7mudM/JjeaAnU2jLrcS9dV/NEFLNboTcFn0tu0O+maA8m/Yjq6BgUTr32SbQJHWmJNbwE5TdPkpBGIJXfJll+TZnMhBBSkYlRkDIofDyaiohZfkuB5deLLL/68OdtmDTWpPJrUIRMU4pCZA5vLUdOa+H417uNb4T4KweUKhG7ZjAioD04chfqOI4SilIqv66jbHSyKqW6Eajmcctv+rzOkiin2ZAy7SDQfstZLnIcZw2AfQA8ADUhxLmO44wCcA+AsQDWAPiAEGJXz1Sz50Cd1Fe0BxoifEJ0VQPLLyk8MdqDnknKF6FjRTTg9EVT5/wKER6jlCLLs+rwZqc95LH81iMgm80bbKbll0JHpdIeDM9MEno135dWhDjtwVdoDMECGu8fslZ5Is759UU8DJJN59QX0zyxIbmFmUB8UFM616Ae9VuaTTBFLTG1vRBxCo+RrqLVL2viF3pm1feNMbsjy1DgNMPnCvWfqcU8n45b0xeYLNxAAud66/KEnmv6DMSdfXoKEe2hOZZfk6wJoj04KLlOrO+zKC50BZ2u8WFPnN+qz5Nc6FbX6P8kWTgkpD2YlF/THKKyhrWW5DgnBSvuZBs/haBy6d7BbvkNaA9uRHsI9WChyVfHcZRTr6ztZrL85qM99P7mhDaHptTx/Q15ts9vFkKcJYQ4N/x/HIDHhRAnA3g8/P+gg0xv3MvzmJyAZDgkVoHOWmAVIIVHV0Tjx8p+aGGIvtMHn34UQ6kTyyUHjhMmuchIe6AFyBRQXUc9ArL5ym/0udENqeT8pvC2jA5vCUq45wcWAlP6Yp7eGAh4v6Y2KjMnndimQ7C+98gyZa6Prtzl2Rj6wu7wZuO66QpFo8qvifZgsqAJxE8ZzI5xen9kVH79yPKbxPmlOWjK8GY6qaBxlMXhrV7Lr6kd+PjVx7fTS0aEZlt+TfPIF4H8dB0nJtuojXwhrJtqnibeD63IhCGVYO7WPD+IEYt4n/FxkBRLd1gC7SFR+W0py36KLL/aBo8pV7z8YGMdKr+D3PIb0NEcOfZpE0utIuTmVpV99Vp+Peajk0X57UvLr83Q0Z/QiAR5D4A/hJ//AODfGq9O78N1e0do69inWWT4YtJV9VBxmeU3xsFUy+qoejEha4v2QLooCW/XcVBxXSW9MWBf4H0/oj1QxIEkS1i1ltyuJsW52bQH1eGtsUkpLU8pnMN6HN5K0mFC/Y2nNwaCBdTE+aXx4vnmDRPVKc3hTbc45FFGfRHParV9f7dSPx1JaX6zQgiBfZ3VkGcZ1YVgtF4L4IBu+bXQIzj0MW1TgqpeMMdqvsCQFqI9xKM90KKpUiJ8dNU842aN2od+4zG6dStiFs6vEAJdNU9ZlE3toFp+eWg+YaRg9ASIdtSsKDIkayiyDhDMXccJFGB9LJIsrnqGDWYIOZ5FMBb5BmZISwl7OqqKU12M9sC+SIqoMySB9mDaaNMpwNCWkkxyJC2/2rVcuYpZfg0Gm8EGIYITUDXaAxSqDHVjsMZGYyCp3bjRiiJxEA501+SJx/6uWir1oS9oKfRuA8nyKwBMdhxntuM414TfHSmEeCn8vBnAkU2vXQ+h5vkYO24CfjllRa86vM1dF2eFRJ6znPMbWH6d8Ogt7vCm/h8ov2q58WgPAjNW7ZCxIrmSUC4Fz1CUX8vOssZoD7RwvPX6p4zXAskTfcGGPTjha4/I/792/wKMHTdBKna2CZQ3/BBfKBu1/N79/HoAwc729G9Pwmf+NMt4XZrDW4zX6wuUXFcJlSN/Y3F+gYg3qIPa68bHluHB+Zti5VOyhEcXb8HYcROsThP699v2dWHsuAm4b04Qw/XNP33SeB8AXHLD0zGh/diLWwBER9V7OqoYO24Cxo6bgHfdPA33zdmoXJ9nLt77/HqMHTcBn/3zHJw+fnIYWzqu3JvGoS8E/vDMmth3pus49LK+9Y9FxrrVPF8qtG0yYU1cwSy5wZaMz+vnVu3Eq74xMdY2QNR/63d2yOd/8NYglnQSd/OpZdswdtwErN7ejrHjJuAH/wzidH/v4Rfxqm9MxIZdUdKOk7/+T1w3QY0xbKORfPvBRfLU6SeT1PjXY8dNwPgHF+G/7pmHE746IfYueUFKmelA6YUNu+W4onjgaehgNIpvPrAQY8dNkOHJSo5Kexj/4CIZhzfoWzs1DICM86ufyBENiDa0uiJ/9vcexdTl27B1byfeduPT1rofMiQIn/WKMcNwzGFDlN9Mc+i3U1cDAIa1lllIKjPnlytW+mlF5PA2MC2/9zy/DmPHTcBvn16FseMmYPeBbrR31TB23AQpL75yzzxMXb492KCG9xHn97Zpq/Gmn0yxOrxdkxBnd/SwFvl5+341lOL//O0FPBTK9VueXIlXfWMi7p65Dp4v5Lj//fTVGDtuAjbt7sBHb5vRhNbIBxoTeztrePW3Jvb68/Mgq/L7BiHEOQAuBfB5x3Eu5j+KYLUxzgTHca5xHGeW4ziztm2rP5tKM9EZCtBfTlnRq16JL+2JZ7gi+ckXqc6qJ60CZdeJCRldsAWcXyRafn0h8E+WrS0SzJGQzxLqzBeR8kv1oOxYJiRZhacs3ar8/5cZ65RybV2TN5yKavltDC0sIsO+rhomLdpivC4WQstXOdX6sZXvC5QchJxfrX+FmiK1XHKMCy/f8GQ5Fmu3WAV15ZcSM1DSjNXb2/VbFNji35KDziY2XhZsjGcvy5Nwhuo0PQz6vnLb/lhkE/0zQSB+PGcarr4Azj1+JK674jU49/iRsfZZxzK9cVRZn7dWIm599KxQ+XWIehT9tnmvORseAJnlcP6GKKnG82t2yXciUMIbAmVcpE34b55aBSBY7AFgkyafSFnS66t//uOzaxMjdPz+mTW4b+7GzCG8kpCUqvzxFyN5Qm2UBjIslF0Hf3puLQDAC2Wp66q0B540xhd2yxr1cXDaosrli8M43fRMwGwtf2DupsQsjgDwqiNH4CfvOwM3fehs3H3NBYnXAsAhbWUceUgrWsuubEeSE/qc4/KrW7P8ygRIA5T2cMf0NQAgN4frd3ZgSzgfb58ezIkH5gVKaHuXp9AeqKvX7jgg21jn/Npw3RWvwd8/exGuu+I11mt02Ttr7S5lrb4uTDy1aNPeRBnSU8Y+bqjLElu8L5FJ+RVCbAz/bgVwP4DXAdjiOM5RABD+3Wq591YhxLlCiHMPP/xw0yW9DtMw7A3WgzlEGe2iVcsv8cGCdJia5Vc/JhNCOXoEzHF+uRCmaA+uEyjKuqNSFs5vFqpIPUdjVKqt/NxCl5XT8GaHvNpThIduma36ahIRnbbg+SLiGWpFkwMOwXQcCzSeHELWTTtOo2dnCa8DxPu87Do4YkRr5jA4nm/nU1qfGc6fzqpndHgz7ZeEiPeDOb2xwIi2Mj5y/vE44pBW6fRIsFkAa54vh550LPVVRQKI+KVZFyTZjobfeLsdPqJV+U2nS8h7wr9pG6ak1MwmbnJPHoun0TKywmMKCi9bHmFbihRCWDmV1MeClUU4llloy5bQhtEzkuvuOsD7zz0OJx4+HGOGtyZfjGBzdeEJo+GwsUbjX+8rbvnVnTRprg102gP53HCes4nLSl+VXHWNpf4jamEaPnL+8Thu1FB85Pzjc9WT9wOdrqWtBD3VdwdTBJDUHnEcZ5jjOCPoM4C3A1gI4EEAV4eXXQ3gHz1VyWaDukeIuCNYTyKJT6hEewjj/AJESdAtv3FP/kCRVRUk9R6hpDwWgmJQRgH2PV9YYwsTaozzm2WxqWcymByWlDrkLJNf3WioM8jxknyZrlTp0TR0RxtSfh2Dw5sQqmLrOk5sAwTkV+xtY75Ls1rTWMoa5aBd47O3lF1USi5L3JBe0bx6DI0Jcv4EVOXBZE0WiEcuMY1pbnkvu25sE2Aa464T1IkeW2HJZPRnUb9nHdcmhc0EfZ1OU7K6UziEiQ5vSnmqYqXUoUGLU9S3BuW3DhlOopS3le/boz3IesB+7F/lll+h9hM//rZx4KNnROWblC4nQd6bQOHVSux0iTY8+phWOL/sc41bfgco7UHG35acbCGVRXO4xuA6h1l+eTkB7aHnjpi53CBH0LSe6am+6+2QsY0gS6izIwHcH060MoC/CCEmOo7zPIB7Hcf5dwBrAXyg56rZXHDBSQKmpzMTBc+w16WqKL8eRrQFfK6y68YsS7oOYgqpE7PuaA5xnqRKBIKTHKICJcVLdnhjPLG0dqtrh5nSFVmVMAJfcBsVQZGlPrmSJuWXV1u3DHtCoBKGOtObVO/bsuvENkBA/uQQto1J3MuYxmjWKAdquS1lV6FqZDFQ02YgK+iZ5PwJqMqeLZOXrvxasyc60WY09mxDu7SUXYX2EIUUVD3nAYrykd3ym6RAJm30JGVAu53GdJrlV0lyoY0dPva6aj7aKuaIJF01Xzpq1QOqq2ko1rPwRmNStfzaoj3wa2zzgfqY5CM3+nEltmyJ9hA9I/rcUnZR6zafyADZlN+qH8QL5ydHVstvVVV4CQHtYWBZfvWwfvS6MmmQL+TmwDT/1fTG8XKCDG89l1KB0wCzPqenIkEcTBneUpVfIcQqAGcavt8B4K09UameBg3KIPe6+l3PPtdgfSLLL6c9VH2MGhpMokrJiSkpJsugL6BovCbLL7ce+iJa1CkloyeCOIKUgcgETwh0VCOeWJpltx4BmdYXecOnKcU1avgNC+tI8aLXF/6qrzrI6Jxgjzm8xTm/cT63kfaQ891slkbdi7jbYh3KipYwMUee7D/1nsR0Vj3jaY5RMRKIxUs2KTvcaUk/vqRoDjpaSi5qnh9xfsnyq1AxQgtTGC4p67jWQ52p9Yk+xyy/fvwajrT+VZQgrRD+rM6qF8SzNcSi7qh6jSm/Bnkp61SHEOfWOV4OORvbyvSFff5QH9PpGp+7XDkhRcoWuYKP3+A+dV6qSY2MRSiohZkiieYGREquLsc59YkbG3w/er+BwvmluNkE2ihHGTOBbspSGc5/PjZcqfxqJwhsnvZk6C9TlBb+NBN9p6foCQfTmBiUGd5MmZ96g/ZgO1IF4pZflfagcQwNynDMq9jI+WX/h7xKJ7zW94P6URxBk3WV6BEUe9IX8WQKOhqhPXCkJSxILi93FVKxvyv5iDhmUcxCe7A4vAWLcfQ/9ZeOvJZfk/UYiCvm3Z55gUwCF/aVkqtx1zMc0dbZaR1VjyXziL43677xJBe2KB2uxfJrC3nVUi4FtIfw/4rB8stDnTnI/s5JDqG8BH08WI/wSRHKw/nVaQ/sUTIajMHy22gM7ySaWl2c3/CVueWv6gnJ37S3mV32RbGA4xtXPn6I9mATofzRpqQ66kmfI5U1G52iFsYLd51oLNjCZXHLLw/t5zGu80BJcqHPO2p3akXu50InODzONNG4gn6O2j7K0uhYY5w3A6ryG/xNOgECeq7v+iK2cL0YlMqvHCCCC9Oef65JjkacX9XyS4tlxXXjcX5jlkF7tAcSkL4QikLM7yFh6PnCeDxLoN0uLWC+n64Q1WP5NU1MXk5e2gPf7DQa55dKSoufGnN483zlqNbm8GaK86tTVmzRHvLyme20h8Ytv3yRb5W0h+yW33q5Y0JE9awnvbFtg0rtrx8rUsICHa1lF1Xfhwh/IuVFWai4w5sbUQnSsgdGjmtpnF/1dxkCTruO/k/rX19EabbjKdejzzT2qW25MtZoDG96jEkE1LOg0waQN1XV8yXn10Z7EMI8f/Q403raeX5yIGkPlrrx8WtKqqP3L8n3Ya3mA91qGC+8pDi8mdvM5vDmMcddimN9sENfo/SIIkIIeUJEco3PUeoGR7P80hoR0B56zvLLqQZZ+6OnLLR9kVWuXgxS5Te+APXGJLaFWwLUxaSz5knLmcnyqwvkKUu2YdPuDkWtI0FIAnbltnbc9MQK+btHR3JuoChTZq4Wg2MOgSIRHOCW31gMYvU+fUG95cmVuOXJlVi1bX+sfIJpYvL2uTUM0ZSEKUu34p8LgjDUz63aKb9v2N8t7MP9KWljY5xfXyiW1o5uD0II3D93A9bvPIDFL+0NeYbArDU7sWzLPnmtL1RLvus4mLNud0yRyHuyZlMwdQsgbTbyKL9cSWwpB9kK82yEGnWMAoC9HVUZF9NmKdTbwBiRRUSbSf34ct2OA2baQ9lVrP00r2as3gEgGB/XTnhRlnmgy5OJb9Kyl9n67all2/D0siicpD7WawZZN2PVDimETCHbZqwK6ttd87Fsy35pXVyrhVx6koUtnLRoC55ZsR1TlgTf6WEcmwH91GL6iu1Yu0Ot/7od5hB0BN8XuHdWEBLNpvzadARfCGPIP1URQcwoYbL8mtaejbsP4NanV8WuTQLp1cMNyu++zqqMF67QHiyWX64Uz1wdyc+te7uUBE3NcnB6aP6mVJnaU9DXm0jpDf7OXb8bz68J2sBkwaX+dR1Hkcm0TrpO474mSVDCJ6ZY9AlJsvjB+Zvw++mrE2XwQ/M34Q7DNYXlt5+D7+z4Lr3nnxv/LnJ4Y8dMYc5wIHR40yanvthu3N2BVdvbjUKWvrpr5rpYXXwh4CBy7KiyNKymRYoWfvLm5yFgCHc/rz5Ht9L+aOIS/GjiEnzjgYWx8m3vB6iT6p5Z6633Ej55x/P47J1zAERJFoBmOLwFSLNg6QpkTbf81jw8u3IHvnLPfLzxx1PCugV9sWzLfrydBbenTQphThin9bsPq0kI8tIebMfset+T0pvHmYErv5/9lxNRYVFLTIt9S9nFYUMr8v96HCf011+z4wC+eNdcLNm8N3P6crvlN/isL35PLdtqXEhay4GyL9j/QBSn84f/XCLjYx8+ohWrmCLVlpI9MEqioNb16ttnKv/r40FaxNl3V936XGK2tKtufQ4AcOvTQXIHUoqeC5V4wpx1UczhH01cgg/fNgM/f3x5rLxGlV8aO/rY/chtMzB1+Xblu4t/MiWxrL/P2YAnQgWdEoYAgfx1HRgjr0T1gFSIOLisjji/0e9c+SXDxAUnjI6V89yqnZi8OJJbawyKvN6/VN5QA6d6ytJtqPnBukL0NcC8oXUctZ9msvckmUrIewpnwuJNe/HFu+biq/ctaLisemBL505j7SeTlmLxpiA51KFhYpGRQ4NkFP/51pPlfa7jKBsDWicdx8GJRwzPVad3nPYy+flVR45IvFYJRRf267UPvyi/O+u4wwAAx42KwuzZZL8QAl+6ay7GP7QYq7abDVRCCHzxrrn4zkOLsWTzPuW3ARXqbCCCy7O0sFrNRFL2KF3BpZ1+peTEjhJsApnLwiHhAmp7Ld8PljzHgbRwdNY8jBoWTG5TgGqyfvFoD/rCv6u9W/nfZi1cv8tulTEpE82aVI1bfoO/aVbQWFpUT00iwukjvG4m6y2PNsCfvW6nannK7/BmO/IMvn/4i2/AeWNHRilQLV3wxpPHxL7jFtL3nHWMdKoEzHNtSKWEud98G35w5ekA6rMoDbEojXs7aplPdszKLxjtwcCfM4zNQ9oqqPmR5bfsOnjn6UfJ37fuiwLQHza0Rbl3WIpDGJWZNgb18UDX69Ya3jRHH9pmLGvXgSjN7uhhLalpy21olqWwGScDeiZCQtXzZYa3pGgPNCZ4MhGdfxlzeOO0h3AsHTdqKCZ/5eLM9T4ijN+s9y/9b6LN7A3TJFdcNYSbSQlqK5cyW2FttIk8oGdt3mNPltSTiMX6JsdQ9h0pyDT/W0ou3nHay/CB845T0htzcMvvRSeOwYNfeH3mOv36Y6+Vn+/45HlY/YPLYnG7CbwP6SMluKiUHBwdxpZ+0ysPxy0fOQeAfR7yr7NEA9LpfwcTD3xQKr9SeRTR7q6v4vzavJdpF18OvcY5bANXUX7DBTRJeNORnOPQMXy0ozVl/+IRJEgIGPPKs2fanGiSOEdm2kN9k0pXehrh/PKyUsNCaa9Q8314WrB4Ckiu1M+gnevWo8g6r9YhL+fXtvsnZ5dKKaArpCksJqVT58aWWAg30zwILG0OizPdPOVXT92dBJvDGzVtWYv24Avz2BzRVlaiPcBxFN6zXg6HjbNJyLoB0y2DXdKCb2+LVksbcirGkJaSNTtgGuqJxcsRycvG5bWNW131/EzRHqguSlp4TntAPM6vq8hQzv/NPnfpWn2+k3w28YNJ0Q8sv5FSb5qLrRU3FqvbVodGHRh5HRr1x6gX1ndgTUMKMsklj53G0TzT+6OdcX7537wolxxFNupQwidq47W1XEIlvK/EnCJt4zrJqZXATz1Njt0c/ZkTPkiV3/jn3ugjXRFtKbny+VVPKFYlJb1xLMmFubJ8cpGCmL3hCQAAIABJREFUZFPqJe3BCSYF7eBGhbnF2w3RDPjkowXapATynbTtmDPJkmtSJuol6Dcz6DYvKotzEEdg+Y3+r3kitvgG3FJzWZzzS4pInPObT7ja2obCHJVL2dJymo7pdQup60aKT1KkClII6nGUtNEFeLzdNNhCnUkOvfZenm+O9jC8rRx8H/7kOiqFKUnZGdaSrPwmWe049MVY0le0sctLMfGNhVDH6pBKqe7UpY3OxyjObxOUX4sHftUL+tsUeUWtSVyB1C2/ehQeDj0iSlbozsyEUig8TEr9vs7Q8hvG+fUTxlBbuYT2lP6ledCoAyPAoiv0je4bewdTn5OSR2sTt+hbLb/hGkoyPU/ccg46LbDJ96oy5nTl15V95br5lF/b0OcKr8mxm6M/+78NTuWXHD8QLYp9wfltKbuMw+bLxBYA1PTGsSQX6crvEKn82utDIZxcx5E7fWn5Nez8efm0QJuUQD45rJZf37e2uUkg1xtQPZaJqgEBq1q0k4W+yRGAf8c95zlMAs731SDsJMD0jUV8MbRbggA7r1Zafl0306Jssrjq3FiuSJj6XVpWLdEEssAWPzawwGYrIy/twZbmtq1cCsd48D95fNM4TtpUDGvNRntIgz4eaMzqc0mJKmBQnLpqvnJKMaSllGoZtKFh5Tfh9CAvbJbf7poP17WnEQ+eb7ZC87YNOL92pYXPkTwZwEpWy2/wt8VwoiQtv1pCFT1ZSXB/+pynk4tmODDS+KvXMtoodOulHu0BiJQ8mb3PZ8pveI1ef7L8Uj81Yvnlf3XUNCdL/d7If8iRGyfbCQz/3jbHFOU3JUlQf6ZBDErlV+X80t/eUH7VZ1RK0VFw1RMY0RZZfBLTG2fg/HIrmG3DWfMCy6/rOnKnT5bfjqoXUxT5zpWcKkzKLd8N2iykNU9Yrblp0R7yoJlZiLLQOeS1QhcCqnOg55tCTgmjgNQ9xqlo3foWd4AJlV/LYpaW5KJcsh+1cZiUTl1p5ketpuErM6i59Vt+bbSHqpfd8mt1eJPWG/W9PCGMNAKatzzovEJ7SNhUDE2hPWRdT2wObzHll302bZQ6q54yhtoasPw2S872NO3BdQJFISnag6TMsbrwegkRpyxxKCd9CTQYHZHyq33vJNEeAsuvpD2E1TStJWnRRngdmkN7CP72leVXV+AlK5K1jbT8shjOJVdVavX5FnF+Q9pBndoWyVI9dj/BFO2BIAQk7cENeeyAnQ7paQYaE/j6Hs9kqkemMhbRLzAolV+e5jPi/Pb8c/Wx1FJ2mcObryi/FRbtQV+sTLt1QLUEcIXEtuOs+RTtIbL0jhwWOd/ox69c+SXag0m5zWINqHq+1fJodnir0/Kbkh0PyM5L4oIljfagF1n1VEu35/vG55q6ytMWUDpuNTnMcdAiaFvkraHOqpF1MovlNwvtIThqDT7bOL/8vmZyfrtqXuxdbdZwkzIQ0IPMll9fmB0HKakHleY6jjKXKwmbiqwOb2nQ9SnJ+Y3x8qLPrZV4uxzo9lTOb6V+zm+j8UXpbpsMzIMkueg6DkqOXTYEim1YF4X2oPL6dc4vB1d488SBLVmULbLqmZRXsvwGtAfmaG2QAaYxoENyfptBe0AfW371LI+k/LLvJOeXLObMD4BMv3r1aU115e91Wn6Z8qpDdzo3ZQctM+VZ+lRkUn7N9elMpD1kM9T1Bwxq5Zd/7g1itj4QKiVXEUIjWiPaAw3SCrMW2cohKE5RTACaJg0QKGCuEygmNFEPHVKRk7hNE4Kq8kuW37jwy2IVqvkJll/DrKt30dQVbNOErudIPE82LCCov+5MYNLnjbQHEU9NDcSVX/3eSpmoMzauWHKA+4rrZjqONdIetDHHwysZaQ+gk44wGUQdJoM2i9LIUx4T8mwIgqPr4LNuobNRKspuMG+lhcWBEu6N3tM0NdMc3rJSB/TFtsti+eUwbQo6NMvvkEqpbh+JRi2/JKebsaja6lKt+WHM7STaQ8T55Yp4NQfnV/XxyG/51ctNOumJaA+u8l4mC6DJEVeH5PwOAMuv/g6SimjoV5o7PPGQjfYQs/zW+YLU36b79fTq8SEtFP8hWkcasvyy9joQoz1kM9T1BwxS5Tf4W/OjIOdZ19rxDy7CJ++YmX6h8bma9ansKrSHYa1lxn00R3v40cQl+NvsDcby+dSg+4dUSnbaQ2jhcBwHe0PhOKRSsvKF+eQjq7BpIc0iED1f4HfTVlt/I6zdEYTzmrRos7Wsf/vldNzw6DIAwIZdB3DS1x6Rv73uuseVa31f4PfTV+PNP31Sfve+Xz+D6ycvBQDcN2cDXnfdY1i+RY1fqNeLYrTaoC/On/rjLMxau4v9Hh8Po4a1qBZediqh0h6C73Xrsy4b6SThZYcOgQl3zlhn/H7+hj0AAuVZV/ZOHz8pdn0WS5HK+TX9Hvwli+iX7poXu6a75uOSG57C+AcX4f/eOx9fumuu8nubRaHt6PZibW1T6m20B5vD22+nmsdwMG+jsijFKS0OVM4xI+N9w7n/JmTRfQ9pK8cWSwop9asnV1rvM1nxO7o9RbZk4YTa0CwWUjNCndkU224vMAq4CZxfbvnd11XDudc+ip9OWhpTHvRQZxwK5zeHM5SkPViiI5g2MHsl7cGRSY3+PnuD0dCQxQr9skOCkHhpp3wPzt+Es787OXHDJalBOZTD6Su244zxk/CDR6J4to8u3oIzxk/KzUOm9aqz6uH08ZOwIwzXmeR47Yu4BV4/aYni/EK5Li+oXZYa1qT75m7E57TYyxxKPbnDmxD4xRPLMXbcBLznF9PY9Wz8Wsb+35n+MXP1Tpz0tUewJQytVlh++zlMVt6sFonfP7MGU5ZuS7/Q+Fz1/0PaKtIaU/N8VEqOFFxkdS27an75WxIWLn1y/eLDZ+OR/3xjTIG58pxjAERHN1zWtZQjJ6fDh6txBU20B90Cqh/D1AN+/8owE1xS3Ml563fjpjCg/uRFWxL5gL4QGP/QYiU709x1u3FzmP3u7pnrsXVflzF7Ux5nHSEExgxvxUfOf7n8jmff8nw/Jly+/s5XK30YxdfVaA+WarSWS7jt4+fK/y88YTS++57T8NP3nSG/OzMMeG7Cz646S34uuw6Gt5ZjC6EpNuobThoT+0zRQrjgTzplkZzfcOyZNhf7OqtYsXU/fv/MGvx9zgY8OH+TMm+HM9oQjz3cUQ1CnY0dHcVj5dbqH1x5Or73ntNwystGmJVf3057IHzxLSfhu+85Dfd/7iL8+d/PD+NzR1QX1wkUe0oJS4rL3ddcqJTzlUteiU9cNBaff/OJeM0xhxifpc+vz1x8At55xlF4yylHyO8mfOmN+OTrx+ITF43FB8491liOCa8/KR6zuZtRdv7y6fPxyiPNAfs/zMY6QadwNOoAQ73TDM6v1fJLGd4Soj0ICGUebt/fjV9MWaE6DPkCvp/g8GaJ9nCUIdby/Z+7CFeefQwe/uIbrJxfigPruo78beTQCo4dOUQqhDy98dfuV5NKnHXcYbjhA2caM8Tp+H4YjzvtBGz8g4uw60BVxhk2geRgnmAISzbvw97OGp5ka/GPJi7B3s6aMVNhEsi4tGl3hyLfRmvr36hhLZLfz0Od2XRaig1sU44J333PabjzU+fnqnNWEPUGCPueRXv46eTAYETGDsAcM1hHlfXXk2HyFEowkzUyVX/AoFR+Gzn6bui52kNGDWtRuETkjABEi3OQXz5b+frO+fIzjsYrxgyLWatOPmKEfKbjOAq3qexGecjffMoReN3YUfI3JdpDSHswhXlqlNfH7yc+lv6cD5x7bIyWkYbhreVUyxMd+ZvewBo2zjB4PD9IJfrpN55gvMfz1TF37MghobIZvVOHTCOttr2tHkNaXFzy6iNxSKgEuq6Dj184FkeMiBZTm3UUAN5yaqQ8XRYmZLBZSK++8Hj5+cTDI2XoXWcG95GSEzl7sKNWk+U3rFYSzcJkReCK4CHMYno0s3Z3VD34ArjopDE4++WB8s+zYI0c2oKPXThWCQHFwR0ObY5Jhw1twccvHIuzXz4Sbzh5DMpucKpDi4njcEpHECFiaEsJxxymWn7/85KTMWpYC/7nX0/BCWOGx+qqv/MpLxuBr152Kn754XPwnXefJr8/btRQnP3ykRj/7tNwxdnZld+RWsINAKHjXvD5hDHDrRuoK84+Rvn/P950Iq48R312ww5v4e3NsCjZ5BTNt8RoDz6rjHKvelRO4SRN4HOdGxZee/zI2LVjRw/DDVedhdccc6jVweqNJx8efoqszf/n9a9QjCx09G2KQvHyUUNx5TnHGpX1D73uOPn5lUcOl47RWWV90lUk2/PYRUnmctodGY66cibeiHwR9Hqp5ZwwZphUlFXagxN+Z74/Lc7vOS8fadx0NgNCRMp9pRQ5vNnHNadQWHpNACcdMdx4AhSjPRTKb/9CI05PjUAX2COHtsgjl6rnK04w5LBWcpzM1hKbkNUX7MipKDje48fnlNiAruNH2orlN6Q9mJx9Grb8svfVvWwJgbNgvnJdJ33x5dZWHdbYiMaThDC2q/V43Vfuo7bl19O7ByHp1LJNaGMbJiCypLS1RH2Y1DfcGkqfbcoe32hxBxsSiNRWJGwdJzlMFS0glQSvd1PXcUsFdxjlHOnOqhfzuh/KnDklp86N8+sB1eHQZvnVv6d+pLkV0B5o3gU8vbSjbt2bnMD7sGSxHnLYQsCZYBqvPEmI69i5i7z9g7qZ4iJnrooR1DvN4BImKdAlNznag4AwGiWEovz6sUgtHDbZYOpHPp6t0R74NdpRN8m1SpjeOKk+pni0nA5TdqM1Im1tyqLQ6hvlLKC+42sXrVXdXj7ag+14X4+HPaSlFCW58ONxfmPKb021aNvi/Pako5/POMFlV7X8mqBEJLIZe0J5aJIDBe2hnyMpzXDPPlf9f9SwCjqqXhgrVCjCkIRNwDvLVn4WxwogOm4LPJHVo6tyKeIFlV1XUWxMtAdT2KRGjyQVy6/mZUtoLZdy8/6I65YEagtT0VkCgxN8P3BS0xcy6ovA8htXfrnyR2OD6k6wbdRIYdUdLDgHUM9jr9bNlWODFCabsscFtilrFQlBHuBdJrlIiPaQaPk1tDNfoLjyxZudOL9cWHOFsMyUTCvnl80JE2wbTFqcHceRfVv1/WCzm+LkpC+uBL7A8DlpaztbFAwTTP1d9YUyDm0OtDpXueTEx3+jXN0oLnoTaA8JZRAdzJ4hM4pSoH9PoPTWtv2cbaNn2hSVDcqvrjRxJYzTjVzXkSeMZWb90+tP9TEpaXwMVUrR6WDWtPNJYpfkfR7OL8kQvnbVb/mlyqmV1N+trVKShhlfxOMtxxIb+b7yu+316k1+kQWchlhha7ttHcwS7cEPaVsmORDLRls4vPUvmPq9N6zzOnfzsKEtECJQSGq+rzhARJa3dIWNkNXCUGLPcR1Htfxquee55y8f7HQU260N7pov6rL8cgHAJ2Bnd2QZ5+Bh4jI/I4HDR+gKFwnTZUme36bvXCceJ5c7HPCF1eRQFShtwWdTnF8dpNC5mlDmi0paiDYad7T5snmh2+Q1LUC65Vfl/JrKC5X/BOXX1M6divIbKV+8vTqqXshvj49fIGov21wLNonx/uHQv9fjFTvsGopxnRZJg8ZO0uKoWH4tylQe5dek3KuWX3vs57jl141d26jSSnfzfqr31C6pLiVXzYQWqwfjUnLwOU3hDfNafk1zzmTVjVl+2Rf89KfMLL9l15XjXX+1JMsvH0PlkhtReFJkfRZ9Vs6RHDogtbNq+Q0jEOVcf6JNuVYvzao9pBJZfn0/orPYqk3vlRStAcjHdc4LMqwBQb9RXWxj3xQJK15mSOMyKb9auYXlt5+h7yy/qiAkgdLZ7Qfpjd34byXXSRUwBCu3TFvQ+KDVlV8ukFtKquXXlN5YV0prnt9wjFZeZkR70KwUYWroPAuf46Rb0SPLr/n42wTzUXnQd/pCRvnVPV8NkVUyKDp0XA+oAtLK+a1EVBm9LEKapYZoC1SWLR6tzfpH91ObRKlYI+66sf5k+U2gPZhOWHmcSYX2oCm/QqjtwccbPyI2jV2+abVZa3WlXbf8uswKWgtjXKclNuBtZwNfUG3KFKe9pMFIe/AjRa/k2C2/8bjgcUWuWYthljSsaUiS+RQFJynagxBxBUCx/HopcX4zUmgAs4U/FueXLLqMalFyHengRmXL62LPtSdT0E9K0uLF6kiS01JW5FF+w+K4oluv5ZeqpldR/7+lHEVe4idJNI3160mWpL2XbT41A76IlHjK7gck0B48Pq/spx7EidcRy0lQcH77F0z90RsbFN3JgARKR9VDzdMsvy3REXbW8ZNVyOpjljsN8GvLOueXlU+CRudFBZnM6sjOxYSrmfagllligj4rSm66skzHg0blN4OTgPwuVJj0hYy8yD1fLc8kSDqY8sv71vYGQyycX440yy89ZkhLFGov6TodNC6ovyKlPmpTo8ObwSkkli3I0Cc8rB63/PL6dXZ7Md60iUNpOxng89Zm9dQVWWo3Wpz5WKj6gTUmLaQUNX3S2ugalCId+WgPcU6okqnOtVuw9DFcct3Y5qlx2kPwN0s80jQkLcxp0R4ow5vu9EPyJeCP+8lxfjNSaACzVVfvBj4W6KOjKSmB9Y8qqz/XvmluU2gPLqM3pcn6oKykbq950QYxK6jvumtRsqCI85u+/vB1gMoy0Vg4ggguwTVBtIewH+Q7arQH7b1spfck51cgsvxyWlvSuJafLc0oQidOU72LaA/9HFktvzXPx8zVO5vyzIUb92D3gaoisGhR6qh6qPrBMShNwOjY2W590GGbRHpKVoWvmUh7cDXaAy8zKINCnBCqnp+ZB8bBF+j7526Unzu6PSzbsg+bwziCUb2Dv/M27M78jKSg9UAQS7g9IUHHjv3dxu+tnF/HiS1kjkPZzoRRAPO+eXj+S1gfhu3JEu2hTdIeEP6Nj4e00ERU9BA2/kywjTVp+fXitAd6x3U74mHkqKr8qLFTq2ua1TDJ8qsnCuG1586GnVUfz69R5zxXnG2bARuvnsZMcEwYWX51mpMJdH0SF1IJl2VRpkyxe5PK0xW6GgvZVnLMFh8TTJbfmi+wbV8Xlhlils5dtwv/XPBSbD4t2bwXO9u7IYSQYayyhGRKghAC01dst/7uOmqEEh0B5zd+EkCXt5TcMNqDfa7YLH6mTZGyWTNsFKnOQJgmXW7oNKsxs/7pSqLMBGaol3ICGHJHXSd7tIekubtkczAWXghDbq3Yuj+xbwBVeSWZ1hrWf+nmvan14ZvmJDoWR9nllt84J1+/feW2duV3G+pNfpEFvogMEdyfZ9LCLbFrZ6zagSeWbJX/8z5buHEP9oTh6gTM9Kfn1+zEfG093rG/qynv0RMYlMpv1ji/Nzy6DB/4zbOYtz7o0Kz0A9PzLr95Gu6auQ4l15GhbGhROtBdC+L8ui4uP+NoAMCRh4QxG1MUNg7bFHrxJVUYcKHp+X7M4Y1QcR28lYW/ev2JUTgWKmJmqCic8rIwfJonZDvlmdNc+eVCubPq4e03Po2FG7V3CCfelb96JvMzXMdJFMKf+dNs+dk0Hh5/MS4wgITECK4Tt/yGXMKaJ5S60ALA5ck9s9bjmj8GdeJr7McuOD72PMcBhmq0B97PZdfByUcMV0IWmXDKUUE/Hh2G4LKlAXadIIYrheoa0VrGsSOH4PjRwwAA7znraPm+9Jea6Zv/WBQr732vDUJijRkWxdbUg9WnWe0PaYtHcKByiHJ0ZRiO6+WjhrJr6Z0cLNi4B+//9bMyyYnucFjRaEEEfZNDMVcfXRwkZ3GcKNpD1RPorqnRHl59VDymb2QNj9pTB39PquNHL1Dj7VZKbmbrb9l18e4z1WdVvSgboe2404SA6hG3/F5yw1N4+41Px66/4lfP4LN3zsGCjXuU79/xs6l4x8+exn1zok2xEpIpxWJnwoQFL+GxF7daf6doD3xqj2DxbwWCOa5vFEhutJRduWnQm+uqc+********************************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\n", "text/plain": ["<Figure size 864x360 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "markdown", "metadata": {"id": "2YK8qw2Gv1bT", "colab_type": "text"}, "source": ["#Check For Stationarity"]}, {"cell_type": "code", "metadata": {"id": "8TqnvNbBexKT", "colab_type": "code", "colab": {}}, "source": ["from statsmodels.tsa.stattools import adfuller\n", "\n", "def adf_test(dataset):\n", "  dftest = adfuller(dataset, autolag = 'AIC')\n", "  print(\"1. ADF : \",dftest[0])\n", "  print(\"2. P-Value : \", dftest[1])\n", "  print(\"3. Num Of Lags : \", dftest[2])\n", "  print(\"4. Num Of Observations Used For ADF Regression and Critical Values Calculation :\", dftest[3])\n", "  print(\"5. Critical Values :\")\n", "  for key, val in dftest[4].items():\n", "      print(\"\\t\",key, \": \", val)"], "execution_count": 4, "outputs": []}, {"cell_type": "code", "metadata": {"id": "muTlXcZYe3Jk", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 150}, "outputId": "80456da0-cc9f-46a0-dbbc-875d9f8b5c51"}, "source": ["adf_test(df['AvgTemp'])"], "execution_count": 5, "outputs": [{"output_type": "stream", "text": ["1. ADF :  -6.554680125068778\n", "2. P-Value :  8.675937480199557e-09\n", "3. <PERSON><PERSON> Of Lags :  12\n", "4. Num Of Observations Used For ADF Regression and Critical Values Calculation : 1808\n", "5. Critical Values :\n", "\t 1% :  -3.433972018026501\n", "\t 5% :  -2.8631399192826676\n", "\t 10% :  -2.5676217442756872\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "XE7o8ZDEv6TV", "colab_type": "text"}, "source": ["#Figure Out Order for ARIMA Model"]}, {"cell_type": "code", "metadata": {"id": "0Y1yq4A5e6j8", "colab_type": "code", "colab": {}}, "source": ["from pmdarima import auto_arima\n", "# Ignore harmless warnings\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"], "execution_count": 6, "outputs": []}, {"cell_type": "code", "metadata": {"id": "6ikhqu-mfHCA", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 497}, "outputId": "12c50b57-0a9c-4c14-c4a3-3061d4ebd954"}, "source": ["stepwise_fit = auto_arima(df['AvgTemp'], \n", "                          suppress_warnings=True)           \n", "\n", "stepwise_fit.summary()"], "execution_count": 7, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>SARIMAX Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>           <td>y</td>        <th>  No. Observations:  </th>   <td>1821</td>   \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>           <td>SARIMAX(1, 0, 5)</td> <th>  Log Likelihood     </th> <td>-4139.393</td>\n", "</tr>\n", "<tr>\n", "  <th>Date:</th>            <td>Fri, 18 Sep 2020</td> <th>  AIC                </th> <td>8294.785</td> \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                <td>04:22:38</td>     <th>  BIC                </th> <td>8338.842</td> \n", "</tr>\n", "<tr>\n", "  <th>Sample:</th>                  <td>0</td>        <th>  HQIC               </th> <td>8311.039</td> \n", "</tr>\n", "<tr>\n", "  <th></th>                      <td> - 1821</td>     <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>        <td>opg</td>       <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>         <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>intercept</th> <td>    1.2330</td> <td>    0.370</td> <td>    3.331</td> <td> 0.001</td> <td>    0.508</td> <td>    1.958</td>\n", "</tr>\n", "<tr>\n", "  <th>ar.L1</th>     <td>    0.9735</td> <td>    0.008</td> <td>  122.152</td> <td> 0.000</td> <td>    0.958</td> <td>    0.989</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L1</th>     <td>   -0.1221</td> <td>    0.024</td> <td>   -5.137</td> <td> 0.000</td> <td>   -0.169</td> <td>   -0.076</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L2</th>     <td>   -0.2201</td> <td>    0.024</td> <td>   -9.113</td> <td> 0.000</td> <td>   -0.267</td> <td>   -0.173</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L3</th>     <td>   -0.2022</td> <td>    0.024</td> <td>   -8.446</td> <td> 0.000</td> <td>   -0.249</td> <td>   -0.155</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L4</th>     <td>   -0.1371</td> <td>    0.023</td> <td>   -6.016</td> <td> 0.000</td> <td>   -0.182</td> <td>   -0.092</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L5</th>     <td>   -0.0506</td> <td>    0.024</td> <td>   -2.072</td> <td> 0.038</td> <td>   -0.098</td> <td>   -0.003</td>\n", "</tr>\n", "<tr>\n", "  <th>sigma2</th>    <td>    5.5035</td> <td>    0.172</td> <td>   31.914</td> <td> 0.000</td> <td>    5.165</td> <td>    5.841</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Ljung-Box (Q):</th>          <td>35.25</td> <th>  <PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>20.53</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(Q):</th>                <td>0.68</td>  <th>  Prob(JB):          </th> <td>0.00</td> \n", "</tr>\n", "<tr>\n", "  <th>Heteroskedasticity (H):</th> <td>0.81</td>  <th>  Skew:              </th> <td>-0.17</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(H) (two-sided):</th>    <td>0.01</td>  <th>  Kurtosis:          </th> <td>3.39</td> \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:                      y   No. Observations:                 1821\n", "Model:               SARIMAX(1, 0, 5)   Log Likelihood               -4139.393\n", "Date:                Fri, 18 Sep 2020   AIC                           8294.785\n", "Time:                        04:22:38   BIC                           8338.842\n", "Sample:                             0   HQIC                          8311.039\n", "                               - 1821                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept      1.2330      0.370      3.331      0.001       0.508       1.958\n", "ar.L1          0.9735      0.008    122.152      0.000       0.958       0.989\n", "ma.L1         -0.1221      0.024     -5.137      0.000      -0.169      -0.076\n", "ma.L2         -0.2201      0.024     -9.113      0.000      -0.267      -0.173\n", "ma.L3         -0.2022      0.024     -8.446      0.000      -0.249      -0.155\n", "ma.L4         -0.1371      0.023     -6.016      0.000      -0.182      -0.092\n", "ma.L5         -0.0506      0.024     -2.072      0.038      -0.098      -0.003\n", "sigma2         5.5035      0.172     31.914      0.000       5.165       5.841\n", "===================================================================================\n", "Ljung-Box (Q):                       35.25   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JB):                20.53\n", "Prob(Q):                              0.68   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               0.81   Skew:                            -0.17\n", "Prob(H) (two-sided):                  0.01   Kurtosis:                         3.39\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "\"\"\""]}, "metadata": {"tags": []}, "execution_count": 7}]}, {"cell_type": "code", "metadata": {"id": "i2OiS6-gfKHs", "colab_type": "code", "colab": {}}, "source": ["from statsmodels.tsa.arima_model import ARIMA"], "execution_count": 8, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "y-JQDC6yv9_y", "colab_type": "text"}, "source": ["#Split Data into Training and Testing"]}, {"cell_type": "code", "metadata": {"id": "h-SgGft2fYMF", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 234}, "outputId": "44f0e4aa-b673-4f81-e3cb-a9ee81948ee4"}, "source": ["print(df.shape)\n", "train=df.iloc[:-30]\n", "test=df.iloc[-30:]\n", "print(train.shape,test.shape)\n", "print(test.iloc[0],test.iloc[-1])"], "execution_count": 9, "outputs": [{"output_type": "stream", "text": ["(1821, 5)\n", "(1791, 5) (30, 5)\n", "MinTemp      36.0\n", "MaxTemp      52.0\n", "AvgTemp      44.0\n", "Sunrise     640.0\n", "Sunset     1743.0\n", "Name: 2018-12-01 00:00:00, dtype: float64 MinTemp      39.0\n", "MaxTemp      52.0\n", "AvgTemp      46.0\n", "Sunrise     656.0\n", "Sunset     1754.0\n", "Name: 2018-12-30 00:00:00, dtype: float64\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "emUTK7M6wCmx", "colab_type": "text"}, "source": ["## Train the Model"]}, {"cell_type": "code", "metadata": {"id": "LKr5XHt0fbWT", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 470}, "outputId": "78d6a43a-ed4b-48b3-9bea-20fb185ed8e4"}, "source": ["from statsmodels.tsa.arima_model import ARIMA\n", "model=ARIMA(train['AvgTemp'],order=(1,0,5))\n", "model=model.fit()\n", "model.summary()\n"], "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>ARMA Model Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>      <td>AvgTemp</td>     <th>  No. Observations:  </th>   <td>1821</td>   \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>            <td>ARMA(1, 5)</td>    <th>  Log Likelihood     </th> <td>-4138.130</td>\n", "</tr>\n", "<tr>\n", "  <th>Method:</th>             <td>css-mle</td>     <th>  S.D. of innovations</th>   <td>2.347</td>  \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>          <td>Fri, 18 Sep 2020</td> <th>  AIC                </th> <td>8292.261</td> \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>              <td>04:23:48</td>     <th>  BIC                </th> <td>8336.318</td> \n", "</tr>\n", "<tr>\n", "  <th>Sample:</th>                <td>0</td>        <th>  HQIC               </th> <td>8308.514</td> \n", "</tr>\n", "<tr>\n", "  <th></th>                       <td> </td>        <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "        <td></td>           <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>         <td>   46.5293</td> <td>    0.787</td> <td>   59.148</td> <td> 0.000</td> <td>   44.988</td> <td>   48.071</td>\n", "</tr>\n", "<tr>\n", "  <th>ar.L1.AvgTemp</th> <td>    0.9860</td> <td>    0.006</td> <td>  155.004</td> <td> 0.000</td> <td>    0.974</td> <td>    0.998</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L1.AvgTemp</th> <td>   -0.1403</td> <td>    0.024</td> <td>   -5.754</td> <td> 0.000</td> <td>   -0.188</td> <td>   -0.092</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L2.AvgTemp</th> <td>   -0.2328</td> <td>    0.024</td> <td>   -9.641</td> <td> 0.000</td> <td>   -0.280</td> <td>   -0.185</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L3.AvgTemp</th> <td>   -0.2163</td> <td>    0.025</td> <td>   -8.502</td> <td> 0.000</td> <td>   -0.266</td> <td>   -0.166</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L4.AvgTemp</th> <td>   -0.1478</td> <td>    0.023</td> <td>   -6.352</td> <td> 0.000</td> <td>   -0.193</td> <td>   -0.102</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L5.AvgTemp</th> <td>   -0.0587</td> <td>    0.024</td> <td>   -2.413</td> <td> 0.016</td> <td>   -0.106</td> <td>   -0.011</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<caption>Roots</caption>\n", "<tr>\n", "    <td></td>   <th>            Real</th>  <th>         Imaginary</th> <th>         Modulus</th>  <th>        Frequency</th>\n", "</tr>\n", "<tr>\n", "  <th>AR.1</th> <td>           1.0142</td> <td>          +0.0000j</td> <td>           1.0142</td> <td>           0.0000</td>\n", "</tr>\n", "<tr>\n", "  <th>MA.1</th> <td>           1.0867</td> <td>          -0.0000j</td> <td>           1.0867</td> <td>          -0.0000</td>\n", "</tr>\n", "<tr>\n", "  <th>MA.2</th> <td>           0.0537</td> <td>          -1.8503j</td> <td>           1.8511</td> <td>          -0.2454</td>\n", "</tr>\n", "<tr>\n", "  <th>MA.3</th> <td>           0.0537</td> <td>          +1.8503j</td> <td>           1.8511</td> <td>           0.2454</td>\n", "</tr>\n", "<tr>\n", "  <th>MA.4</th> <td>          -1.8566</td> <td>          -1.0630j</td> <td>           2.1394</td> <td>          -0.4172</td>\n", "</tr>\n", "<tr>\n", "  <th>MA.5</th> <td>          -1.8566</td> <td>          +1.0630j</td> <td>           2.1394</td> <td>           0.4172</td>\n", "</tr>\n", "</table>"], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                              ARMA Model Results                              \n", "==============================================================================\n", "Dep. Variable:                AvgTemp   No. Observations:                 1821\n", "Model:                     ARMA(1, 5)   Log Likelihood               -4138.130\n", "Method:                       css-mle   S.D. of innovations              2.347\n", "Date:                Fri, 18 Sep 2020   AIC                           8292.261\n", "Time:                        04:23:48   BIC                           8336.318\n", "Sample:                             0   HQIC                          8308.514\n", "                                                                              \n", "=================================================================================\n", "                    coef    std err          z      P>|z|      [0.025      0.975]\n", "---------------------------------------------------------------------------------\n", "const            46.5293      0.787     59.148      0.000      44.988      48.071\n", "ar.L1.AvgTemp     0.9860      0.006    155.004      0.000       0.974       0.998\n", "ma.L1.AvgTemp    -0.1403      0.024     -5.754      0.000      -0.188      -0.092\n", "ma.L2.AvgTemp    -0.2328      0.024     -9.641      0.000      -0.280      -0.185\n", "ma.L3.AvgTemp    -0.2163      0.025     -8.502      0.000      -0.266      -0.166\n", "ma.L4.AvgTemp    -0.1478      0.023     -6.352      0.000      -0.193      -0.102\n", "ma.L5.AvgTemp    -0.0587      0.024     -2.413      0.016      -0.106      -0.011\n", "                                    Roots                                    \n", "=============================================================================\n", "                  Real          Imaginary           Modulus         Frequency\n", "-----------------------------------------------------------------------------\n", "AR.1            1.0142           +0.0000j            1.0142            0.0000\n", "MA.1            1.0867           -0.0000j            1.0867           -0.0000\n", "MA.2            0.0537           -1.8503j            1.8511           -0.2454\n", "MA.3            0.0537           +1.8503j            1.8511            0.2454\n", "MA.4           -1.8566           -1.0630j            2.1394           -0.4172\n", "MA.5           -1.8566           +1.0630j            2.1394            0.4172\n", "-----------------------------------------------------------------------------\n", "\"\"\""]}, "metadata": {"tags": []}, "execution_count": 12}]}, {"cell_type": "markdown", "metadata": {"id": "QnJXQ4iBwFec", "colab_type": "text"}, "source": ["#Make Predictions on Test Set"]}, {"cell_type": "code", "metadata": {"id": "2ZtJTygKfg5i", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 331}, "outputId": "46053cf4-b267-4cb8-c89d-fbb7f1fc079c"}, "source": ["start=len(train)\n", "end=len(train)+len(test)-1\n", "#if the predicted values dont have date values as index, you will have to uncomment the following two commented lines to plot a graph\n", "#index_future_dates=pd.date_range(start='2018-12-01',end='2018-12-30')\n", "pred=model.predict(start=start,end=end,typ='levels').rename('ARIMA predictions')\n", "#pred.index=index_future_dates\n", "pred.plot(legend=True)\n", "test['AvgTemp'].plot(legend=True)\n"], "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x7f15c3aa47f0>"]}, "metadata": {"tags": []}, "execution_count": 13}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "grTT31Tdfqfb", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 331}, "outputId": "8caf1342-cc91-496f-c4ca-9159007add83"}, "source": ["pred.plot(legend='ARIMA Predictions')\n", "test['AvgTemp'].plot(legend=True)"], "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x7f15c37b9f60>"]}, "metadata": {"tags": []}, "execution_count": 14}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "3R2eTZHDfz5H", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 33}, "outputId": "8dcb8e41-f96d-4adf-9784-21342aacb1a4"}, "source": ["test['AvgTemp'].mean()"], "execution_count": 15, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["45.0"]}, "metadata": {"tags": []}, "execution_count": 15}]}, {"cell_type": "code", "metadata": {"id": "zQ7SB2lQux5x", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 33}, "outputId": "740e2eeb-e2b0-42ac-ec76-58da663146e9"}, "source": ["from sklearn.metrics import mean_squared_error\n", "from math import sqrt\n", "rmse=sqrt(mean_squared_error(pred,test['AvgTemp']))\n", "print(rmse)\n"], "execution_count": 16, "outputs": [{"output_type": "stream", "text": ["2.326343714317439\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "DgJUq_lGvV10", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 226}, "outputId": "c4087b3d-d92b-44ce-b116-fa1c7ffd3b47"}, "source": ["model2=ARIMA(df['AvgTemp'],order=(1,0,5))\n", "model2=model2.fit()\n", "df.tail()"], "execution_count": 17, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MinTemp</th>\n", "      <th>MaxTemp</th>\n", "      <th>AvgTemp</th>\n", "      <th>Sunrise</th>\n", "      <th>Sunset</th>\n", "    </tr>\n", "    <tr>\n", "      <th>DATE</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2018-12-26</th>\n", "      <td>35.0</td>\n", "      <td>45.0</td>\n", "      <td>40.0</td>\n", "      <td>654</td>\n", "      <td>1752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-12-27</th>\n", "      <td>33.0</td>\n", "      <td>44.0</td>\n", "      <td>39.0</td>\n", "      <td>655</td>\n", "      <td>1752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-12-28</th>\n", "      <td>33.0</td>\n", "      <td>47.0</td>\n", "      <td>40.0</td>\n", "      <td>655</td>\n", "      <td>1753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-12-29</th>\n", "      <td>36.0</td>\n", "      <td>47.0</td>\n", "      <td>42.0</td>\n", "      <td>655</td>\n", "      <td>1753</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2018-12-30</th>\n", "      <td>39.0</td>\n", "      <td>52.0</td>\n", "      <td>46.0</td>\n", "      <td>656</td>\n", "      <td>1754</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            MinTemp  MaxTemp  AvgTemp  Sunrise  Sunset\n", "DATE                                                  \n", "2018-12-26     35.0     45.0     40.0      654    1752\n", "2018-12-27     33.0     44.0     39.0      655    1752\n", "2018-12-28     33.0     47.0     40.0      655    1753\n", "2018-12-29     36.0     47.0     42.0      655    1753\n", "2018-12-30     39.0     52.0     46.0      656    1754"]}, "metadata": {"tags": []}, "execution_count": 17}]}, {"cell_type": "markdown", "metadata": {"id": "hQ4chXSmvSZw", "colab_type": "text"}, "source": ["#For Future Dates"]}, {"cell_type": "code", "metadata": {"id": "ZsjzMPfKqVag", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 552}, "outputId": "8de3772d-8613-4db7-91d4-673d3a1909e9"}, "source": ["index_future_dates=pd.date_range(start='2018-12-30',end='2019-01-29')\n", "#print(index_future_dates)\n", "pred=model2.predict(start=len(df),end=len(df)+30,typ='levels').rename('ARIMA Predictions')\n", "#print(comp_pred)\n", "pred.index=index_future_dates\n", "print(pred)"], "execution_count": 18, "outputs": [{"output_type": "stream", "text": ["2018-12-30    46.418064\n", "2018-12-31    46.113783\n", "2019-01-01    45.617772\n", "2019-01-02    45.249555\n", "2019-01-03    45.116984\n", "2019-01-04    45.136771\n", "2019-01-05    45.156280\n", "2019-01-06    45.175516\n", "2019-01-07    45.194482\n", "2019-01-08    45.213183\n", "2019-01-09    45.231622\n", "2019-01-10    45.249802\n", "2019-01-11    45.267728\n", "2019-01-12    45.285403\n", "2019-01-13    45.302830\n", "2019-01-14    45.320012\n", "2019-01-15    45.336955\n", "2019-01-16    45.353659\n", "2019-01-17    45.370130\n", "2019-01-18    45.386370\n", "2019-01-19    45.402383\n", "2019-01-20    45.418171\n", "2019-01-21    45.433738\n", "2019-01-22    45.449087\n", "2019-01-23    45.464221\n", "2019-01-24    45.479143\n", "2019-01-25    45.493855\n", "2019-01-26    45.508362\n", "2019-01-27    45.522665\n", "2019-01-28    45.536769\n", "2019-01-29    45.550674\n", "Freq: D, Name: ARIMA Predictions, dtype: float64\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "hPgempSauwqo", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 372}, "outputId": "3dc42a39-ff4d-4c6e-ac68-0cef1ffe7d01"}, "source": ["pred.plot(figsize=(12,5),legend=True)\n"], "execution_count": 19, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x7f15c37985f8>"]}, "metadata": {"tags": []}, "execution_count": 19}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 864x360 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "JeeTSWbZww7r", "colab_type": "code", "colab": {}}, "source": [""], "execution_count": null, "outputs": []}]}