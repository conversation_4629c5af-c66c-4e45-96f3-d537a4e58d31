#!/usr/bin/env python3
"""
Fixed ARIMA Analysis for Mauna Loa Temperature Data
This script addresses the issues with model.predict and empty plots
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.stattools import adfuller
from statsmodels.tsa.arima.model import ARIMA
from pmdarima import auto_arima
from sklearn.metrics import mean_absolute_error, mean_squared_error, mean_absolute_percentage_error
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")

def main():
    print("=== ARIMA Analysis for Mauna Loa Temperature Data ===\n")
    
    # 1. Load and prepare data
    print("1. Loading data...")
    df = pd.read_csv('Dataset/MaunaLoaDailyTemps.csv', index_col='DATE', parse_dates=True)
    df = df.dropna()
    
    print(f"Shape of data: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    print(f"Date range: {df.index[0]} to {df.index[-1]}")
    print()
    
    # 2. Plot the time series
    print("2. Plotting time series...")
    plt.figure(figsize=(15, 6))
    df['AvgTemp'].plot(title='Mauna Loa Daily Average Temperature', linewidth=1)
    plt.xlabel('Date')
    plt.ylabel('Average Temperature (°F)')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('temperature_series.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 3. Stationarity test
    print("3. Testing stationarity...")
    def adf_test(dataset):
        dftest = adfuller(dataset, autolag='AIC')
        print("1. ADF : ", dftest[0])
        print("2. P-Value : ", dftest[1])
        print("3. Num Of Lags : ", dftest[2])
        print("4. Num Of Observations Used For ADF Regression and Critical Values Calculation :", dftest[3])
        print("5. Critical Values :")
        for key, val in dftest[4].items():
            print("\t", key, ": ", val)
        
        if dftest[1] <= 0.05:
            print("\n✅ Series is stationary (p-value <= 0.05)")
        else:
            print("\n❌ Series is non-stationary (p-value > 0.05)")
    
    adf_test(df['AvgTemp'])
    print()
    
    # 4. Auto ARIMA to find best parameters
    print("4. Finding optimal ARIMA parameters...")
    stepwise_fit = auto_arima(df['AvgTemp'], 
                              suppress_warnings=True,
                              stepwise=True,
                              seasonal=False,
                              error_action='ignore')
    
    print(f"Optimal ARIMA order: {stepwise_fit.order}")
    print()
    
    # 5. Split data into train and test
    print("5. Splitting data...")
    train = df.iloc[:-30]
    test = df.iloc[-30:]
    
    print(f"Train shape: {train.shape}")
    print(f"Test shape: {test.shape}")
    print(f"Test period: {test.index[0]} to {test.index[-1]}")
    print()
    
    # 6. Fit ARIMA model
    print("6. Fitting ARIMA model...")
    optimal_order = stepwise_fit.order
    print(f"Using ARIMA order: {optimal_order}")
    
    model = ARIMA(train['AvgTemp'], order=optimal_order)
    fitted_model = model.fit()
    
    print("Model fitted successfully!")
    print()
    
    # 7. Make predictions
    print("7. Making predictions...")
    start = len(train)
    end = len(train) + len(test) - 1
    
    print(f"Making predictions from index {start} to {end}")
    
    # Predict with proper parameters
    predictions = fitted_model.predict(start=start, end=end, typ='levels')
    
    # Create a proper index for predictions
    prediction_index = test.index
    predictions.index = prediction_index
    
    print(f"Predictions shape: {predictions.shape}")
    print(f"Test data shape: {test['AvgTemp'].shape}")
    print()
    
    # 8. Plot predictions vs actual values
    print("8. Plotting results...")
    plt.figure(figsize=(15, 8))
    
    # Plot training data
    plt.plot(train.index, train['AvgTemp'], label='Training Data', color='blue', alpha=0.7)
    
    # Plot test data
    plt.plot(test.index, test['AvgTemp'], label='Actual Test Data', color='green', linewidth=2)
    
    # Plot predictions
    plt.plot(predictions.index, predictions, label='ARIMA Predictions', color='red', linewidth=2, linestyle='--')
    
    plt.title('ARIMA Model: Training, Test, and Predictions', fontsize=16)
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Average Temperature (°F)', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('arima_predictions.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 9. Calculate and display metrics
    print("9. Calculating performance metrics...")
    mae = mean_absolute_error(test['AvgTemp'], predictions)
    mse = mean_squared_error(test['AvgTemp'], predictions)
    rmse = np.sqrt(mse)
    mape = mean_absolute_percentage_error(test['AvgTemp'], predictions) * 100
    
    print("Model Performance Metrics:")
    print(f"Mean Absolute Error (MAE): {mae:.2f}°F")
    print(f"Mean Squared Error (MSE): {mse:.2f}")
    print(f"Root Mean Squared Error (RMSE): {rmse:.2f}°F")
    print(f"Mean Absolute Percentage Error (MAPE): {mape:.2f}%")
    
    # Create a comparison dataframe
    comparison_df = pd.DataFrame({
        'Actual': test['AvgTemp'],
        'Predicted': predictions,
        'Difference': test['AvgTemp'] - predictions
    })
    
    print("\nPrediction vs Actual Comparison (first 10 rows):")
    print(comparison_df.head(10))
    print()
    
    # 10. Plot residuals
    print("10. Analyzing residuals...")
    residuals = test['AvgTemp'] - predictions
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Residuals over time
    axes[0, 0].plot(residuals.index, residuals, color='purple')
    axes[0, 0].axhline(y=0, color='red', linestyle='--')
    axes[0, 0].set_title('Residuals Over Time')
    axes[0, 0].set_xlabel('Date')
    axes[0, 0].set_ylabel('Residuals')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Residuals histogram
    axes[0, 1].hist(residuals, bins=15, color='skyblue', edgecolor='black', alpha=0.7)
    axes[0, 1].set_title('Residuals Distribution')
    axes[0, 1].set_xlabel('Residuals')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Actual vs Predicted scatter
    axes[1, 0].scatter(test['AvgTemp'], predictions, alpha=0.6, color='green')
    axes[1, 0].plot([test['AvgTemp'].min(), test['AvgTemp'].max()], 
                    [test['AvgTemp'].min(), test['AvgTemp'].max()], 
                    'r--', lw=2)
    axes[1, 0].set_title('Actual vs Predicted')
    axes[1, 0].set_xlabel('Actual Values')
    axes[1, 0].set_ylabel('Predicted Values')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Residuals vs Predicted
    axes[1, 1].scatter(predictions, residuals, alpha=0.6, color='orange')
    axes[1, 1].axhline(y=0, color='red', linestyle='--')
    axes[1, 1].set_title('Residuals vs Predicted')
    axes[1, 1].set_xlabel('Predicted Values')
    axes[1, 1].set_ylabel('Residuals')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('residuals_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 11. Future predictions
    print("11. Making future predictions...")
    last_date = df.index[-1]
    future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), periods=30, freq='D')
    
    future_start = len(df)
    future_end = future_start + 29
    future_predictions = fitted_model.predict(start=future_start, end=future_end, typ='levels')
    future_predictions.index = future_dates
    
    # Plot with future predictions
    plt.figure(figsize=(15, 8))
    
    plt.plot(df.index, df['AvgTemp'], label='Historical Data', color='blue', alpha=0.7)
    plt.plot(predictions.index, predictions, label='Test Predictions', color='red', linewidth=2, linestyle='--')
    plt.plot(future_predictions.index, future_predictions, label='Future Predictions', color='purple', linewidth=2, linestyle=':')
    
    plt.title('ARIMA Model: Historical Data and Future Predictions', fontsize=16)
    plt.xlabel('Date', fontsize=12)
    plt.ylabel('Average Temperature (°F)', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('future_predictions.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Future predictions (next 10 days):")
    print(future_predictions.head(10))
    
    print("\n=== Analysis Complete ===")
    print("Plots saved as:")
    print("- temperature_series.png")
    print("- arima_predictions.png") 
    print("- residuals_analysis.png")
    print("- future_predictions.png")

if __name__ == "__main__":
    main() 