in notebook VAR_VECM_Analysis_2025.ipynb

for timeseries training and forcasting using VAR and VECM
using 3 metrolocological variables: uvindex, tempmax, and solarradiation
data are grouped weekly for faster computing


1- in Step3, test size is set for 168, is there a better test size?
2- in Step4, what is the best maxlags to use in model.select_order?
3- in Step5, is there a better way to calculate mape? why it's using 152 for train data is this correct ?
4- in Step6, what are the best lags to use in multiple VAR models comparison for better results?
5- in Step8, that should show forecast for future periods using best traing var model? are all numbers coorect, any better suggestions for more accurate results?
6- in Step10, write a better interpretation for the variance decomposition on the used variables after fixing the error in the cell.
7- in Step9, is there a better way to plot the impulse response functions?
8- steps 12-14 are fore VECM, is there a better way to implement them? add more details and interpretations on resutls
9- step 15 fix the error for granger causality test and add interpretation on the results.
10- add new cell for conclusion and interpretation of the results.

11- for all stpeps add human readable interpretation on the results.