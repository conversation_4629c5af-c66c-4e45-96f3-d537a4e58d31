Analyze codeing style and time series approch in the following codes in ../hw_final dir.
using same data set create a py and notebook for the solving project tasks in preojct.md

- first analyze the requirements carefully, build execution plan, then create scripts 
- refret to ../hw_final dir. and VECM2004.ipynb, follow the same structure and naming convention and same approch.
- make sure generated scripts are working, test each unit individually, for testing test with first 2 year of data.
- create solution in python script first, then convert it to jupyter notebook for visualization and reporting.
- genereate documnetion for final process and results 