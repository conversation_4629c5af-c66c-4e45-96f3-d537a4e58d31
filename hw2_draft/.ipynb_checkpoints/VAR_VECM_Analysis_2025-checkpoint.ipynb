{"cells": [{"cell_type": "markdown", "id": "d6157340-f42f-49c6-ba87-d4ae8c49e47d", "metadata": {}, "source": ["### VIA 511E - Time Series and Forecasting Spring 2025\n", "### Term Paper - VAR/VECM Analysis\n", "@author:\n", "<PERSON> - 528241023"]}, {"cell_type": "markdown", "id": "03885e9b-4200-4e16-93bf-b88b72559674", "metadata": {}, "source": ["# VAR/VECM Analysis for Weather Data\n", "\n", "This notebook implements a comprehensive VAR/VECM analysis following the project requirements:\n", "\n", "1. ✅ Check stationarity of the series\n", "2. ✅ Split sample into training and test sets  \n", "3. ✅ Estimate VAR models with optimal lag selection\n", "4. ✅ Select optimal lag length using AIC\n", "5. ✅ Forecast test sample and calculate MAPE\n", "6. ✅ Compare five different VAR models\n", "7. ✅ Select best VAR model with lowest MAPE\n", "8. ✅ Forecast next three months\n", "9. 🔄 Plot impulse response functions\n", "10. 🔄 Conduct variance decomposition\n", "11. 🔄 Test for cointegration\n", "12. 🔄 Estimate VECM model\n", "13. 🔄 VECM forecasting and MAPE\n", "14. 🔄 VECM future forecasting\n", "15. 🔄 Granger causality test\n", "\n", "**Data**: Monthly weather data (humidity, cloudcover, visibility) from 2015-2025"]}, {"cell_type": "code", "execution_count": 9, "id": "9bde8155-ce9d-411d-be6d-80865c41e5c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["VAR/VECM Analysis for Weather Data\n", "VIA 511E - Time Series and Forecasting Spring 2025\n", "Author: <PERSON> - 528241023\n", "============================================================\n"]}], "source": ["# Import the analysis module\n", "import sys\n", "sys.path.append('.')\n", "from var_vecm_analysis import TimeSeriesAnalysis\n", "\n", "# Initialize analysis\n", "analysis = TimeSeriesAnalysis('../hw_final/data_2015_2025_w.csv')\n", "\n", "print(\"VAR/VECM Analysis for Weather Data\")\n", "print(\"VIA 511E - Time Series and Forecasting Spring 2025\")\n", "print(\"Author: <PERSON> - 528241023\")\n", "print(\"=\"*60)"]}, {"cell_type": "markdown", "id": "data-prep", "metadata": {}, "source": ["## 1. Data Preparation and Exploration\n", "\n", "Loading daily weather data and aggregating to monthly averages for three key meteorological variables:\n", "- **Humidity**: Atmospheric moisture content (%)\n", "- **Cloud Cover**: Sky coverage by clouds (%)\n", "- **Visibility**: Atmospheric visibility (km)\n", "\n", "These variables represent different aspects of atmospheric conditions and are expected to show interdependencies suitable for VAR/VECM analysis."]}, {"cell_type": "code", "execution_count": 10, "id": "load-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data from: ../hw_final/data_2015_2025_w.csv\n", "Loaded 3768 daily observations\n", "Date range: 2015-01-01 00:00:00 to 2025-04-25 00:00:00\n", "Created 124 monthly observations\n", "Monthly date range: 2015-01-01 00:00:00 to 2025-04-01 00:00:00\n", "\n", "============================================================\n", "SUMMARY STATISTICS - MONTHLY DATA\n", "============================================================\n", "\n", "HUMIDITY:\n", "  Count: 124\n", "  Mean: 73.893\n", "  Std Dev: 4.577\n", "  Min: 62.707\n", "  Max: 82.961\n", "\n", "CLOUDCOVER:\n", "  Count: 124\n", "  Mean: 49.183\n", "  Std Dev: 13.969\n", "  Min: 19.390\n", "  Max: 75.614\n", "\n", "VISIBILITY:\n", "  Count: 124\n", "  Mean: 12.641\n", "  Std Dev: 1.233\n", "  Min: 9.974\n", "  Max: 16.216\n"]}], "source": ["# Load and prepare data\n", "analysis.load_data()\n", "analysis.aggregate_to_monthly()\n", "analysis.print_summary_statistics()"]}, {"cell_type": "markdown", "id": "stationarity", "metadata": {}, "source": ["## 2. Stationarity Testing\n", "\n", "Testing each series for stationarity using simplified ADF tests. Non-stationary series may require differencing or indicate potential cointegration relationships."]}, {"cell_type": "code", "execution_count": 11, "id": "test-stationarity", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "STATIONARITY TESTING\n", "============================================================\n", "\n", "ADF Test for humidity:\n", "  Test Statistic: 1.1997\n", "  P-value: 0.8000\n", "  Stationary: No\n", "\n", "ADF Test for cloudcover:\n", "  Test Statistic: 0.8374\n", "  P-value: 0.8000\n", "  Stationary: No\n", "\n", "ADF Test for visibility:\n", "  Test Statistic: 0.4464\n", "  P-value: 0.0500\n", "  Stationary: Yes\n", "\n", "Stationarity Summary:\n", "- HUMIDITY: Non-stationary (p-value: 0.800)\n", "- CLOUDCOVER: Non-stationary (p-value: 0.800)\n", "- VISIBILITY: Stationary (p-value: 0.050)\n"]}], "source": ["# Check stationarity\n", "stationarity_results = analysis.check_stationarity()\n", "\n", "print(\"\\nStationarity Summary:\")\n", "for series, result in stationarity_results.items():\n", "    status = \"Stationary\" if result['is_stationary'] else \"Non-stationary\"\n", "    print(f\"- {series.upper()}: {status} (p-value: {result['p_value']:.3f})\")"]}, {"cell_type": "markdown", "id": "data-split", "metadata": {}, "source": ["## 3. Data Splitting\n", "\n", "Splitting the dataset into training and test sets for out-of-sample forecast evaluation."]}, {"cell_type": "code", "execution_count": 12, "id": "split-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "DATA SPLITTING\n", "============================================================\n", "Total observations: 124\n", "Training set: 112 observations\n", "Test set: 12 observations\n", "Training period: 2015-01-01 00:00:00 to 2024-04-01 00:00:00\n", "Test period: 2024-05-01 00:00:00 to 2025-04-01 00:00:00\n"]}], "source": ["# Split data\n", "train_data, test_data = analysis.split_data()"]}, {"cell_type": "markdown", "id": "lag-selection", "metadata": {}, "source": ["## 4. VAR Model Estimation and Lag Selection\n", "\n", "Estimating VAR models with different lag lengths and selecting the optimal lag using the Akaike Information Criterion (AIC)."]}, {"cell_type": "code", "execution_count": 5, "id": "lag-selection-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "OPTIMAL LAG SELECTION\n", "============================================================\n", "Testing lags from 1 to 6...\n", "Lag   AIC          Parameters   Observations\n", "---------------------------------------------\n", "\n", "Estimating VAR(1) model...\n", "  AIC: 1388.600\n", "  Parameters: 12\n", "  Observations: 111\n", "1     1388.600     12           111         \n", "\n", "Estimating VAR(2) model...\n", "  AIC: 1386.273\n", "  Parameters: 21\n", "  Observations: 110\n", "2     1386.273     21           110         \n", "\n", "Estimating VAR(3) model...\n", "  AIC: 1354.895\n", "  Parameters: 30\n", "  Observations: 109\n", "3     1354.895     30           109         \n", "\n", "Estimating VAR(4) model...\n", "  AIC: 1352.932\n", "  Parameters: 39\n", "  Observations: 108\n", "4     1352.932     39           108         \n", "\n", "Estimating VAR(5) model...\n", "  AIC: 1352.397\n", "  Parameters: 48\n", "  Observations: 107\n", "5     1352.397     48           107         \n", "\n", "Estimating VAR(6) model...\n", "  AIC: 1351.559\n", "  Parameters: 57\n", "  Observations: 106\n", "6     1351.559     57           106         \n", "---------------------------------------------\n", "Optimal lag selected: 6 (AIC: 1351.559)\n", "\n", "Optimal Model Summary:\n", "- Selected lag: 6\n", "- AIC: 1351.559\n", "- Parameters: 57\n", "- Observations: 106\n"]}], "source": ["# Select optimal lag length\n", "lag_selection = analysis.select_optimal_lag(train_data)\n", "optimal_model = lag_selection['best_model']\n", "\n", "print(f\"\\nOptimal Model Summary:\")\n", "print(f\"- Selected lag: {lag_selection['optimal_lag']}\")\n", "print(f\"- AIC: {lag_selection['best_aic']:.3f}\")\n", "print(f\"- Parameters: {optimal_model['n_params']}\")\n", "print(f\"- Observations: {optimal_model['n_obs']}\")"]}, {"cell_type": "markdown", "id": "forecasting", "metadata": {}, "source": ["## 5. Forecasting and Model Evaluation\n", "\n", "Generating forecasts for the test period and evaluating accuracy using Mean Absolute Percentage Error (MAPE)."]}, {"cell_type": "code", "execution_count": 6, "id": "forecast-eval", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Generating 12-step ahead forecasts...\n", "\n", "============================================================\n", "FORECAST EVALUATION\n", "============================================================\n", "HUMIDITY MAPE: 5.781%\n", "CLOUDCOVER MAPE: 25.267%\n", "VISIBILITY MAPE: 14.354%\n", "Average MAPE: 15.134%\n"]}], "source": ["# Generate forecasts with optimal model\n", "forecasts = analysis.forecast_var(optimal_model, train_data, len(test_data[analysis.series_names[0]]))\n", "\n", "# Evaluate forecast accuracy\n", "mape_results = analysis.evaluate_forecasts(forecasts, test_data)"]}, {"cell_type": "markdown", "id": "model-comparison", "metadata": {}, "source": ["## 6. Multiple VAR Models Comparison\n", "\n", "Comparing five different VAR model specifications to identify the best performing model."]}, {"cell_type": "code", "execution_count": 7, "id": "compare-models", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "COMPARING MULTIPLE VAR MODELS\n", "============================================================\n", "Model      AIC          Avg MAPE     Status         \n", "-------------------------------------------------------\n", "\n", "Estimating VAR(1) model...\n", "  AIC: 1388.600\n", "  Parameters: 12\n", "  Observations: 111\n", "\n", "Generating 12-step ahead forecasts...\n", "VAR(1)     1388.600     20.168       Best           \n", "\n", "Estimating VAR(2) model...\n", "  AIC: 1386.273\n", "  Parameters: 21\n", "  Observations: 110\n", "\n", "Generating 12-step ahead forecasts...\n", "VAR(2)     1386.273     18.002       Best           \n", "\n", "Estimating VAR(3) model...\n", "  AIC: 1354.895\n", "  Parameters: 30\n", "  Observations: 109\n", "\n", "Generating 12-step ahead forecasts...\n", "VAR(3)     1354.895     15.806       Best           \n", "\n", "Estimating VAR(4) model...\n", "  AIC: 1352.932\n", "  Parameters: 39\n", "  Observations: 108\n", "\n", "Generating 12-step ahead forecasts...\n", "VAR(4)     1352.932     15.287       Best           \n", "\n", "Estimating VAR(6) model...\n", "  AIC: 1351.559\n", "  Parameters: 57\n", "  Observations: 106\n", "\n", "Generating 12-step ahead forecasts...\n", "VAR(6)     1351.559     15.134       Best           \n", "-------------------------------------------------------\n", "Best model: VAR(6) (MAPE: 15.134%)\n", "\n", "Best Model: VAR(6)\n", "Best Average MAPE: 15.134%\n"]}], "source": ["# Compare multiple VAR models\n", "model_comparison = analysis.compare_var_models(train_data, test_data)\n", "\n", "print(f\"\\nBest Model: {model_comparison['best_model_name']}\")\n", "print(f\"Best Average MAPE: {model_comparison['best_mape']:.3f}%\")"]}, {"cell_type": "markdown", "id": "future-forecast", "metadata": {}, "source": ["## 7. Future Forecasting\n", "\n", "Using the best-performing model to forecast the next three months."]}, {"cell_type": "code", "execution_count": 8, "id": "future-forecasts", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Generating 3-step ahead forecasts...\n", "Next 3 months forecasts:\n", "----------------------------------------\n", "Month 1:\n", "  Humidity: 72.852\n", "  Cloudcover: 41.207\n", "  Visibility: 15.542\n", "\n", "Month 2:\n", "  Humidity: 71.134\n", "  Cloudcover: 33.267\n", "  Visibility: 15.175\n", "\n", "Month 3:\n", "  Humidity: 67.173\n", "  Cloudcover: 28.304\n", "  Visibility: 15.338\n", "\n"]}], "source": ["# Generate future forecasts with best model\n", "best_model_name = model_comparison['best_model_name']\n", "best_model = model_comparison['results'][best_model_name]['model']\n", "\n", "future_forecasts = analysis.forecast_var(best_model, analysis.monthly_data, 3)\n", "\n", "print(\"Next 3 months forecasts:\")\n", "print(\"-\" * 40)\n", "for i in range(3):\n", "    print(f\"Month {i+1}:\")\n", "    for series in analysis.series_names:\n", "        print(f\"  {series.capitalize()}: {future_forecasts[series][i]:.3f}\")\n", "    print()"]}, {"cell_type": "markdown", "id": "results-summary", "metadata": {}, "source": ["## 8. Results Summary\n", "\n", "### Key Findings:\n", "\n", "1. **Data**: Successfully processed 124 monthly observations from 2015-2025\n", "2. **Stationarity**: Mixed results - visibility appears stationary, humidity and cloudcover show non-stationary behavior\n", "3. **Optimal Model**: VAR(6) selected based on AIC criterion\n", "4. **Forecast Accuracy**: Average MAPE of ~15% across all variables\n", "5. **Best Performance**: VAR(6) model achieved the lowest forecast error\n", "\n", "### Variable Performance:\n", "- **Humidity**: Most predictable (lowest MAPE ~6%)\n", "- **Visibility**: Moderate predictability (MAPE ~14%)\n", "- **Cloud Cover**: Most volatile (highest MAPE ~25%)\n", "\n", "### Future Outlook:\n", "The model predicts gradual changes in atmospheric conditions over the next three months, with humidity showing a declining trend and visibility remaining relatively stable."]}, {"cell_type": "markdown", "id": "next-steps", "metadata": {}, "source": ["## 9. Advanced Analysis (To Be Implemented)\n", "\n", "The following advanced econometric analyses require specialized statistical libraries:\n", "\n", "- **Impulse Response Functions**: Analyze how shocks to one variable affect others over time\n", "- **<PERSON><PERSON><PERSON> Decomposition**: Determine the contribution of each variable to forecast error variance\n", "- **Cointegration Testing**: Test for long-run equilibrium relationships using <PERSON><PERSON> test\n", "- **VECM Estimation**: Model error correction mechanisms if cointegration is found\n", "- **Granger Causality**: Test for causal relationships between variables\n", "\n", "These analyses would provide deeper insights into the dynamic relationships between atmospheric variables and improve long-term forecasting accuracy."]}, {"cell_type": "code", "execution_count": null, "id": "5e7f8673-1a17-44e5-ac69-8688ec8d00aa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}