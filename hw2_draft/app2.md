create v2 of notebook var_vecm_analysis_2025.ipynb

1- in the notebook do not import var_vecm_analysis.py, instead copy the code from the script and paste it in the notebook.

2- after loading data do not aggregate to monthly, instead use daily data.
3- for each step out of the 15 in project.md create a new cell in the notebook.
4- make sure each step is documented and commented in the notebook.
5- make sure each step is executed in the notebook.
6- make sure the results are visulaized in the notebook.
7- make sure after each step there is an interpretation of the results in the notebook based on the dataset actual results.

follow the same structure and naming convention in the followin notebooks
1- var.ipynb
2- VARmodel2024.ipynb
3- VECM2024.ipynb

each step of the 15 step need to be solve in a different cell in the notebook.
use same libraries and same approch as in the above notebooks.
