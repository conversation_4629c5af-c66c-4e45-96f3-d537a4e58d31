#!/usr/bin/env python3
"""
Test Script for VAR/VECM Analysis - First 2 Years Only
VIA 511E - Time Series and Forecasting Spring 2025

This script tests the VAR/VECM implementation using only the first 2 years of data
as requested in the project requirements for unit testing.

Author: <PERSON> 528241023
"""

import sys
from datetime import datetime
from var_vecm_analysis import TimeSeriesAnalysis

class TestAnalysis(TimeSeriesAnalysis):
    """Extended analysis class for testing with limited data"""
    
    def limit_to_first_n_years(self, n_years: int = 2):
        """Limit data to first n years for testing"""
        if not self.monthly_dates:
            print("Error: No monthly data available. Run aggregate_to_monthly() first.")
            return
        
        start_date = min(self.monthly_dates)
        end_date = datetime(start_date.year + n_years, start_date.month, start_date.day)
        
        # Filter data
        limited_dates = []
        limited_data = {series: [] for series in self.series_names}
        
        for i, date in enumerate(self.monthly_dates):
            if date < end_date:
                limited_dates.append(date)
                for series in self.series_names:
                    limited_data[series].append(self.monthly_data[series][i])
        
        # Update instance variables
        self.monthly_dates = limited_dates
        self.monthly_data = limited_data
        
        print(f"\nData limited to first {n_years} years:")
        print(f"- Period: {min(limited_dates)} to {max(limited_dates)}")
        print(f"- Observations: {len(limited_dates)}")
        
        return limited_data

def run_test_analysis():
    """Run complete analysis on first 2 years of data"""
    print("="*80)
    print("VAR/VECM ANALYSIS - TESTING WITH FIRST 2 YEARS")
    print("VIA 511E - Time Series and Forecasting Spring 2025")
    print("Author: Ahmed Elgarhy - 528241023")
    print("="*80)
    
    # Initialize test analysis
    test_analysis = TestAnalysis('../hw_final/data_2015_2025_w.csv')
    
    # Load and prepare data
    print("\n1. Loading and preparing data...")
    test_analysis.load_data()
    test_analysis.aggregate_to_monthly()
    
    # Limit to first 2 years
    print("\n2. Limiting to first 2 years for testing...")
    limited_data = test_analysis.limit_to_first_n_years(2)
    
    if len(test_analysis.monthly_dates) < 12:
        print("Warning: Less than 12 months of data available for meaningful analysis.")
        return
    
    # Print summary statistics
    test_analysis.print_summary_statistics()
    
    # Check stationarity
    print("\n3. Testing stationarity...")
    stationarity_results = test_analysis.check_stationarity()
    
    # Split data (use smaller test set for limited data)
    test_size = min(6, len(test_analysis.monthly_dates) // 4)  # 25% or 6 months, whichever is smaller
    print(f"\n4. Splitting data (test size: {test_size})...")
    train_data, test_data = test_analysis.split_data(test_size)
    
    if len(train_data[test_analysis.series_names[0]]) < 6:
        print("Warning: Insufficient training data for reliable VAR estimation.")
        print("Proceeding with simplified analysis...")
    
    # Test VAR estimation with limited lags
    max_lag = min(3, len(train_data[test_analysis.series_names[0]]) // 4)
    print(f"\n5. VAR model estimation (max lag: {max_lag})...")
    
    try:
        lag_selection = test_analysis.select_optimal_lag(train_data, max_lag)
        optimal_model = lag_selection['best_model']
        
        # Generate forecasts
        print("\n6. Generating forecasts...")
        forecasts = test_analysis.forecast_var(optimal_model, train_data, len(test_data[test_analysis.series_names[0]]))
        
        # Evaluate forecasts
        print("\n7. Evaluating forecast accuracy...")
        mape_results = test_analysis.evaluate_forecasts(forecasts, test_data)
        
        # Test cointegration
        print("\n8. Testing cointegration...")
        coint_result = test_analysis.johansen_test_simple(train_data)
        
        # Test Granger causality
        print("\n9. Testing Granger causality...")
        causality_results = test_analysis.granger_causality_test_simple(train_data)
        
        # Summary
        print("\n" + "="*60)
        print("TEST ANALYSIS SUMMARY")
        print("="*60)
        print(f"Data period: {min(test_analysis.monthly_dates)} to {max(test_analysis.monthly_dates)}")
        print(f"Total observations: {len(test_analysis.monthly_dates)}")
        print(f"Training observations: {len(train_data[test_analysis.series_names[0]])}")
        print(f"Test observations: {len(test_data[test_analysis.series_names[0]])}")
        print(f"Optimal lag: {lag_selection['optimal_lag']}")
        print(f"Average MAPE: {sum(mape_results[s] for s in test_analysis.series_names) / len(test_analysis.series_names):.3f}%")
        print(f"Cointegration detected: {'Yes' if coint_result['has_cointegration'] else 'No'}")
        
        causal_count = sum(1 for result in causality_results.values() if result['is_causal'])
        print(f"Causal relationships: {causal_count}/{len(causality_results)}")
        
        print("\n✅ Unit testing completed successfully!")
        print("All major components are working correctly with limited data.")
        
    except Exception as e:
        print(f"\nError during analysis: {e}")
        print("This may be due to insufficient data for the selected model complexity.")
        print("Consider using even simpler models or more data for robust analysis.")

def run_individual_tests():
    """Run individual component tests"""
    print("\n" + "="*60)
    print("INDIVIDUAL COMPONENT TESTS")
    print("="*60)
    
    test_analysis = TestAnalysis('../hw_final/data_2015_2025_w.csv')
    test_analysis.load_data()
    test_analysis.aggregate_to_monthly()
    test_analysis.limit_to_first_n_years(2)
    
    print("\n✅ Data loading and aggregation: PASSED")
    
    # Test stationarity
    try:
        stationarity_results = test_analysis.check_stationarity()
        print("✅ Stationarity testing: PASSED")
    except Exception as e:
        print(f"❌ Stationarity testing: FAILED ({e})")
    
    # Test VAR estimation
    try:
        train_data, _ = test_analysis.split_data(6)
        model = test_analysis.estimate_var_model(train_data, 1)
        print("✅ VAR model estimation: PASSED")
    except Exception as e:
        print(f"❌ VAR model estimation: FAILED ({e})")
    
    # Test forecasting
    try:
        forecasts = test_analysis.forecast_var(model, train_data, 3)
        print("✅ VAR forecasting: PASSED")
    except Exception as e:
        print(f"❌ VAR forecasting: FAILED ({e})")
    
    # Test MAPE calculation
    try:
        actual = [1, 2, 3, 4, 5]
        forecast = [1.1, 2.2, 2.8, 4.1, 4.9]
        mape = test_analysis.calculate_mape(actual, forecast)
        print(f"✅ MAPE calculation: PASSED (test MAPE: {mape:.3f}%)")
    except Exception as e:
        print(f"❌ MAPE calculation: FAILED ({e})")

if __name__ == "__main__":
    # Run main test analysis
    run_test_analysis()
    
    # Run individual component tests
    run_individual_tests()
    
    print("\n" + "="*80)
    print("TESTING COMPLETE")
    print("="*80)
    print("The VAR/VECM analysis implementation has been successfully tested")
    print("with the first 2 years of data as requested.")
    print("\nFor full analysis, run: python3 var_vecm_analysis.py")
