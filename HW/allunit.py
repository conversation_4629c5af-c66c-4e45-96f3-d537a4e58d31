import warnings
import pandas as pd
from pathlib import Path
import statsmodels.api as sm
import  matplotlib.pylab as plt
from statsmodels.graphics.tsaplots import plot_acf
from statsmodels.graphics.tsaplots import plot_pacf
plt.style.use('fivethirtyeight')
import arch
from arch.unitroot import ADF
from arch.unitroot import DFGLS
from arch.unitroot import PhillipsPerron
from arch.unitroot import KPSS
from pathlib import Path
from statsmodels.tsa.stattools import adfuller

data_folder = Path("D:/TimeSeriesandForecasting/DataandCodes")

# data_folder = Path("C:/data/")
# warnings.filterwarnings("ignore")
md = pd.read_excel( '../Money.xlsx', sheet_name='Sayfa1')
df=md
ip=df["ip"]
Real_Money=df["Real_Money"]
interest_rate=df["interest_rate"]

#Augmented Dickey Fuller Test for ip
ip.plot()

adf_ip = ADF(ip, trend='ct', max_lags=10, method='aic') 
print(adf_ip.summary().as_text())
reg_res = adf_ip.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
adf_cv=adf_ip.critical_values
print('ADF_critical values:',adf_cv)

dif_ip=ip.diff()
dif_ip=dif_ip.dropna()
dif_adf_ip = ADF(dif_ip, trend='ct', max_lags=10, method='aic') 
print(dif_adf_ip.summary().as_text())
reg_res = dif_adf_ip.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
adf_cv=adf_ip.critical_values
print('ADF_critical values:',adf_cv)


#Augmented Dickey Fuller Test for Real_Money
Real_Money.plot()
adf_Real_Money = ADF(Real_Money, trend='ct', max_lags=10, method='aic') 
print(adf_Real_Money.summary().as_text())
reg_res = adf_Real_Money.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
adf_cv=adf_Real_Money.critical_values
print('ADF_critical values:',adf_cv)


dif_Real_Money=Real_Money.diff()
dif_Real_Money=dif_Real_Money.dropna()
dif_adf_Real_Money = ADF(dif_Real_Money, trend='ct', max_lags=10, method='aic') 
print(dif_adf_Real_Money.summary().as_text())
reg_res = dif_adf_Real_Money.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
adf_cv=adf_Real_Money.critical_values
print('ADF_critical values:',adf_cv)

#Augmented Dickey Fuller Test for interest rate
interest_rate.plot()
adf_interest_rate = ADF(interest_rate, trend='c', max_lags=10, method='aic') 
print(adf_interest_rate.summary().as_text())
reg_res = adf_interest_rate.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
adf_cv=adf_interest_rate.critical_values
print('ADF_critical values:',adf_cv)

dif_interest_rate=interest_rate.diff()
dif_interest_rate=dif_interest_rate.dropna()
dif_adf_interest_rate = ADF(dif_interest_rate, trend='c', max_lags=10, method='aic') 
print(dif_adf_interest_rate.summary().as_text())
reg_res = dif_adf_interest_rate.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
adf_cv=adf_interest_rate.critical_values
print('ADF_critical values:',adf_cv)

#KPSS test for ip
kpss_ip = KPSS(ip, trend='ct') 
print(kpss_ip.summary().as_text())
print('KPSS_critical values:',kpss_ip._critical_values)

dif_kpss_ip=KPSS(dif_ip, trend='ct') 
print(dif_kpss_ip.summary().as_text())
print('KPSS_critical values:',kpss_ip._critical_values)

#KPSS test for Real_Money
kpss_Real_Money = KPSS(Real_Money, trend='ct') 
print(kpss_Real_Money.summary().as_text())
print('KPSS_critical values:',kpss_Real_Money._critical_values)
dif_kpss_Real_Money=KPSS(dif_Real_Money, trend='ct') 
print(dif_kpss_Real_Money.summary().as_text())
print('KPSS_critical values:',kpss_Real_Money._critical_values)

#KPSS test for interest_rate
kpss_interest_rate = KPSS(interest_rate, trend='c') 
print(kpss_interest_rate.summary().as_text())
print('KPSS_critical values:',kpss_interest_rate._critical_values)
dif_kpss_interest_rate=KPSS(dif_interest_rate, trend='c') 
print(dif_kpss_interest_rate.summary().as_text())
print('KPSS_critical values:',kpss_interest_rate._critical_values)

#PHILLIPSPERRON test for ip
PhillipsPerron_ip = PhillipsPerron (ip, trend='ct') 
print(PhillipsPerron_ip.summary().as_text())
print('PhillipsPerron_critical values:',PhillipsPerron_ip._critical_values)

dif_PhillipsPerron_ip= PhillipsPerron (dif_ip, trend='ct') 
print(dif_PhillipsPerron_ip.summary().as_text())
print('PhillipsPerron_critical values:',PhillipsPerron_ip._critical_values)

#PHILLIPSPERRON test for Real_Money
PhillipsPerron_Real_Money = PhillipsPerron (Real_Money, trend='ct') 
print(PhillipsPerron_Real_Money.summary().as_text())
print('PhillipsPerron_critical values:',PhillipsPerron_Real_Money._critical_values)

dif_PhillipsPerron_Real_Money= PhillipsPerron (dif_Real_Money, trend='ct') 
print(dif_PhillipsPerron_Real_Money.summary().as_text())
print('PhillipsPerron_critical values:',PhillipsPerron_Real_Money._critical_values)

#PHİLLİPSPERRON test for interest_rate
PhillipsPerron_interest_rate = PhillipsPerron (interest_rate, trend='c') 
print(PhillipsPerron_interest_rate.summary().as_text())
print('PhillipsPerron_critical values:',PhillipsPerron_interest_rate._critical_values)

dif_PhillipsPerron_interest_rate= PhillipsPerron (dif_interest_rate, trend='c') 
print(dif_PhillipsPerron_interest_rate.summary().as_text())
print('PhillipsPerron_critical values:',PhillipsPerron_interest_rate._critical_values)

#DFGLS Test for ip
DFGLS_ip = DFGLS(ip, trend='ct', max_lags=10, method='aic') 
print(DFGLS_ip.summary())
reg_res = DFGLS_ip.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
DFGLS_cv=DFGLS_ip.critical_values
print('DFGLS_critical values:',DFGLS_cv)


dif_ip=ip.diff()
dif_ip=dif_ip.dropna()
dif_DFGLS_ip = DFGLS(dif_ip, trend='ct', max_lags=10, method='aic') 
print(dif_DFGLS_ip.summary().as_text())
reg_res = dif_DFGLS_ip.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
DFGLS_cv=DFGLS_ip.critical_values
print('DFGLS_critical values:',DFGLS_cv)

#DFGLS Test for Real_Money
DFGLS_Real_Money = DFGLS(Real_Money, trend='ct', max_lags=10, method='aic') 
print(DFGLS_Real_Money.summary().as_text())
reg_res = DFGLS_Real_Money.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
DFGLS_cv=DFGLS_Real_Money.critical_values
print('DFGLS_critical values:',DFGLS_cv)

dif_Real_Money=Real_Money.diff()
dif_Real_Money=dif_Real_Money.dropna()
dif_DFGLS_Real_Money = DFGLS(dif_Real_Money, trend='ct', max_lags=10, method='aic') 
print(dif_DFGLS_Real_Money.summary().as_text())
reg_res = dif_DFGLS_Real_Money.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
DFGLS_cv=DFGLS_Real_Money.critical_values
print('DFGLS_critical values:',DFGLS_cv)

#DFGLS Test for interest_rate
DFGLS_interest_rate = DFGLS(interest_rate, trend='c', max_lags=10, method='aic') 
print(DFGLS_interest_rate.summary().as_text())
reg_res = DFGLS_interest_rate.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
DFGLS_cv=DFGLS_interest_rate.critical_values
print('DFGLS_critical values:',DFGLS_cv)


dif_interest_rate=interest_rate.diff()
dif_interest_rate=dif_interest_rate.dropna()
dif_DFGLS_interest_rate = DFGLS(dif_interest_rate, trend='c', max_lags=10, method='aic') #default
print(dif_DFGLS_interest_rate.summary().as_text())
reg_res = dif_DFGLS_interest_rate.regression
residuals=reg_res.resid
print(reg_res.summary().as_text())
plot_acf(residuals,lags=60)
print('test statistic',(reg_res.tvalues[0]))
DFGLS_cv=DFGLS_interest_rate.critical_values
print('DFGLS_critical values:',DFGLS_cv)

