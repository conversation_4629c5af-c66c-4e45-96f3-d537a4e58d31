# -*- coding: utf-8 -*-
"""LSTM.ipynb

Automatically generated by Colab.

Original file is located at
    https://colab.research.google.com/drive/1TM5vnDFVFjf28ye2NhsjVyiXavpisqCh
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import seaborn as sns

# Data loading and preparation

df = pd.read_csv('data_2015_2025.csv')
df['datetime'] = pd.to_datetime(df['datetime'])
df.set_index('datetime', inplace=True)

# Resample to weekly average
df = df.resample('W').mean(numeric_only=True)

print(f"Data shape: {df.shape}")
print(f"Date range: {df.index.min()} to {df.index.max()}")
print(f"Years of data: {len(df) / 52:.1f}")

feature_column = 'precip'
title = 'Precipitation.'

plt.figure(figsize=(15, 6))
df[feature_column].plot()
plt.title('Istanbul ' + title + ' Weekly Average Time Series')
plt.xlabel('Date')
plt.ylabel('Precipitation (mm)')
plt.grid(True, alpha=0.3)
plt.show()

print("\n=== DATA PREPROCESSING ===")

temperature_data = df[feature_column].values.reshape(-1, 1)
scaler = MinMaxScaler(feature_range=(0, 1))
scaled_data = scaler.fit_transform(temperature_data)

print(f"Original data range: {temperature_data.min():.1f}mm to {temperature_data.max():.1f}mm")
print(f"Scaled data range: {scaled_data.min():.3f} to {scaled_data.max():.3f}")

print("\n=== CREATING SEQUENCES ===")

def create_sequences(data, look_back=30):
    X, y = [], []
    for i in range(look_back, len(data)):
        X.append(data[i-look_back:i, 0])
        y.append(data[i, 0])
    return np.array(X), np.array(y)

look_back = 20
print(f"Using {look_back} days of history to predict next day")
X, y = create_sequences(scaled_data, look_back)
print(f"Total sequences created: {len(X)}")
print(f"Input shape: {X.shape}")
print(f"Output shape: {y.shape}")

print("\n=== DATA SPLITTING ===")
train_size = int(0.85 * len(X))
X_train, X_test = X[:train_size], X[train_size:]
y_train, y_test = y[:train_size], y[train_size:]

print(f"Training samples: {len(X_train)}")
print(f"Testing samples: {len(X_test)}")

# No need to reshape for MLP, keep as (samples, look_back)
print(f"Training data shape: {X_train.shape}")
print(f"Testing data shape: {X_test.shape}")

print("\n=== BUILDING MLP REGRESSOR ===")
model = MLPRegressor(hidden_layer_sizes=(64, 32), activation='relu', solver='adam', alpha=0.001,
                    max_iter=300, random_state=42, early_stopping=True, validation_fraction=0.2, verbose=True)

print("Model Architecture:")
print(model)

print("\n=== TRAINING MLP MODEL ===")
model.fit(X_train, y_train)

# 6. EVALUATE MODEL PERFORMANCE
print("\n=== MODEL EVALUATION ===")
y_pred_scaled = model.predict(X_test)
y_pred = scaler.inverse_transform(y_pred_scaled.reshape(-1, 1))
y_test_original = scaler.inverse_transform(y_test.reshape(-1, 1))

mse = mean_squared_error(y_test_original, y_pred)
mae = mean_absolute_error(y_test_original, y_pred)
rmse = np.sqrt(mse)
mape = np.mean(np.abs((y_test_original - y_pred) / y_test_original)) * 100

print(f"Test Performance Metrics:")
print(f"MSE: {mse:.4f}")
print(f"MAE: {mae:.4f}")
print(f"RMSE: {rmse:.4f}")
print(f"MAPE: {mape:.2f}%")

pred_df = pd.DataFrame(y_pred, columns=['Predictions'])
pred_df['TrueValues'] = y_test_original
plt.figure(figsize=(12,8))
sns.lineplot(data=pred_df)
plt.title("Predictions VS True Values on Testing Set")
plt.show()

print("\n=== VISUALIZATION ===")
# Plot loss curve from MLPRegressor
plt.figure(figsize=(8, 5))
plt.plot(model.loss_curve_, label='Training Loss')
plt.title('Model Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# Plot predictions vs actual
test_dates = df.index[train_size + look_back:]
plt.figure(figsize=(15, 8))
plt.plot(test_dates, y_test_original, label='Actual', color='blue', alpha=0.7)
plt.plot(test_dates, y_pred, label='MLP Predictions', color='red', linewidth=2)
plt.title('MLPRegressor: Predictions vs Actual')
plt.xlabel('Date')
plt.ylabel('Temperature (°C)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

print("\n=== FORECASTING NEXT 90 WEEKS ===")
def forecast_future(model, last_sequence, scaler, steps=90):
    forecasts = []
    current_sequence = last_sequence.copy()
    for _ in range(steps):
        current_sequence_reshaped = current_sequence.reshape(1, -1)
        next_pred = model.predict(current_sequence_reshaped)
        forecasts.append(next_pred[0])
        current_sequence = np.roll(current_sequence, -1)
        current_sequence[-1] = next_pred[0]
    return np.array(forecasts)

last_sequence = scaled_data[-look_back:].flatten()
future_forecasts_scaled = forecast_future(model, last_sequence, scaler, steps=90)
future_forecasts = scaler.inverse_transform(future_forecasts_scaled.reshape(-1, 1))

last_date = df.index[-1]
future_dates = pd.date_range(start=last_date + pd.Timedelta(weeks=1), periods=90, freq='W')

plt.figure(figsize=(15, 8))
plt.plot(df.index, df[feature_column], label='Historical Data', color='blue', alpha=0.7)
plt.plot(future_dates, future_forecasts, label='90-Week Forecast', color='red', linewidth=2)
plt.title('MLPRegressor: 90-Week Precipitation Forecast')
plt.xlabel('Date')
plt.ylabel('Precipitation (mm)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

print(f"\nForecast Statistics (Next 90 Weeks):")
print(f"Mean Forecast: {future_forecasts.mean():.2f} mm")
print(f"Min Forecast: {future_forecasts.min():.2f} mm")
print(f"Max Forecast: {future_forecasts.max():.2f} mm")
print(f"Standard Deviation: {future_forecasts.std():.2f} mm")

future_df = pd.DataFrame({
    'date': future_dates,
    'forecast': future_forecasts.flatten()
})
future_df['month'] = future_df['date'].dt.month
future_df['month_name'] = future_df['date'].dt.strftime('%B')

monthly_forecasts = future_df.groupby('month_name')['forecast'].agg(['mean', 'min', 'max'])
print(f"\nMonthly Forecast Breakdown:")
for month, stats in monthly_forecasts.iterrows():
    print(f"{month}: {stats['mean']:.1f} mm (min: {stats['min']:.1f} mm, max: {stats['max']:.1f} mm)")

forecast_df = pd.DataFrame({
    'date': future_dates,
    'forecast': future_forecasts.flatten()
})
forecast_df.to_csv('mlp_precipitation_forecast_90weeks.csv', index=False)
print(f"\nForecast saved to 'mlp_precipitation_forecast_90weeks.csv'")

print(f"\n=== MLP vs SARIMA COMPARISON ===")
print(f"MLPRegressor Performance:")
print(f"- RMSE: {rmse:.4f}")
print(f"- MAPE: {mape:.2f}%")
print(f"- Model Complexity: {model.n_layers_} layers, {model.n_outputs_} output(s)")
print(f"- Training Iterations: {model.n_iter_}")

print(f"\nAdvantages of MLPRegressor:")
print(f"- Captures non-linear patterns")
print(f"- Learns complex temporal dependencies")
print(f"- Can handle multiple seasonalities")
print(f"- Adaptive to changing patterns")

print(f"\n=== MODEL INTERPRETATION ===")
print(f"Look-back period: {look_back} days")
print(f"Network architecture: 2 hidden layers (64, 32 units)")
print(f"Activation function: ReLU (hidden), Identity (output)")
print(f"Optimizer: Adam with early stopping")

