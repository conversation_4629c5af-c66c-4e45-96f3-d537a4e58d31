{"cells": [{"cell_type": "markdown", "metadata": {"id": "0lb9MQPAbHg0"}, "source": ["### VIA 514E Spring 2025\n", "### Homework 1: Recommender system\n", "@authors:\n", "<PERSON> - 528241023  |\n", "<PERSON>e Law Win - 528241007"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "eN17rUcvLbZt"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from scipy.stats import pearsonr\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {"id": "Cwd9vsouIHW9"}, "source": ["### **Item-based collaborative filtering recommender system using cosine similatiry with scikit-learn **"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "HwkZTBiDLfXl"}, "outputs": [], "source": ["class ItemBasedCollaborativeFiltering:\n", "    def __init__(self, similarity_metric='cosine', k_neighbors=350):\n", "\n", "        self.similarity_metric = similarity_metric\n", "        self.k_neighbors = k_neighbors\n", "        self.user_item_matrix = None\n", "        self.item_similarity_matrix = None\n", "        self.item_means = None\n", "        self.train_df = None\n", "        self.test_df = None\n", "\n", "    def load_data(self, train_file, test_file):\n", "\n", "        print(f\"Loading training data from {train_file}...\\n\")\n", "        self.train_df = pd.read_csv(train_file)\n", "        print(self.train_df.head(),\"\\n\")\n", "        print(f\"Training data shape: {self.train_df.shape}\")\n", "        print(f\"Training data columns: {list(self.train_df.columns)}\\n\")\n", "\n", "        print(f\"Loading test data from {test_file}...\\n\")\n", "        self.test_df = pd.read_csv(test_file)\n", "        print(self.test_df.head(),\"\\n\")\n", "        print(f\"Test data shape: {self.test_df.shape}\")\n", "        print(f\"Test data columns: {list(self.test_df.columns)}\\n\")\n", "\n", "        # Check if test data has ratings (for evaluation) or just user-item pairs (for prediction)\n", "        self.has_test_ratings = 'rating' in self.test_df.columns\n", "        print(f\"Test data has ratings: {self.has_test_ratings}\")\n", "\n", "        return self.train_df, self.test_df\n", "\n", "    def create_user_item_matrix(self):\n", "        # Create user-item matrix from training data\n", "        # Create pivot table with users as rows and movies as columns\n", "        self.user_item_matrix = self.train_df.pivot_table(\n", "            index='userId',\n", "            columns='movieId',\n", "            values='rating',\n", "            fill_value=0\n", "        )\n", "\n", "        print(f\"User-Item Matrix shape: {self.user_item_matrix.shape}\")\n", "        print(f\"Number of users: {len(self.user_item_matrix.index)}\")\n", "        print(f\"Number of movies: {len(self.user_item_matrix.columns)}\")\n", "\n", "        # Calculate item means (excluding zero ratings)\n", "        self.item_means = {}\n", "        for movie_id in self.user_item_matrix.columns:\n", "            ratings = self.user_item_matrix[movie_id]\n", "            non_zero_ratings = ratings[ratings > 0]\n", "            if len(non_zero_ratings) > 0:\n", "                self.item_means[movie_id] = non_zero_ratings.mean()\n", "            else:\n", "                self.item_means[movie_id] = 0\n", "\n", "        return self.user_item_matrix\n", "\n", "    def compute_cosine_similarity_sklearn(self):\n", "\n", "        # Transpose to get items as rows for similarity computation\n", "        item_matrix = self.user_item_matrix.T\n", "\n", "        # Convert to dense array for scikit-learn\n", "        item_matrix_dense = item_matrix.values\n", "\n", "        # Compute cosine similarity using scikit-learn\n", "        similarity_matrix = cosine_similarity(item_matrix_dense)\n", "\n", "        # Convert back to DataFrame\n", "        self.item_similarity_matrix = pd.DataFrame(\n", "            similarity_matrix,\n", "            index=self.user_item_matrix.columns,\n", "            columns=self.user_item_matrix.columns\n", "        )\n", "\n", "        return self.item_similarity_matrix\n", "\n", "\n", "    def predict_rating(self, user_id, movie_id):\n", "        #Predict rating for a user-movie pair\n", "        if movie_id not in self.user_item_matrix.columns:\n", "            # If movie not in training data, return global average\n", "            return self.train_df['rating'].mean()\n", "\n", "        if user_id not in self.user_item_matrix.index:\n", "            # If user not in training data, return item average\n", "            return self.item_means.get(movie_id, self.train_df['rating'].mean())\n", "\n", "        # Get user's ratings\n", "        user_ratings = self.user_item_matrix.loc[user_id]\n", "\n", "        # Find movies rated by this user\n", "        rated_movies = user_ratings[user_ratings > 0].index\n", "\n", "        if len(rated_movies) == 0:\n", "            return self.item_means.get(movie_id, self.train_df['rating'].mean())\n", "\n", "        # Get similarities between target movie and rated movies\n", "        similarities = []\n", "        ratings = []\n", "\n", "        for rated_movie in rated_movies:\n", "            if movie_id in self.item_similarity_matrix.index and rated_movie in self.item_similarity_matrix.columns:\n", "                sim = self.item_similarity_matrix.loc[movie_id, rated_movie]\n", "                if sim > 0:  # Only consider positive similarities\n", "                    similarities.append(sim)\n", "                    ratings.append(user_ratings[rated_movie])\n", "\n", "        if len(similarities) == 0:\n", "            return self.item_means.get(movie_id, self.train_df['rating'].mean())\n", "\n", "        # Sort by similarity and take top k neighbors\n", "        sim_ratings = list(zip(similarities, ratings))\n", "        sim_ratings.sort(key=lambda x: x[0], reverse=True)\n", "        sim_ratings = sim_ratings[:self.k_neighbors]\n", "\n", "        # Weighted average prediction\n", "        numerator = sum(sim * rating for sim, rating in sim_ratings)\n", "        denominator = sum(sim for sim, rating in sim_ratings)\n", "\n", "        if denominator == 0:\n", "            return self.item_means.get(movie_id, self.train_df['rating'].mean())\n", "\n", "        predicted_rating = numerator / denominator\n", "\n", "        # Ensure rating is within valid range (0.5 to 5.0)\n", "        predicted_rating = max(0.5, min(5.0, predicted_rating))\n", "\n", "        return predicted_rating\n", "\n", "    def fit(self, train_file='ratings_train.csv', test_file='ratings_test.csv'):\n", "\n", "        #Train the model using separate train and test files\n", "\n", "        print(\"Loading train and test data...\")\n", "        self.load_data(train_file, test_file)\n", "\n", "        print(\"Creating user-item matrix...\")\n", "        self.create_user_item_matrix()\n", "\n", "        print(\"Computing item similarity matrix (cosine)...\")\n", "\n", "        self.compute_cosine_similarity_sklearn()\n", "\n", "        print(\"Model training completed!\")\n", "\n", "    def predict(self, user_movie_pairs=None):\n", "        #Generate predictions for given user-movie pairs or test set\n", "\n", "        if user_movie_pairs is None:\n", "            # Use test set\n", "            user_movie_pairs = self.test_df[['userId', 'movieId']]\n", "\n", "        predictions = []\n", "\n", "        print(\"Generating predictions...\\n\")\n", "        for idx, row in user_movie_pairs.iterrows():\n", "\n", "            if idx % 1000 == 0:\n", "                print(f\"Processing prediction {idx+1}/{len(user_movie_pairs)}\")\n", "\n", "            user_id = row['userId']\n", "            movie_id = row['movieId']\n", "\n", "            predicted_rating = self.predict_rating(user_id, movie_id)\n", "            predictions.append(predicted_rating)\n", "        print(\"Predictions completed\")\n", "        return np.array(predictions)\n", "\n", "    def save_predictions(self, predictions=None, filename='predictions.csv'):\n", "\n", "        if predictions is None:\n", "            predictions = self.predict()\n", "\n", "\n", "        predictions_df = pd.DataFrame({\n", "            'userId': self.test_df['userId'],\n", "            'movieId': self.test_df['movieId'],\n", "            'predicted_rating': predictions\n", "        })\n", "\n", "\n", "        predictions_df.to_csv(filename, index=False)\n", "        print(f\"Predictions saved to {filename}\")\n", "\n", "        return predictions_df\n", "\n", "\n", "def main():\n", "    # Initialize the recommender system\n", "    print(\"Initializing Item-Based Collaborative Filtering Recommender with Scikit-learn...\")\n", "    recommender = ItemBasedCollaborativeFiltering(\n", "        similarity_metric='cosine',\n", "        k_neighbors=350\n", "    )\n", "\n", "    # Train the model using separate train and test files\n", "    recommender.fit('ratings_train.csv', 'ratings_test.csv')\n", "\n", "    # Generate predictions for test set\n", "    predictions = recommender.predict()\n", "    predictions_df = recommender.save_predictions(predictions)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "N_UYTY_mMels", "outputId": "033cc35d-5012-450f-af21-97afcd7c7924"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing Item-Based Collaborative Filtering Recommender with Scikit-learn...\n", "Loading train and test data...\n", "Loading training data from ratings_train.csv...\n", "\n", "   userId  movieId  rating\n", "0     509     7347     3.0\n", "1     326    71462     4.0\n", "2      57     2115     3.0\n", "3     610     1127     4.0\n", "4     462     2409     2.0 \n", "\n", "Training data shape: (80668, 3)\n", "Training data columns: ['userId', 'movieId', 'rating']\n", "\n", "Loading test data from ratings_test.csv...\n", "\n", "   userId  movieId\n", "0     432    77866\n", "1     288      474\n", "2     599     4351\n", "3      42     2987\n", "4      75     1610 \n", "\n", "Test data shape: (20168, 2)\n", "Test data columns: ['userId', 'movieId']\n", "\n", "Test data has ratings: False\n", "Creating user-item matrix...\n", "User-Item Matrix shape: (610, 8983)\n", "Number of users: 610\n", "Number of movies: 8983\n", "Computing item similarity matrix (cosine)...\n", "Model training completed!\n", "Generating predictions...\n", "\n", "Processing prediction 1/20168\n", "Processing prediction 1001/20168\n", "Processing prediction 2001/20168\n", "Processing prediction 3001/20168\n", "Processing prediction 4001/20168\n", "Processing prediction 5001/20168\n", "Processing prediction 6001/20168\n", "Processing prediction 7001/20168\n", "Processing prediction 8001/20168\n", "Processing prediction 9001/20168\n", "Processing prediction 10001/20168\n", "Processing prediction 11001/20168\n", "Processing prediction 12001/20168\n", "Processing prediction 13001/20168\n", "Processing prediction 14001/20168\n", "Processing prediction 15001/20168\n", "Processing prediction 16001/20168\n", "Processing prediction 17001/20168\n", "Processing prediction 18001/20168\n", "Processing prediction 19001/20168\n", "Processing prediction 20001/20168\n", "Predictions completed\n", "Predictions saved to predictions.csv\n"]}], "source": ["main()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pCDponWIJxSD"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}