{"cells": [{"cell_type": "code", "execution_count": 2, "id": "eca26662-af11-4a78-abf8-001e41c77dd5", "metadata": {}, "outputs": [], "source": ["import yfinance as yf\n", "import numpy as np\n", "import pandas as pd\n", "import seaborn as sns\n", "\n", "import matplotlib.pyplot as plt\n", "from statsmodels.stats.diagnostic import acorr_ljungbox\n", "from sklearn.neural_network import MLPRegressor\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "# Set style for better visualizations\n", "plt.style.use('seaborn-v0_8')  # Using a valid matplotlib style\n", "sns.set_theme()  # This sets the seaborn theme properly\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c8acbc87-35a6-4507-b546-bf3ae3242b4c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (3768, 29)\n", "\n", "First few rows:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>tempmax</th>\n", "      <th>tempmin</th>\n", "      <th>temp</th>\n", "      <th>feelslikemax</th>\n", "      <th>feelslikemin</th>\n", "      <th>feelslike</th>\n", "      <th>humidity</th>\n", "      <th>precip</th>\n", "      <th>precipprob</th>\n", "      <th>...</th>\n", "      <th>sealevelpressure</th>\n", "      <th>cloudcover</th>\n", "      <th>visibility</th>\n", "      <th>solarradiation</th>\n", "      <th>uvindex</th>\n", "      <th>sunrise</th>\n", "      <th>sunset</th>\n", "      <th>moonphase</th>\n", "      <th>conditions</th>\n", "      <th>source</th>\n", "    </tr>\n", "    <tr>\n", "      <th>datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-01</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>3.9</td>\n", "      <td>2.4</td>\n", "      <td>3.1</td>\n", "      <td>-1.4</td>\n", "      <td>-3.5</td>\n", "      <td>-2.9</td>\n", "      <td>82.7</td>\n", "      <td>10.842</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1023.9</td>\n", "      <td>89.4</td>\n", "      <td>8.0</td>\n", "      <td>20.3</td>\n", "      <td>1</td>\n", "      <td>2015-01-01T07:29:12</td>\n", "      <td>2015-01-01T16:45:49</td>\n", "      <td>0.37</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>4.9</td>\n", "      <td>0.6</td>\n", "      <td>2.8</td>\n", "      <td>1.7</td>\n", "      <td>-3.6</td>\n", "      <td>-1.4</td>\n", "      <td>68.2</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1027.4</td>\n", "      <td>73.8</td>\n", "      <td>10.5</td>\n", "      <td>66.3</td>\n", "      <td>3</td>\n", "      <td>2015-01-02T07:29:19</td>\n", "      <td>2015-01-02T16:46:40</td>\n", "      <td>0.40</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-03</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>6.0</td>\n", "      <td>-0.9</td>\n", "      <td>3.5</td>\n", "      <td>5.4</td>\n", "      <td>-3.6</td>\n", "      <td>1.4</td>\n", "      <td>68.9</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1023.9</td>\n", "      <td>67.1</td>\n", "      <td>9.8</td>\n", "      <td>58.7</td>\n", "      <td>3</td>\n", "      <td>2015-01-03T07:29:23</td>\n", "      <td>2015-01-03T16:47:32</td>\n", "      <td>0.44</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-04</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>9.0</td>\n", "      <td>4.5</td>\n", "      <td>7.1</td>\n", "      <td>6.1</td>\n", "      <td>1.2</td>\n", "      <td>3.7</td>\n", "      <td>77.7</td>\n", "      <td>5.499</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1014.0</td>\n", "      <td>84.1</td>\n", "      <td>9.0</td>\n", "      <td>58.0</td>\n", "      <td>3</td>\n", "      <td>2015-01-04T07:29:24</td>\n", "      <td>2015-01-04T16:48:26</td>\n", "      <td>0.47</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-05</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>5.9</td>\n", "      <td>2.9</td>\n", "      <td>4.4</td>\n", "      <td>3.5</td>\n", "      <td>-1.4</td>\n", "      <td>1.0</td>\n", "      <td>70.7</td>\n", "      <td>1.623</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1014.2</td>\n", "      <td>71.5</td>\n", "      <td>10.3</td>\n", "      <td>79.3</td>\n", "      <td>3</td>\n", "      <td>2015-01-05T07:29:24</td>\n", "      <td>2015-01-05T16:49:22</td>\n", "      <td>0.50</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 29 columns</p>\n", "</div>"], "text/plain": ["                         name  tempmax  tempmin  temp  feelslikemax  \\\n", "datetime                                                              \n", "2015-01-01  İstanbul, Türkiye      3.9      2.4   3.1          -1.4   \n", "2015-01-02  İstanbul, Türkiye      4.9      0.6   2.8           1.7   \n", "2015-01-03  İstanbul, Türkiye      6.0     -0.9   3.5           5.4   \n", "2015-01-04  İstanbul, Türkiye      9.0      4.5   7.1           6.1   \n", "2015-01-05  İstanbul, Türkiye      5.9      2.9   4.4           3.5   \n", "\n", "            feelslikemin  feelslike  humidity  precip  precipprob  ...  \\\n", "datetime                                                           ...   \n", "2015-01-01          -3.5       -2.9      82.7  10.842         100  ...   \n", "2015-01-02          -3.6       -1.4      68.2   0.000           0  ...   \n", "2015-01-03          -3.6        1.4      68.9   0.000           0  ...   \n", "2015-01-04           1.2        3.7      77.7   5.499         100  ...   \n", "2015-01-05          -1.4        1.0      70.7   1.623         100  ...   \n", "\n", "            sealevelpressure cloudcover  visibility  solarradiation  uvindex  \\\n", "datetime                                                                       \n", "2015-01-01            1023.9       89.4         8.0            20.3        1   \n", "2015-01-02            1027.4       73.8        10.5            66.3        3   \n", "2015-01-03            1023.9       67.1         9.8            58.7        3   \n", "2015-01-04            1014.0       84.1         9.0            58.0        3   \n", "2015-01-05            1014.2       71.5        10.3            79.3        3   \n", "\n", "                        sunrise               sunset  moonphase  \\\n", "datetime                                                          \n", "2015-01-01  2015-01-01T07:29:12  2015-01-01T16:45:49       0.37   \n", "2015-01-02  2015-01-02T07:29:19  2015-01-02T16:46:40       0.40   \n", "2015-01-03  2015-01-03T07:29:23  2015-01-03T16:47:32       0.44   \n", "2015-01-04  2015-01-04T07:29:24  2015-01-04T16:48:26       0.47   \n", "2015-01-05  2015-01-05T07:29:24  2015-01-05T16:49:22       0.50   \n", "\n", "                        conditions  source  \n", "datetime                                    \n", "2015-01-01  Rain, Partially cloudy     obs  \n", "2015-01-02        Partially cloudy     obs  \n", "2015-01-03        Partially cloudy     obs  \n", "2015-01-04  Rain, Partially cloudy     obs  \n", "2015-01-05  Rain, Partially cloudy     obs  \n", "\n", "[5 rows x 29 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Read the dataset\n", "df = pd.read_csv('data_2015_2025.csv')\n", "\n", "# Convert datetime to proper format\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "\n", "# Set datetime as index\n", "df.set_index('datetime', inplace=True)\n", "\n", "print(\"Dataset shape:\", df.shape)\n", "print(\"\\nFirst few rows:\")\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": 8, "id": "3054418b-6f4e-4dfc-b696-bb667c8ba455", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimal delay selected: 1\n"]}], "source": ["# # Step 1: Download S&P 500 data\n", "# sp500_data = yf.download(\"^GSPC\", start=\"2010-01-01\", progress=False)\n", "# sp500_close = sp500_data[\"Close\"].dropna()\n", "\n", "# # Step 2: First difference of closing values\n", "# sp500_diff = sp500_close.diff().dropna()\n", "\n", "# Step 3: Scale first differences\n", "scaler = MinMaxScaler()\n", "# Select only numeric columns for scaling\n", "df_numeric = df.select_dtypes(include=np.number)\n", "df_scaled = scaler.fit_transform(df_numeric).flatten()\n", "\n", "# Step 4: Split into train and test with buffer for lags\n", "forecast_horizon = 21\n", "max_lag = 10\n", "train_diff = df_scaled[:-forecast_horizon]\n", "test_diff = df_scaled[-(forecast_horizon ):]  # buffer to allow lagging\n", "\n", "# Step 5: Ljung-Box test to determine optimal delay\n", "lb_pvalues = []\n", "for lag in range(1, max_lag + 1):\n", "    lb_test = acorr_ljungbox(train_diff, lags=[lag], return_df=True)\n", "    lb_pvalues.append(lb_test['lb_pvalue'].iloc[0])\n", "optimal_delay = next((i + 1 for i, p in enumerate(lb_pvalues) if p > 0.05), 1)\n", "print(f\"Optimal delay selected: {optimal_delay}\")\n", "\n", "# Step 6: Function to create lagged data\n", "def create_lagged_data(series, lag):\n", "    X, y = [], []\n", "    for i in range(lag, len(series)):\n", "        X.append(series[i - lag:i])\n", "        y.append(series[i])\n", "    return np.array(X), np.array(y)\n", "\n", "# Step 7: Prepare data for training and testing\n", "X_train, y_train = create_lagged_data(train_diff, optimal_delay)\n", "X_test, y_test = create_lagged_data(test_diff, optimal_delay)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "c9500fc2-3b00-46a6-ae95-bc466721253e", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "could not convert string to float: 'İstanbul, Türkiye'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 11\u001b[0m\n\u001b[1;32m      9\u001b[0m scaler \u001b[38;5;241m=\u001b[39m MinMaxScaler()\n\u001b[1;32m     10\u001b[0m \u001b[38;5;66;03m# sp500_diff_scaled = scaler.fit_transform(sp500_diff.values.reshape(-1, 1)).flatten()\u001b[39;00m\n\u001b[0;32m---> 11\u001b[0m df_scaled \u001b[38;5;241m=\u001b[39m \u001b[43mscaler\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit_transform\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalues\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreshape\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mflatten()\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m# Step 4: Split into train and test with buffer for lags\u001b[39;00m\n\u001b[1;32m     14\u001b[0m forecast_horizon \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m21\u001b[39m\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/sklearn/utils/_set_output.py:319\u001b[0m, in \u001b[0;36m_wrap_method_output.<locals>.wrapped\u001b[0;34m(self, X, *args, **kwargs)\u001b[0m\n\u001b[1;32m    317\u001b[0m \u001b[38;5;129m@wraps\u001b[39m(f)\n\u001b[1;32m    318\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrapped\u001b[39m(\u001b[38;5;28mself\u001b[39m, X, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m--> 319\u001b[0m     data_to_wrap \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    320\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data_to_wrap, \u001b[38;5;28mtuple\u001b[39m):\n\u001b[1;32m    321\u001b[0m         \u001b[38;5;66;03m# only wrap the first output for cross decomposition\u001b[39;00m\n\u001b[1;32m    322\u001b[0m         return_tuple \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    323\u001b[0m             _wrap_data_with_container(method, data_to_wrap[\u001b[38;5;241m0\u001b[39m], X, \u001b[38;5;28mself\u001b[39m),\n\u001b[1;32m    324\u001b[0m             \u001b[38;5;241m*\u001b[39mdata_to_wrap[\u001b[38;5;241m1\u001b[39m:],\n\u001b[1;32m    325\u001b[0m         )\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/sklearn/base.py:918\u001b[0m, in \u001b[0;36mTransformerMixin.fit_transform\u001b[0;34m(self, X, y, **fit_params)\u001b[0m\n\u001b[1;32m    903\u001b[0m         warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m    904\u001b[0m             (\n\u001b[1;32m    905\u001b[0m                 \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThis object (\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m) has a `transform`\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    913\u001b[0m             \u001b[38;5;167;01mUserWarning\u001b[39;00m,\n\u001b[1;32m    914\u001b[0m         )\n\u001b[1;32m    916\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m y \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    917\u001b[0m     \u001b[38;5;66;03m# fit method of arity 1 (unsupervised transformation)\u001b[39;00m\n\u001b[0;32m--> 918\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mfit_params\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mtransform(X)\n\u001b[1;32m    919\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    920\u001b[0m     \u001b[38;5;66;03m# fit method of arity 2 (supervised transformation)\u001b[39;00m\n\u001b[1;32m    921\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfit(X, y, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mfit_params)\u001b[38;5;241m.\u001b[39mtransform(X)\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/sklearn/preprocessing/_data.py:447\u001b[0m, in \u001b[0;36mMinMaxScaler.fit\u001b[0;34m(self, X, y)\u001b[0m\n\u001b[1;32m    445\u001b[0m \u001b[38;5;66;03m# Reset internal state before fitting\u001b[39;00m\n\u001b[1;32m    446\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reset()\n\u001b[0;32m--> 447\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpartial_fit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/sklearn/base.py:1389\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[0;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1382\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[1;32m   1384\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[1;32m   1385\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[1;32m   1386\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[1;32m   1387\u001b[0m     )\n\u001b[1;32m   1388\u001b[0m ):\n\u001b[0;32m-> 1389\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfit_method\u001b[49m\u001b[43m(\u001b[49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/sklearn/preprocessing/_data.py:487\u001b[0m, in \u001b[0;36mMinMaxScaler.partial_fit\u001b[0;34m(self, X, y)\u001b[0m\n\u001b[1;32m    484\u001b[0m xp, _ \u001b[38;5;241m=\u001b[39m get_namespace(X)\n\u001b[1;32m    486\u001b[0m first_pass \u001b[38;5;241m=\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn_samples_seen_\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 487\u001b[0m X \u001b[38;5;241m=\u001b[39m \u001b[43mvalidate_data\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    488\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    489\u001b[0m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    490\u001b[0m \u001b[43m    \u001b[49m\u001b[43mreset\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfirst_pass\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    491\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m_array_api\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msupported_float_dtypes\u001b[49m\u001b[43m(\u001b[49m\u001b[43mxp\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    492\u001b[0m \u001b[43m    \u001b[49m\u001b[43mensure_all_finite\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mallow-nan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    493\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    495\u001b[0m data_min \u001b[38;5;241m=\u001b[39m _array_api\u001b[38;5;241m.\u001b[39m_nanmin(X, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m, xp\u001b[38;5;241m=\u001b[39mxp)\n\u001b[1;32m    496\u001b[0m data_max \u001b[38;5;241m=\u001b[39m _array_api\u001b[38;5;241m.\u001b[39m_nanmax(X, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m, xp\u001b[38;5;241m=\u001b[39mxp)\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/sklearn/utils/validation.py:2944\u001b[0m, in \u001b[0;36mvalidate_data\u001b[0;34m(_estimator, X, y, reset, validate_separately, skip_check_array, **check_params)\u001b[0m\n\u001b[1;32m   2942\u001b[0m         out \u001b[38;5;241m=\u001b[39m X, y\n\u001b[1;32m   2943\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m no_val_X \u001b[38;5;129;01mand\u001b[39;00m no_val_y:\n\u001b[0;32m-> 2944\u001b[0m     out \u001b[38;5;241m=\u001b[39m \u001b[43mcheck_array\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minput_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mX\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mcheck_params\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2945\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m no_val_X \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m no_val_y:\n\u001b[1;32m   2946\u001b[0m     out \u001b[38;5;241m=\u001b[39m _check_y(y, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mcheck_params)\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/sklearn/utils/validation.py:1055\u001b[0m, in \u001b[0;36mcheck_array\u001b[0;34m(array, accept_sparse, accept_large_sparse, dtype, order, copy, force_writeable, force_all_finite, ensure_all_finite, ensure_non_negative, ensure_2d, allow_nd, ensure_min_samples, ensure_min_features, estimator, input_name)\u001b[0m\n\u001b[1;32m   1053\u001b[0m         array \u001b[38;5;241m=\u001b[39m xp\u001b[38;5;241m.\u001b[39mastype(array, dtype, copy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[1;32m   1054\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1055\u001b[0m         array \u001b[38;5;241m=\u001b[39m \u001b[43m_asarray_with_order\u001b[49m\u001b[43m(\u001b[49m\u001b[43marray\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43morder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43morder\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mxp\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mxp\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1056\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ComplexWarning \u001b[38;5;28;01mas\u001b[39;00m complex_warning:\n\u001b[1;32m   1057\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1058\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mComplex data not supported\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(array)\n\u001b[1;32m   1059\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mcomplex_warning\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/sklearn/utils/_array_api.py:839\u001b[0m, in \u001b[0;36m_asarray_with_order\u001b[0;34m(array, dtype, order, copy, xp, device)\u001b[0m\n\u001b[1;32m    837\u001b[0m     array \u001b[38;5;241m=\u001b[39m numpy\u001b[38;5;241m.\u001b[39marray(array, order\u001b[38;5;241m=\u001b[39morder, dtype\u001b[38;5;241m=\u001b[39mdtype)\n\u001b[1;32m    838\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 839\u001b[0m     array \u001b[38;5;241m=\u001b[39m \u001b[43mnumpy\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43masarray\u001b[49m\u001b[43m(\u001b[49m\u001b[43marray\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43morder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43morder\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    841\u001b[0m \u001b[38;5;66;03m# At this point array is a NumPy ndarray. We convert it to an array\u001b[39;00m\n\u001b[1;32m    842\u001b[0m \u001b[38;5;66;03m# container that is consistent with the input's namespace.\u001b[39;00m\n\u001b[1;32m    843\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m xp\u001b[38;5;241m.\u001b[39masarray(array)\n", "\u001b[0;31mValueError\u001b[0m: could not convert string to float: 'İstanbul, Türkiye'"]}], "source": ["\n", "# Step 8: Train MLP model\n", "model = MLPRegressor(hidden_layer_sizes=(10,), activation='relu', solver='adam',\n", "                     max_iter=500, random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "# Step 9: Predict and reverse scale manually\n", "y_pred_scaled = model.predict(X_test)\n", "min_val, max_val = scaler.data_min_[0], scaler.data_max_[0]\n", "y_pred_diff = y_pred_scaled * (max_val - min_val) + min_val\n", "y_test_diff = y_test * (max_val - min_val) + min_val\n", "\n", "# Step 10: Reconstruct actual levels\n", "# The original code uses sp500_close for reconstruction, which is commented out.\n", "# We need to use the original df data for reconstruction.\n", "# Find the last known level of the first numeric column in the original df\n", "last_known_index = len(df_numeric) - len(test_diff)\n", "last_known_level = df_numeric.iloc[last_known_index, 0] # Using the first numeric column\n", "last_known_level_vector = np.repeat(last_known_level, len(y_test))\n", "predicted_levels = np.cumsum(y_pred_diff) + last_known_level_vector\n", "actual_levels = np.cumsum(y_test_diff) + last_known_level_vector\n", "#calculate mape\n", "mape = np.mean(np.abs((actual_levels - predicted_levels) / actual_levels)) * 100\n", "print(f\"MAPE: {mape:.2f}%\")"]}, {"cell_type": "code", "execution_count": null, "id": "3b826f19-88b5-49e2-b2fc-31586303beb5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}