# -*- coding: utf-8 -*-
"""NAR.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1MLo_2ba51IEqpjo8iXQG9bnB2sM1FC4z
"""

import yfinance as yf
import numpy as np
import pandas as pd
import seaborn as sns

import matplotlib.pyplot as plt
from statsmodels.stats.diagnostic import acorr_ljungbox
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score

# Set style for better visualizations
plt.style.use('seaborn-v0_8')  # Using a valid matplotlib style
sns.set_theme()  # This sets the seaborn theme properly

# Read the dataset
df = pd.read_csv('data_2015_2025.csv')

# Convert datetime to proper format
df['datetime'] = pd.to_datetime(df['datetime'])

# Set datetime as index
df.set_index('datetime', inplace=True)

print("Dataset shape:", df.shape)
print("\nFirst few rows:")
df.head()

# # Step 1: Download S&P 500 data
# sp500_data = yf.download("^GSPC", start="2010-01-01", progress=False)
# sp500_close = sp500_data["Close"].dropna()

# # Step 2: First difference of closing values
# sp500_diff = sp500_close.diff().dropna()

# Step 3: Scale first differences
scaler = MinMaxScaler()
# Select only numeric columns for scaling
df_numeric = df.select_dtypes(include=np.number)
df_scaled = scaler.fit_transform(df_numeric).flatten()

# Step 4: Split into train and test with buffer for lags
forecast_horizon = 21
max_lag = 10
train_diff = df_scaled[:-forecast_horizon]
test_diff = df_scaled[-(forecast_horizon ):]  # buffer to allow lagging

# Step 5: Ljung-Box test to determine optimal delay
lb_pvalues = []
for lag in range(1, max_lag + 1):
    lb_test = acorr_ljungbox(train_diff, lags=[lag], return_df=True)
    lb_pvalues.append(lb_test['lb_pvalue'].iloc[0])
optimal_delay = next((i + 1 for i, p in enumerate(lb_pvalues) if p > 0.05), 1)
print(f"Optimal delay selected: {optimal_delay}")

# Step 6: Function to create lagged data
def create_lagged_data(series, lag):
    X, y = [], []
    for i in range(lag, len(series)):
        X.append(series[i - lag:i])
        y.append(series[i])
    return np.array(X), np.array(y)

# Step 7: Prepare data for training and testing
X_train, y_train = create_lagged_data(train_diff, optimal_delay)
X_test, y_test = create_lagged_data(test_diff, optimal_delay)

# Step 8: Train MLP model
model = MLPRegressor(hidden_layer_sizes=(10,), activation='relu', solver='adam',
                     max_iter=500, random_state=42)
model.fit(X_train, y_train)

# Step 9: Predict and reverse scale manually
y_pred_scaled = model.predict(X_test)
min_val, max_val = scaler.data_min_[0], scaler.data_max_[0]
y_pred_diff = y_pred_scaled * (max_val - min_val) + min_val
y_test_diff = y_test * (max_val - min_val) + min_val

# Step 10: Reconstruct actual levels
# The original code uses sp500_close for reconstruction, which is commented out.
# We need to use the original df data for reconstruction.
# Find the last known level of the first numeric column in the original df
last_known_index = len(df_numeric) - len(test_diff)
last_known_level = df_numeric.iloc[last_known_index, 0] # Using the first numeric column
last_known_level_vector = np.repeat(last_known_level, len(y_test))
predicted_levels = np.cumsum(y_pred_diff) + last_known_level_vector
actual_levels = np.cumsum(y_test_diff) + last_known_level_vector
#calculate mape
mape = np.mean(np.abs((actual_levels - predicted_levels) / actual_levels)) * 100
print(f"MAPE: {mape:.2f}%")

from sklearn.metrics import mean_squared_error, mean_absolute_error

# Calculate metrics
mse = mean_squared_error(actual_levels, predicted_levels)
mae = mean_absolute_error(actual_levels, predicted_levels)
rmse = np.sqrt(mse)
mape = np.mean(np.abs((actual_levels - predicted_levels) / actual_levels)) * 100

print(f"MSE: {mse:.2f}")
print(f"MAE: {mae:.2f}")
print(f"RMSE: {rmse:.2f}")
print(f"MAPE: {mape:.2f}%")

pred_df = pd.DataFrame({'Predictions': predicted_levels, 'TrueValues': actual_levels})
display(pred_df)



# 7. VISUALIZE TRAINING AND PREDICTIONS
print("\n=== VISUALIZATION ===")

# Plot training history
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

# Loss
ax1.plot(history.history['loss'], label='Training Loss')
ax1.plot(history.history['val_loss'], label='Validation Loss')
ax1.set_title('Model Loss')
ax1.set_xlabel('Epoch')
ax1.set_ylabel('Loss')
ax1.legend()
ax1.grid(True, alpha=0.3)

# MAE
ax2.plot(history.history['mae'], label='Training MAE')
ax2.plot(history.history['val_mae'], label='Validation MAE')
ax2.set_title('Model MAE')
ax2.set_xlabel('Epoch')
ax2.set_ylabel('MAE')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Plot predictions vs actual
plt.figure(figsize=(15, 8))

