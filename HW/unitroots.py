# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from arch.unitroot import ADF, KPSS, PhillipsPerron, DFGLS
from statsmodels.graphics.tsaplots import plot_acf

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_theme()

# Read and prepare the data
df = pd.read_csv('../Dataset/data_01.csv')
df['datetime'] = pd.to_datetime(df['datetime'])
df.set_index('datetime', inplace=True)

# Fill any missing values with interpolation
df['tempmax'] = df['tempmax'].interpolate(method='linear')

# Plot the original series
plt.figure(figsize=(15, 6))
plt.plot(df.index, df['tempmax'])
plt.title('Daily Precipitation in Istanbul')
plt.xlabel('Date')
plt.ylabel('Precipitation (mm)')
plt.grid(True)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Calculate and plot rolling statistics
rolling_mean = df['tempmax'].rolling(window=30).mean()
rolling_std = df['tempmax'].rolling(window=30).std()

plt.figure(figsize=(15, 6))
plt.plot(df.index, df['tempmax'], label='Original')
plt.plot(df.index, rolling_mean, label='Rolling Mean (30 days)')
plt.plot(df.index, rolling_std, label='Rolling Std (30 days)')
plt.title('Rolling Statistics of Precipitation')
plt.xlabel('Date')
plt.ylabel('Precipitation (mm)')
plt.legend()
plt.grid(True)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Transformations
df['log_tempmax'] = np.log(df['tempmax'] + 0.1)
df['diff_tempmax'] = df['tempmax'].diff().dropna()
df['seasonal_diff_tempmax'] = df['tempmax'].diff(periods=30).dropna()

series_dict = {
    'Original': df['tempmax'],
    'Log': df['log_tempmax'].dropna(),
    'First Difference': df['diff_tempmax'].dropna(),
    'Seasonal Difference': df['seasonal_diff_tempmax'].dropna()
}

for name, series in series_dict.items():
    print(f"\n===== {name} Series =====")
    print("\nADF Test:")
    adf = ADF(series, trend='c', max_lags=10, method='aic')
    print(adf.summary().as_text())
    print("ADF critical values:", adf.critical_values)
    if hasattr(adf, 'regression'):
        reg_res = adf.regression
        print(reg_res.summary().as_text())
        plot_acf(reg_res.resid, lags=60)
        plt.title(f'ACF of ADF Residuals: {name}')
        plt.show()
    print("\nKPSS Test:")
    kpss = KPSS(series, trend='c')
    print(kpss.summary().as_text())
    print('KPSS critical values:', kpss._critical_values)
    print("\nPhillips-Perron Test:")
    pp = PhillipsPerron(series, trend='c')
    print(pp.summary().as_text())
    print('PhillipsPerron critical values:', pp._critical_values)
    print("\nDFGLS Test:")
    dfgls = DFGLS(series, trend='c', max_lags=10, method='aic')
    print(dfgls.summary().as_text())
    if hasattr(dfgls, 'regression'):
        reg_res = dfgls.regression
        print(reg_res.summary().as_text())
        plot_acf(reg_res.resid, lags=60)
        plt.title(f'ACF of DFGLS Residuals: {name}')
        plt.show()
    print('DFGLS critical values:', dfgls.critical_values)
