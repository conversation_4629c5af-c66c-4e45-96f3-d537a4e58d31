#!/usr/bin/env python
# coding: utf-8

# In[4]:


pip install yfinance


# # 1. soru : VERi SETİ TEMİNİ

# In[1]:


import warnings
warnings.filterwarnings("ignore")
import yfinance as yf


# In[2]:


# tüm veri için
google = yf.Ticker("GOOGL")
#google_fiyatlar = google.history(period="max")
google_fiyatlar = google.history(start="2017-01-01", end="2022-07-01")


# In[3]:


## 1. veri setimiz için yfinance bağlanıp GOOGLE stock verileri aldık. Veri setimiz günlük veri içermektedir. Son 5 yıl alındı.

google_fiyatlar.head()


# In[4]:


pip install evds


# In[14]:


from evds import evdsAPI

evds = evdsAPI('lXebviNeQl')


# In[15]:


## 2. Veri olarak merkez bankasından aldığımız api ile dolar kurunu aldım. Günlük veri olduğu için son 5 yıl.
dolar_kuru = evds.get_data(['TP.DK.USD.A.YTL'], startdate='01-01-2017', enddate='01-07-2022')


# In[16]:


dolar_kuru.head()


# In[13]:


pip install pandas_datareader


# In[1]:


from pandas_datareader import wb


# In[2]:


##3.veriseti olarak Türkiyenin yıllık GSYH bilgilerini aldık. Veri yıllık bir veri son 71 yılı içerecek şekilde aldım.
gsyh = wb.download(indicator='NY.GDP.PCAP.KD', country=['TR'], start=1950, end=2021)


# In[3]:


gsyh.head()


# In[11]:


gsyh.tail()


# In[12]:


### veri setlerinin inceleyelim.
## null değer kontrolü


# In[12]:


google_fiyatlar.isna().sum()


# In[13]:


dolar_kuru.isna().sum()


# In[14]:


gsyh.isna().sum()


# In[4]:


import numpy as np 
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
from hdbcli import dbapi
import seaborn as sns
get_ipython().run_line_magic('matplotlib', 'inline')
from statsmodels.tsa.seasonal import seasonal_decompose


# In[127]:


dolar_kuru = dolar_kuru.reset_index()


# In[128]:


dolar_kuru = dolar_kuru.set_index('Tarih')


# In[129]:


dolar_kuru= dolar_kuru.reset_index()


# In[130]:


dolar_kuru.head()


# In[135]:


Date = dolar_kuru["Tarih"]
Dolar_kuru = dolar_kuru["TP_DK_USD_A_YTL"]
plt.plot(Date, Dolar_kuru)
plt.title('Dolar kuru Vs Date')
plt.xlabel('Date')
plt.ylabel('Dolar kuru')
plt.show()


# In[ ]:


### veri setinde pozitif yönlü trend görünmektedir. 


# In[5]:


gsyh = gsyh.reset_index()


# In[39]:


Year = gsyh["year"]
GSYH = gsyh["NY.GDP.PCAP.KD"]
plt.plot(Year, GSYH)
plt.title('GSYH Vs Year')
plt.xlabel('Year')
plt.ylabel('GSYH')
plt.show()


# In[ ]:


### veri setinde negatif yönlü trend görünmektedir. 


# In[106]:


google_fiyatlar = google_fiyatlar.reset_index()


# In[41]:


Date = google_fiyatlar["Date"]
Close_Stock = google_fiyatlar["Close"]
plt.plot(Date, Close_Stock)
plt.title('Close_Stock Vs Date')
plt.xlabel('Date')
plt.ylabel('Close_Stock')
plt.show()


# In[ ]:


### veri setinde pozitif yönlü trend görünmektedir. 


# # 2. soru : 3 VERİ SETİ İÇİN ADF VE KPSS  BİRİM KÖK TESTİ

# 1. Data set dolar kuru

# In[35]:


from statsmodels.tsa.stattools import adfuller 


# Artırılmış Dickey Fuller testi (ADF Testi), belirli bir Zaman serisinin durağan olup olmadığını test etmek için kullanılan yaygın bir istatistiksel testtir. Bir serinin durağanlığını analiz etmek söz konusu olduğunda en sık kullanılan istatistiksel testlerden biridir. Durağanlık zaman serilerinde çok önemli bir faktördür. ARIMA zaman serisi tahmininde ilk adım, seriyi durağan hale getirmek için gereken fark sayısını belirlemektir.
# 
# Burada hatırlanması gereken önemli bir nokta şudur: Sıfır hipotezi, birim kökün, yani α=1'in varlığını varsaydığından, sıfır hipotezini reddetmek için elde edilen p-değeri anlamlılık seviyesinden (örneğin 0,05 veya 0,01) küçük olmalıdır. . Buradan serinin durağan olduğu sonucuna varılır.

# In[17]:


dolar_kuru.dropna(inplace =True)


# In[18]:


dolar_kuru = dolar_kuru.reset_index()


# In[19]:


dolar_kuru = dolar_kuru.drop(columns='index')


# In[20]:


dolar_kuru['Tarih'] = pd.to_datetime(dolar_kuru['Tarih'])


# In[21]:


dolar_kuru = dolar_kuru.set_index('Tarih')


# In[22]:


dolar_kuru.head()


# In[44]:


df_resample = dolar_kuru.resample('D').sum()


# In[45]:


# ADF Test before differencing

from statsmodels.tsa.stattools import adfuller 
df_resample = dolar_kuru.resample('D').sum()
adf = adfuller(df_resample['TP_DK_USD_A_YTL'],365)
print("\nStatistics analysis\n")
print("Statistic Test : " , adf[0])
print("p-value : " , adf[1])
print("# n_lags : " , adf[2])
print("No of observation: " , adf[3])
for key,value in adf[4].items():
    print(f" critical value {key} : {value}")


# In[ ]:


## Burada statistic test valuenun kritik değerden büyük olduğunu ve p değerinin de anlamlı değerden (0.05) küçük olduğunu fark ettik. Yani zaman serisinin durağan olmadığını söyleyebiliriz.


# In[46]:


# ADF Test after differencing

df_resample = dolar_kuru.resample('D').sum()
df_resample['first_diffkur'] = df_resample['TP_DK_USD_A_YTL'].diff() # first difference 
df_resample.dropna(inplace =True)
adf = adfuller(df_resample['first_diffkur'],365)
print("\nStatistics analysis\n")
print("Statistic Test : " , adf[0])
print("p-value : " , adf[1])
print("# n_lags : " , adf[2])
print("No of observation: " , adf[3])
for key,value in adf[4].items():
    print(f" critical value {key} : {value}")


# In[156]:


df_resample.head()


# 1. farktan sonra serimizin durağan olduğu görülmektedir. Çünkü p-vlaue değerimiz 0.05 ten küçük.

# In[47]:


# plot of time series data for ADF test
fig ,ax = plt.subplots(1,2,figsize=(16,5))
# mean ,std of before difference data 
mean1 = df_resample['TP_DK_USD_A_YTL'].rolling(12).mean()
std1 = df_resample['TP_DK_USD_A_YTL'].rolling(12).std()
ax[0].plot(df_resample['TP_DK_USD_A_YTL'],color ='blue',label = 'original')
ax[0].plot(mean1,color ='red',label = 'mean')
ax[0].plot(std1,color ='black',label = 'std')
ax[0].set_title("Before first diff time series data with mean and std ")
ax[0].set_xlabel("Dolar Kuru")
ax[0].set_ylabel("Date")
# mean ,std  after difference data 
mean2 = df_resample['first_diffkur'].rolling(12).mean()
std2 = df_resample['first_diffkur'].rolling(12).std()
ax[1].plot(df_resample['first_diffkur'],color ='blue',label = 'original')
ax[1].plot(mean2,color ='red',label = 'mean')
ax[1].plot(std2,color ='black',label = 'std')
ax[1].set_title("After first diff time series data with mean and std ")
ax[1].set_xlabel("Dolar Kuru")
ax[1].set_ylabel("Day")
plt.legend(loc ='best')
plt.show()


# In[23]:


import statsmodels.api as sm

#perform KPSS test
sm.tsa.stattools.kpss(dolar_kuru, regression='ct')


# kpss test istatistik : 0.80
#     p-value : 0.01
#         p-value 0.01 
# p-value < 0.05 hipotezi reddedemeyiz. Bu, zaman serisinin trend durağan olduğunu varsayabileceğimiz anlamına gelir.

# 2. Data set GSYH

# In[6]:


gsyh['year'] = pd.to_datetime(gsyh['year'])


# In[7]:


gsyh = gsyh.set_index('year')


# In[8]:


gsyh.head()


# In[9]:


gsyh.drop(columns = 'country' , inplace =True)


# In[10]:


gsyh.head()


# In[66]:


# ADF Test before differencing

from statsmodels.tsa.stattools import adfuller 
df_resample = gsyh.resample('Y').sum()
adf = adfuller(df_resample['NY.GDP.PCAP.KD'],1)
print("\nStatistics analysis\n")
print("Statistic Test : " , adf[0])
print("p-value : " , adf[1])
print("# n_lags : " , adf[2])
print("No of observation: " , adf[3])
for key,value in adf[4].items():
    print(f" critical value {key} : {value}")


# In[69]:


# ADF Test after differencing

df_resample = gsyh.resample('Y').sum()
df_resample['first_diffgsyh'] = df_resample['NY.GDP.PCAP.KD'].diff() # first difference 
df_resample.dropna(inplace =True)
adf = adfuller(df_resample['first_diffgsyh'],12)
print("\nStatistics analysis\n")
print("Statistic Test : " , adf[0])
print("p-value : " , adf[1])
print("# n_lags : " , adf[2])
print("No of observation: " , adf[3])
for key,value in adf[4].items():
    print(f" critical value {key} : {value}")


# In[70]:


# plot of time series data for ADF test
fig ,ax = plt.subplots(1,2,figsize=(16,5))
# mean ,std of before difference data 
mean1 = df_resample['NY.GDP.PCAP.KD'].rolling(12).mean()
std1 = df_resample['NY.GDP.PCAP.KD'].rolling(12).std()
ax[0].plot(df_resample['NY.GDP.PCAP.KD'],color ='blue',label = 'original')
ax[0].plot(mean1,color ='red',label = 'mean')
ax[0].plot(std1,color ='black',label = 'std')
ax[0].set_title("Before first diff time series data with mean and std ")
ax[0].set_xlabel("GSYH")
ax[0].set_ylabel("Year")
# mean ,std  after difference data 
mean2 = df_resample['first_diffgsyh'].rolling(12).mean()
std2 = df_resample['first_diffgsyh'].rolling(12).std()
ax[1].plot(df_resample['first_diffgsyh'],color ='blue',label = 'original')
ax[1].plot(mean2,color ='red',label = 'mean')
ax[1].plot(std2,color ='black',label = 'std')
ax[1].set_title("After first diff time series data with mean and std ")
ax[1].set_xlabel("GSYH")
ax[1].set_ylabel("Year")
plt.legend(loc ='best')
plt.show()


# In[13]:


sm.tsa.stattools.kpss(gsyh, regression='ct')


# In[ ]:


kpss test istatistik : 0.80 p-value : 0.01 p-value 0.01 p-value < 0.05 hipotezi reddedemeyiz. Bu, zaman serisinin trend durağan olduğunu varsayabileceğimiz anlamına gelir.


# 3. Data set Google Stock

# In[101]:


google_fiyatlar.head()


# In[107]:


google_fiyatlar['Date'] = pd.to_datetime(google_fiyatlar['Date'])


# In[108]:


google_fiyatlar = google_fiyatlar[["Date","Close"]]


# In[109]:


google_fiyatlar.head()


# In[111]:


google_fiyatlar = google_fiyatlar.set_index("Date")


# In[112]:


# ADF Test before differencing

df_resample = google_fiyatlar.resample('D').sum()
adf = adfuller(df_resample['Close'],365)
print("\nStatistics analysis\n")
print("Statistic Test : " , adf[0])
print("p-value : " , adf[1])
print("# n_lags : " , adf[2])
print("No of observation: " , adf[3])
for key,value in adf[4].items():
    print(f" critical value {key} : {value}")


# In[113]:


# ADF Test after differencing
df_resample = google_fiyatlar.resample('D').sum()
df_resample['first_diffstock'] = df_resample['Close'].diff() # first difference 
df_resample.dropna(inplace =True)
adf = adfuller(df_resample['first_diffstock'],365)
print("\nStatistics analysis\n")
print("Statistic Test : " , adf[0])
print("p-value : " , adf[1])
print("# n_lags : " , adf[2])
print("No of observation: " , adf[3])
for key,value in adf[4].items():
    print(f" critical value {key} : {value}")


# In[114]:


# plot of time series data for ADF test
fig ,ax = plt.subplots(1,2,figsize=(16,5))
# mean ,std of before difference data 
mean1 = df_resample['Close'].rolling(12).mean()
std1 = df_resample['Close'].rolling(12).std()
ax[0].plot(df_resample['Close'],color ='blue',label = 'original')
ax[0].plot(mean1,color ='red',label = 'mean')
ax[0].plot(std1,color ='black',label = 'std')
ax[0].set_title("Before first diff time series data with mean and std ")
ax[0].set_xlabel("Stock")
ax[0].set_ylabel("Date")
# mean ,std  after difference data 
mean2 = df_resample['first_diffstock'].rolling(12).mean()
std2 = df_resample['first_diffstock'].rolling(12).std()
ax[1].plot(df_resample['first_diffstock'],color ='blue',label = 'original')
ax[1].plot(mean2,color ='red',label = 'mean')
ax[1].plot(std2,color ='black',label = 'std')
ax[1].set_title("After first diff time series data with mean and std ")
ax[1].set_xlabel("Stock")
ax[1].set_ylabel("Date")
plt.legend(loc ='best')
plt.show()


# In[118]:


sm.tsa.stattools.kpss(google_fiyatlar, regression='ct')


# In[ ]:


kpss test istatistik : 1.08 p-value : 0.01 p-value 0.01 p-value < 0.05 hipotezi reddedemeyiz. Bu, zaman serisinin trend durağan olduğunu varsayabileceğimiz anlamına gelir.


# # 3. Johansen eşbütünleşme testi 
# 

# In[121]:


get_ipython().system('pip install johansen')


# In[14]:


from statsmodels.tsa.vector_ar.vecm import coint_johansen


# In[15]:


google_fiyatlar.head()


# In[16]:


google_fiyatlar.reset_index()


# In[17]:


pred = google_fiyatlar["Close"]


# In[18]:


x = google_fiyatlar["Volume"]


# In[19]:


df = google_fiyatlar[["Close", "Volume"]]


# In[27]:


result = coint_johansen(df, 0,1)


# In[33]:


print('**************')
print(result.lr1)
print('---------')
print(result.cvt) # Critical values (90%, 95%, 99%) of maximum eigenvalue statistic.
print('---------')
print(result.cvm)


# # 6. EKK(OLS) 

# Elinizdeki değişkenlerden bir tanesini bağımlı diğerlerini bağımsız değişken olarak belirleyip 
# bağımlı değişkenin bağımsız değişkenlerin gecikmeleri üzerine regresyonunu EKK(OLS)
# tekniğiyle tahmin ederek Rolling forecast yapınız.

# In[34]:


x = google_fiyatlar["Volume"]


# In[35]:


pred = google_fiyatlar["Close"]


# In[41]:


X = sm.add_constant(x)
model = sm.OLS(pred, X).fit()


# In[42]:


summary = model.summary()
print(summary)


# # 7.Kantil regresyon 

# In[37]:


import statsmodels.api as sm
import statsmodels.formula.api as smf


# In[38]:


model = smf.quantreg('pred ~ x', df).fit(q=0.9)
print(model.summary())


# In[ ]:




