library(zoo)
library(readxl)
library(timeSeries)
library(tseries)
library(forecast)
data <- read_excel("dataset_TS.xlsx", sheet = "Konut",col_names = TRUE)
attach(data)
konut<-Konut
ts_konut<-ts(konut, frequency = 12, start = c(2013, 1))
ts_konut<-ts_konut/1000
plot(ts_konut, xlab= "Time (years)", ylab = "sales(1000) ")
#EXPONENTIAL SMOOTHING WITH ALPHA PRODUCED BY R
konut.hw1 <- HoltWinters(ts_konut, beta = FALSE, gamma = FALSE) ;konut.hw1 #(to print output)
plot(konut.hw1)
#SSR one step ahead prediction errors
konut.hw1$SSE
konut.hw1$fitted
#EXPONENTIAL SMOOTHING WITH ALPHA=0.2
konut.hw2 <- HoltWinters(ts_konut, alpha=0.2,beta = FALSE, gamma = FALSE) ;konut.hw2 #(to print output)
plot(konut.hw2)
konut.hw2$SSE

konut.hw2 <- HoltWinters(ts_konut, alpha=0.2,beta = FALSE, gamma = FALSE) ;konut.hw2 #(to print output)
plot(konut.hw2)
konut.hw2$SSE
konut.hw2$fitted
#Holt-Winters with additive seasonality (the default)

konut.hw3 <- HoltWinters(ts_konut) ;konut.hw3 #(to print output)
plot(konut.hw3)
konut.hw3$SSE
konut.hw3$fitted
plot (konut.hw3$fitted)
konut.hw4 <- HoltWinters(ts_konut, seasonal="mult") ;konut.hw4 #(to print output)
plot(konut.hw4)
konut.hw4$SSE
konut.hw4$fitted


#prediction
konut.predict<-predict(konut.hw4, n.ahead = 4*12)
ts.plot(ts_konut, konut.predict, lty = 1:2)
print(konut.predict)
