rm(list = ls())
install.packages("lmtest")
set.seed(2023)
random.order <- sample(1:506, size=506, replace = FALSE)
veri.ser.cor.randomized <- BostonHousing2[random.order,]
library lmtest
library lmtest::
library(lmtest)
set.seed(2023)
random.order <- sample(1:506, size=506, replace = FALSE)
veri.ser.cor.randomized <- BostonHousing2[random.order,]
lmtest::dwtest(model.het)
library(skedastic)
install.packages("skedastic")
lmtest::dwtest(model.het)
install.packages("mlbench")
library(mlbench)
data(BostonHousing2)
BostonHousing2$price <- BostonHousing2$cmedv*1000
BostonHousing2$ln.price <- log(BostonHousing2$price)
set.seed(2023)
random.order <- sample(1:506, size=506, replace = FALSE)
veri.ser.cor.randomized <- BostonHousing2[random.order,]
model.het <- lm(ln.price~dis+crim+nox+ptratio+rm+I(rm^2), data=BostonHousing2)
lmtest::dwtest(model.het)
View(BostonHousing2)
model.het.random <- lm(ln.price~dis+crim+nox+ptratio+rm+I(rm^2),
data=veri.ser.cor.randomized)
lmtest::dwtest(model.het.random)
i1 <- seq(0.3, 0.5, len = 100) - rnorm (100, 0.2, 0.05)
i2 <- seq(0.3, 1, len = 100) - rnorm (100, 0.2, 0.05)
dati = data.frame(i1, i2)
random1 = data.frame(i1=0.6, i2=1)
random2 = data.frame(i1=0.5, i2=NA)
Indic = rbind(dati,random1,random2)
install.packages("Compind")
CI1 = ci_bod(Indic)
library(Compind)
i1 <- seq(0.3, 0.5, len = 100) - rnorm (100, 0.2, 0.05)
i2 <- seq(0.3, 1, len = 100) - rnorm (100, 0.2, 0.05)
dati = data.frame(i1, i2)
random1 = data.frame(i1=0.6, i2=1)
random2 = data.frame(i1=0.5, i2=NA)
Indic = rbind(dati,random1,random2)
CI1 = ci_bod(Indic)
View(CI1)
View(CI1)
View(CI1)
View(CI1)
CI1
View(Indic)
library(zoo)
library(readxl)
library(timeSeries)
library(tseries)
library(forecast)
data(AirPassengers)
AP <- AirPassengers
plot(AP, ylab = "Passengers (1000's)")
data <- read_excel("Airlinedata.xlsx", sheet = "Data",col_names = TRUE)
setwd("G:/Drive'ım/TimeSeriesandForecasting/DataandCodes")
data <- read_excel("Airlinedata.xlsx", sheet = "Data",col_names = TRUE)
passenger<-data$Passengers
class(passenger)
plot(passenger, ylab = "Passengers")
ts_passengers<-ts(passenger, frequency = 4, start = c(2000, 1)) # First Quarter of 2000
class(ts_passengers)
plot(ts_passengers, ylab = "Passengers")
layout(1:3)
plot(aggregate(AP))
boxplot(AP ~ cycle(AP))
layout(1:2)
plot(aggregate(ts_passengers))
boxplot(ts_passengers ~ cycle(ts_passengers))
df <- read_excel("stockdata2.xlsx")
date<- df$date
df2<-df[-c(1)]#removing the first column
Stocks<-zoo(df2, seq(from = as.Date("2016-01-04"), to = as.Date("2022-04-14"), by = 1))
plot(Stocks, xlab="Time")
temp<-ts(df3, frequency = 12, start = c(1850, 1))
data <- read.csv("temperature.csv")
df3<-data[,2]
#print(df3)
#plot(df3,ylab="Global Temperature")
#layout(1:3)
#plot(aggregate(temp))
#boxplot(temp ~ cycle(temp))
temp<-ts(df3, frequency = 12, start = c(1850, 1))
#temp<-ts(df3, frequency = 12, start = c(1850, 1),end=c(2020,1))
New.series <- window(temp, start=c(1970, 1), end=c(2022, 02))
New.time <- time(New.series)
print(New.time)
print(New.series) #extracts the time from a time series object
plot(New.series); abline(reg=lm(New.series ~ New.time))
Temp_dec<-decompose(temp, type ="add")
plot(Temp_dec)
temp_seasonal_component<-Temp_dec$seasonal
temp_trend_component<-Temp_dec$trend
temp_deaseasonalized_series=temp-temp_seasonal_component
tem_detrended_series<-temp-temp_trend_component
sanayi <- read_excel("sanayi.xlsx", sheet = "Sayfa1",col_names = TRUE)
df4<-sanayi[,2]
ip<-ts(df4, frequency = 12, start = c(2009, 8))
plot(ip,ylab="Sanayi \retimi")
layout(1:3)
plot(aggregate(ip))
boxplot(ip ~ cycle(ip))
ip_dec<-decompose(ip, type ="mult")
plot(ip_dec)
ip_seasonal_component<-ip_dec$seasonal
ip_trend_component<-ip_dec$trend
ip_deaseasonalized_series=ip/ip_seasonal_component
ip_detrended_series<-ip/ip_trend_component
print(ip_detrended_series)
trend<-seq(1,length(New.series), by=1)
#construct 12 out of sample observation for the trend
trend_out<-seq(length(New.series)+1, length(New.series)+12, by=1)
class(trend_out)
trend_out<-as.data.frame(trend_out)
colnames(trend_out) <- "trend"
class(trend_out)
model2<-lm(New.series ~ trend)
summary(model2)
forecasted_temp2<-predict(model2, newdata = trend_out)
dummies=seasonaldummy(ip)
View(dummies)
View(dummies)
tarih<-sanayi$Tarih
dummy_df<-cbind(tarih,dummies)
View(dummy_df)
View(model.het)
dummy.ts<-ts(dummy_df, frequency = 12, start = c(2009, 8))
ln_ip<-log(ip)
plot(ln_ip, ylab="ln_ip")
plot(ip,ylab="ip")
plot(cbind(ip,ln_ip))
trend<-seq(1,length(ln_ip),by=1)
trend<-as.data.frame(trend)
dummy_df<-dummy_df[,2:12]
dummy_df<-as.data.frame(dummy_df)
ln_ip<-as.data.frame(ln_ip)
colnames(ln_ip) <- "ln_ip"
data_ip<-cbind(ln_ip,trend, dummy_df)
View(data_ip)
View(data_ip)
View(data_ip)
model3<-lm(ln_ip~trend+Jan+Feb+Mar+Apr+Jun+Jul+Aug+Sep+Oct+Nov, data=data_ip )
summary(model3)
dummies_o_s<-window(dummy.ts, start=c(2010, 3), end=c(2010, 12))
dummies_o_s<-dummies_o_s[,2:12]#removing the first column
dummies_o_s<-as.data.frame(dummies_o_s)
trend_out<-seq(length(ip)+1, length(ip)+10, by=1)
trend_out<-as.data.frame(trend_out)
colnames(trend_out) <- "trend"
dum_trend<-cbind(trend_out,dummies_o_s)
forecasted_ln_ip<-predict(model3, newdata = dum_trend)
sigma<-summary(model3)$sigma
log_correction_factor<-exp(0.5*sigma^2)
forecasted_ip<-exp(forecasted_ln_ip)*log_correction_factor
empirical_correction_factor<-mean(exp(resid(model3)))
forecasted_ip2<-exp(forecasted_ln_ip)*empirical_correction_factor
library(zoo)
library(readxl)
library(timeSeries)
library(tseries)
library(forecast)
data <- read_excel("dataset_TS.xlsx", sheet = "Konut",col_names = TRUE)
setwd("G:/Drive'ım/TimeSeriesandForecasting/DataandCodes")
library(zoo)
library(readxl)
library(timeSeries)
library(tseries)
library(forecast)
data <- read_excel("dataset_TS.xlsx", sheet = "Konut",col_names = TRUE)
setwd("G:/Drive'ım/TimeSeriesandForecasting/DataandCodes/Applications")
library(zoo)
library(readxl)
library(timeSeries)
library(tseries)
library(forecast)
data <- read_excel("dataset_TS.xlsx", sheet = "Konut",col_names = TRUE)
attach(data)
konut<-Konut
ts_konut<-ts(konut, frequency = 12, start = c(2013, 1))
ts_konut<-ts_konut/1000
plot(ts_konut, xlab= "Time (years)", ylab = "sales(1000) ")
#EXPONENTIAL SMOOTHING WITH ALPHA PRODUCED BY R
konut.hw1 <- HoltWinters(ts_konut, beta = FALSE, gamma = FALSE) ;konut.hw1 #(to print output)
plot(konut.hw1)
View(konut.hw1)
View(konut.hw1)
konut.hw1$SSE
konut.hw1$fitted
konut.hw2 <- HoltWinters(ts_konut, alpha=0.2,beta = FALSE, gamma = FALSE) ;konut.hw2 #(to print output)
plot(konut.hw2)
konut.hw2$SSE
konut.hw2 <- HoltWinters(ts_konut, alpha=0.2,beta = FALSE, gamma = FALSE) ;konut.hw2 #(to print output)
plot(konut.hw2)
konut.hw2$SSE
konut.hw2$fitted
konut.hw3 <- HoltWinters(ts_konut) ;konut.hw3 #(to print output)
plot(konut.hw3)
konut.hw3$SSE
konut.hw3$fitted
plot (konut.hw3$fitted)
konut.hw4 <- HoltWinters(ts_konut, seasonal="mult") ;konut.hw4 #(to print output)
plot(konut.hw4)
konut.hw4$SSE
konut.hw4$fitted
