
import yfinance as yf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.stats.diagnostic import acorr_ljungbox
from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense

# Download S&P 500 index data
sp500_data = yf.download("^GSPC", start="2010-01-01", progress=False)
sp500_close = sp500_data["Close"].dropna()

# First difference
sp500_diff = sp500_close.diff().dropna()

# Normalize
scaler = MinMaxScaler()
sp500_diff_scaled = scaler.fit_transform(sp500_diff.values.reshape(-1, 1)).flatten()

# Use last 21 as test
train_diff = sp500_diff_scaled[:-21]
test_diff = sp500_diff_scaled[-(21+20):]  # +20 to allow for possible lag

# Ljung-Box for lag selection
max_lag = 20
lb_pvalues = []
for lag in range(1, max_lag + 1):
    lb_test = acorr_ljungbox(train_diff, lags=[lag], return_df=True)
    lb_pvalues.append(lb_test['lb_pvalue'].iloc[0])
optimal_delay = next((i + 1 for i, p in enumerate(lb_pvalues) if p > 0.05), 1)
print(f"Optimal delay selected: {optimal_delay}")

# Lagged data
def create_lagged_data(series, lag):
    X, y = [], []
    for i in range(lag, len(series)):
        X.append(series[i - lag:i])
        y.append(series[i])
    return np.array(X), np.array(y)

X_train, y_train = create_lagged_data(train_diff, optimal_delay)
X_test, y_test = create_lagged_data(test_diff, optimal_delay)

# Reshape for LSTM [samples, time steps, features]
X_train = X_train.reshape((X_train.shape[0], X_train.shape[1], 1))
X_test = X_test.reshape((X_test.shape[0], X_test.shape[1], 1))

# Build LSTM model
model = Sequential()
model.add(LSTM(50, activation='relu', input_shape=(optimal_delay, 1)))
model.add(Dense(1))
model.compile(optimizer='adam', loss='mse')
model.fit(X_train, y_train, epochs=100, verbose=0)

# Predict and denormalize
y_pred_diff = model.predict(X_test).flatten()
y_pred_diff_rescaled = scaler.inverse_transform(y_pred_diff.reshape(-1, 1)).flatten()

# Reconstruct levels from last known index value
last_known_level = sp500_close.iloc[-22]
predicted_levels = np.cumsum(y_pred_diff_rescaled) + last_known_level

# Actual levels
y_test_rescaled = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
actual_levels = np.cumsum(y_test_rescaled) + last_known_level

# Plot
plt.figure(figsize=(10, 5))
plt.plot(actual_levels, label='Actual S&P 500 Level')
plt.plot(predicted_levels, label='Predicted S&P 500 Level')
plt.title('21-Day Forecast of S&P 500 Index (LSTM)')
plt.xlabel('Days Ahead')
plt.ylabel('Index Level')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()

# MAPE calculation
mape = np.mean(np.abs((actual_levels - predicted_levels) / actual_levels)) * 100
print(f"MAPE: {mape:.2f}%")
print(f"Final predicted index level at end of forecast: {predicted_levels[-1]:.2f}")
