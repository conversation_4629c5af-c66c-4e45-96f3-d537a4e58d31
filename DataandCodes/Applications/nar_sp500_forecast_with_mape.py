# -*- coding: utf-8 -*-
"""
Created on Tue Jun 17 22:34:02 2025

@author: ITU
"""

import yfinance as yf
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.stats.diagnostic import acorr_ljungbox
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score

# Step 1: Download S&P 500 data
sp500_data = yf.download("^GSPC", start="2010-01-01", progress=False)
sp500_close = sp500_data["Close"].dropna()

# Step 2: First difference of closing values
sp500_diff = sp500_close.diff().dropna()

# Step 3: Scale first differences
scaler = MinMaxScaler()
sp500_diff_scaled = scaler.fit_transform(sp500_diff.values.reshape(-1, 1)).flatten()

# Step 4: Split into train and test with buffer for lags
forecast_horizon = 21
max_lag = 10
train_diff = sp500_diff_scaled[:-forecast_horizon]
test_diff = sp500_diff_scaled[-(forecast_horizon ):]  # buffer to allow lagging

# Step 5: Ljung-Box test to determine optimal delay
lb_pvalues = []
for lag in range(1, max_lag + 1):
    lb_test = acorr_ljungbox(train_diff, lags=[lag], return_df=True)
    lb_pvalues.append(lb_test['lb_pvalue'].iloc[0])
optimal_delay = next((i + 1 for i, p in enumerate(lb_pvalues) if p > 0.05), 1)
print(f"Optimal delay selected: {optimal_delay}")

# Step 6: Function to create lagged data
def create_lagged_data(series, lag):
    X, y = [], []
    for i in range(lag, len(series)):
        X.append(series[i - lag:i])
        y.append(series[i])
    return np.array(X), np.array(y)

# Step 7: Prepare data for training and testing
X_train, y_train = create_lagged_data(train_diff, optimal_delay)
X_test, y_test = create_lagged_data(test_diff, optimal_delay)

# Step 8: Train MLP model
model = MLPRegressor(hidden_layer_sizes=(10,), activation='relu', solver='adam',
                     max_iter=500, random_state=42)
model.fit(X_train, y_train)

# Step 9: Predict and reverse scale manually
y_pred_scaled = model.predict(X_test)
min_val, max_val = scaler.data_min_[0], scaler.data_max_[0]
y_pred_diff = y_pred_scaled * (max_val - min_val) + min_val
y_test_diff = y_test * (max_val - min_val) + min_val

# Step 10: Reconstruct actual levels
last_known_index = len(sp500_diff_scaled) - len(test_diff)
last_known_level = sp500_close.iloc[last_known_index]
last_known_level_vector = np.repeat(last_known_level, len(y_test))
predicted_levels = np.cumsum(y_pred_diff) + last_known_level_vector
actual_levels = np.cumsum(y_test_diff) + last_known_level_vector
#calculate mape
mape = np.mean(np.abs((actual_levels - predicted_levels) / actual_levels)) * 100
print(f"MAPE: {mape:.2f}%")