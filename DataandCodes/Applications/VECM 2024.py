# -*- coding: utf-8 -*-
"""
Created on Thu Feb 28 19:13:22 2019

@author: pc
"""

"""
 VECTOR ERROR CORRECTION MODEL (VECM) and COINTEGRATION
 
    Time series models for VAR are usually based on applying VAR to stationary series with first differences 
    to original series and because of that, there is always a possibility of loss of information about 
    the relationship among integrated series.
    Therefore, differencing the series to make them stationary is one solution, 
    but at the cost of ignoring possibly important (“long run”) relationships between the levels. 
    A better solution is to test whether the levels regressions are trustworthy (“cointegration”).

    This cointegration concept origins in macroeconomics where series often seen as I(1) are
    regressed onto, like private consumption, C, and disposable income, Yd.
    Despite I(1), Yd and C cannot diverge too much in either direction.
    Because they share a common stochastic trend!

    Through VECM we can interpret long term and short term equations. 
    In order to fit a VECM model, we need to determine the number of co-integrating relationships 
    using a VEC rank test.
"""


#https://www.statsmodels.org/dev/generated/statsmodels.tsa.vector_ar.vecm.VECM.html
import warnings
import pandas as pd
import numpy as np
import statsmodels.api as sm
import statsmodels.tsa.stattools as ts 

from pathlib import Path
from arch.unitroot import ADF #augmented dickey fuller
from arch.unitroot import DFGLS
from arch.unitroot import PhillipsPerron
from arch.unitroot import KPSS
from statsmodels.tsa.api import VAR
from statsmodels.tsa.api import VECM
from statsmodels.tsa.stattools import coint
from statsmodels.tsa.vector_ar.vecm import CointRankResults
from statsmodels.tsa.vector_ar.vecm import coint_johansen
from pathlib import Path
import warnings
warnings.filterwarnings("ignore")

data_folder = Path("C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes/Applications")

data = pd.read_excel(data_folder /'datasetVAR.xlsx', sheet_name='Sayfa1')



# Data contains the gdp, investment, export and import values of Turkey in turkish liras.
idx = pd.date_range('1998-01-01', '2024-03-31', freq='Q')
data.Date=idx
df=data.set_index("Date")
df.columns


# Engle-granger cointegration test-single approach
x=df[['GDP',]]
y=df[['EXPORTS', 'IMPORTS']]
EG_coint=ts.coint(x, y)
print(EG_coint)


#unit root test: ADF
print(ADF(df.GDP, trend="ct"))
print(ADF(df.EXPORTS, trend="ct"))
print(ADF(df.INVESTMENT, trend="ct"))


#  creation of VECM dataset
vecmdata = df[['GDP','EXPORTS','IMPORTS']]
train_vecm=vecmdata.iloc[0:100]
test_vecm=vecmdata.iloc[-5:]
vecmvalues=train_vecm.values   # converting dataframe into array to proceed with Johansen test 



##### Johansen test for cointegration ######
##  coint_johansen(endog, det_order, k_ar_diff) ##
Test_Johansen=coint_johansen(vecmvalues,0,2)
#-1 - no deterministic terms; 0 - constant term; 1 - linear trend
#the later is the Number of lagged differences in the model.
trace_test=pd.DataFrame(Test_Johansen.lr1)
trace_test.columns=["trace test stat"]
cvt=pd.DataFrame(Test_Johansen.cvt)
cvt.columns=["0.1","0.05","0.01"]
Trace_test=pd.concat([trace_test,cvt],axis=1)

meigen_test=pd.DataFrame(Test_Johansen.lr2)
meigen_test.columns=["meigen test stat"]
cvm=pd.DataFrame(Test_Johansen.cvm)
cvm.columns=["0.1","0.05","0.01"]
Meigen_test=pd.concat([meigen_test,cvm],axis=1)

#signif.levels: {0.1, 0.05, 0.01}


### RULE: if both trace test statistics and max.eigen test statistics are greater than critical values, reject H0.
#   for any critical values that you choose.
#   r=1; there is one linear combination; there is one cointegration relationship!"
#   H0= no cointegration; HA: there is cointegration


 ########################################
####  Vector Error Correction Model  #####
 ########################################
model = VECM(train_vecm, k_ar_diff=2, coint_rank=1, deterministic='ci') 
vecm_res = model.fit()
vecm_res.summary()

# Loading coefficients (alpha) for equation Y: gives cointegration coefficient
# as we set cointegration rank to 1, cointegration matrix contains just 1 column:"1 linear combination"
# beta's are the coefficient of this linear combination equation

vecm_res.plot_data(with_presample=True)
vecmalphabeta=vecm_res.gamma.round(4) #coefficients

### checking residuals if they follow WN or not 
residuals=vecm_res.resid

from statsmodels.graphics.tsaplots import plot_acf
plot_acf(residuals[:,0]) #GDP
plot_acf(residuals[:,1]) #export
plot_acf(residuals[:,2]) #import

## Forecast
predicted_values=pd.DataFrame(vecm_res.predict(steps=5))
predicted_values.columns=['GDP','EXPORTS','IMPORTS']


forecast, lower, upper = vecm_res.predict(5, 0.05)
print("lower bounds of confidence intervals:")
print(lower.round(3))
print("\npoint forecasts:")
print(forecast.round(1))
print("\nupper bounds of confidence intervals:")
print(upper.round(3))

vecm_res.plot_forecast(steps=10) #out of sample forecast

#impulse-response functions

irf = vecm_res.irf(30)
irf.plot(orth=True)

irf.plot(impulse='GDP') #just for the GDP
irf.plot(impulse='EXPORTS') 
irf.plot(impulse='IMPORTS') 

irf = vecm_res.irf(20)
irf.plot(impulse='GDP') 


#### Model 2
model2 = VECM(train_vecm, k_ar_diff=1, coint_rank=1, deterministic='ci') 
vecm_res2 = model2.fit()
vecm_res2.summary()
predicted_values2=pd.DataFrame(vecm_res2.predict(steps=5))
predicted_values2.columns=['GDP','EXPORTS','IMPORTS']

### checking residuals if they follow WN or not 
residuals=vecm_res2.resid

from statsmodels.graphics.tsaplots import plot_acf
plot_acf(residuals[:,0]) #GDP
plot_acf(residuals[:,1]) #export
plot_acf(residuals[:,2]) #import


## Calculation of the metrics
def forecast_accuracy(forecast, actual):
    mape = np.mean(np.abs(forecast - actual)/np.abs(actual))  # MAPE
    mae = np.mean(np.abs(forecast - actual))    # MAE
    rmse = np.mean((forecast - actual)**2)**.5  # RMSE
    return({'mape':mape, 'mae': mae, 
             'rmse':rmse})

test_values=test_vecm.reset_index(drop=True)  ## in order to perform calculation, index of two DFs must match
# Metrics for model 1
metrics_for_GDP=forecast_accuracy(predicted_values.GDP, test_values.GDP)
metrics_for_Export=forecast_accuracy(predicted_values.EXPORTS, test_values.EXPORTS)
metrics_for_Import=forecast_accuracy(predicted_values.IMPORTS, test_values.IMPORTS)

# Metrics for model 2
metrics_for_GDP2=forecast_accuracy(predicted_values2.GDP, test_values.GDP)
metrics_for_Export2=forecast_accuracy(predicted_values2.EXPORTS, test_values.EXPORTS)
metrics_for_Import2=forecast_accuracy(predicted_values2.IMPORTS, test_values.IMPORTS)





    