{"cells": [{"cell_type": "code", "execution_count": 1, "id": "169803c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          Date  TURKEY CAPACITY UTILIZATION  TURKEY INDUSTRIAL PRODUCTION\n", "0   2010-05-15                         75.1                         51.21\n", "1   2010-06-15                         75.0                         53.52\n", "2   2010-07-15                         75.8                         53.91\n", "3   2010-08-15                         75.0                         51.94\n", "4   2010-09-15                         75.2                         50.21\n", "..         ...                          ...                           ...\n", "163 2023-12-15                         77.5                        121.51\n", "164 2024-01-15                         76.2                        100.97\n", "165 2024-02-15                         76.4                        103.36\n", "166 2024-03-15                         76.2                           NaN\n", "167 2024-04-15                         76.7                           NaN\n", "\n", "[168 rows x 3 columns]\n"]}], "source": ["import warnings\n", "import pandas as pd\n", "#from pandas import ExcelWriter\n", "#from pandas import ExcelFile\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import  matplotlib.pylab as plt\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "from statsmodels.graphics.tsaplots import plot_pacf\n", "plt.style.use('fivethirtyeight')\n", "from pathlib import Path\n", "data_folder = Path(\"C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes/Applications\")\n", "md = pd.read_excel(data_folder /'dataset_TS.xlsx', sheet_name='TUR Capacity util and IND Prod')\n", "# data_folder = Path(\"C:/data/\")\n", "# warnings.filterwarn \n", "#DATA=df['TURKEY CAPACITY UTILIZATION']\n", "print(md)"]}, {"cell_type": "code", "execution_count": 2, "id": "7cb23329", "metadata": {}, "outputs": [{"data": {"text/plain": ["DatetimeIndex(['2010-05-15', '2010-06-15', '2010-07-15', '2010-08-15',\n", "               '2010-09-15', '2010-10-15', '2010-11-15', '2010-12-15',\n", "               '2011-01-15', '2011-02-15',\n", "               ...\n", "               '2023-07-15', '2023-08-15', '2023-09-15', '2023-10-15',\n", "               '2023-11-15', '2023-12-15', '2024-01-15', '2024-02-15',\n", "               '2024-03-15', '2024-04-15'],\n", "              dtype='datetime64[ns]', name='Date', length=168, freq=None)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df= md.set_index('Date')\n", "df.index\n"]}, {"cell_type": "code", "execution_count": 10, "id": "c524f200", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                    SARIMAX Results                                    \n", "=======================================================================================\n", "Dep. Variable:     TURKEY CAPACITY UTILIZATION   No. Observations:                  132\n", "Model:                  SARIMAX(1, 0, [1, 12])   Log Likelihood                -252.487\n", "Date:                         Wed, 12 Jun 2024   AIC                            512.974\n", "Time:                                 20:59:38   BIC                            524.505\n", "Sample:                                      0   HQIC                           517.660\n", "                                         - 132                                         \n", "Covariance Type:                           opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "ar.L1          0.9997      0.003    329.613      0.000       0.994       1.006\n", "ma.L1          0.1199      0.062      1.947      0.052      -0.001       0.241\n", "ma.L12         0.0184      0.110      0.167      0.867      -0.197       0.234\n", "sigma2         2.5321      0.106     23.905      0.000       2.324       2.740\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.01   Jarque-<PERSON>ra (JB):              8764.31\n", "Prob(Q):                              0.94   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               5.39   Skew:                            -4.49\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        41.90\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "[[[5.82575237e-03 7.37422854e-01 3.31446902e+00 5.62213483e+00\n", "   6.12115047e+00 6.34377161e+00 6.35897440e+00 6.84222552e+00\n", "   6.85458134e+00 6.86008935e+00]\n", "  [9.39159251e-01 6.91624966e-01 3.45633898e-01 2.29200502e-01\n", "   2.94608347e-01 3.85802130e-01 4.98513657e-01 5.53747260e-01\n", "   6.52256514e-01 7.38584071e-01]]]\n", "1.5299656749024266\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cur=md['TURKEY CAPACITY UTILIZATION']\n", "data2=cur\n", "test_size=36\n", "training_size=len(data2)-test_size\n", "test_sample=data2[training_size:len(data2)]\n", "test_sample=test_sample.reset_index()\n", "del test_sample['index']\n", "training_sample=data2[0:training_size]\n", "\n", "#additive seasonality\n", "\n", "\n", "ar = 1          # this is the maximum degree specification\n", "ma = (1,0,0,0,0,0,0,0,0,0,0,1)  # this is the lag polynomial specification\n", "\n", "mod=sm.tsa.statespace.SARIMAX(training_sample,trend='n',order=(ar,0,ma),enforce_stationarity=True, \n", "                                         enforce_invertibility=True)\n", "results=mod.fit(disp=False)\n", "print(results.summary())\n", "\n", "print(results.test_serial_correlation(method='ljungbox', lags=None))\n", "#results.plot_diagnostics()\n", "residuals=results.resid\n", "residuals=residuals.iloc[1:-1]\n", "plot_acf(residuals,lags=10)\n", "\n", "\n", "\n", "#in sample prediction\n", "pred=results.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "pred_pseudo1=pred.predicted_mean\n", "pred_pseudo1=pred_pseudo1.reset_index()\n", "del pred_pseudo1['index']\n", "pred_pseudo1.columns = ['predicted']\n", "ypredict1=pred_pseudo1.values\n", "yactual=test_sample.values\n", "mae1=abs(yactual-ypredict1).mean()\n", "mape1=100*(abs(yactual-ypredict1)/yactual).mean()\n", "print(mape1)"]}, {"cell_type": "code", "execution_count": 11, "id": "cdd331a6", "metadata": {}, "outputs": [], "source": ["#out of sample prediction\n", "# Getting 12 months for forecasts\n", "SARIMAX_forecast = round(results.forecast(steps =12 ), 2)\n", "# Creating an index from 2024.5 to 2025.5 and a SARIMAX_forecast dataframe:\n", "idx = pd.date_range('2024.5', '2025.5', freq='    M')"]}, {"cell_type": "code", "execution_count": 40, "id": "bf4322c6", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DatetimeIndex(['2024-05-31', '2024-06-30', '2024-07-31', '2024-08-31',\n", "               '2024-09-30', '2024-10-31', '2024-11-30', '2024-12-31',\n", "               '2025-01-31', '2025-02-28', '2025-03-31', '2025-04-30'],\n", "              dtype='datetime64[ns]', freq='M')\n"]}], "source": ["print(idx)"]}, {"cell_type": "code", "execution_count": 12, "id": "f25f5435", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["            ForecastValue\n", "Date                     \n", "2024-05-31          76.10\n", "2024-06-30          76.14\n", "2024-07-31          76.20\n", "2024-08-31          76.22\n", "2024-09-30          76.21\n", "2024-10-31          76.20\n", "2024-11-30          76.19\n", "2024-12-31          76.16\n", "2025-01-31          76.14\n", "2025-02-28          76.11\n", "2025-03-31          76.08\n", "2025-04-30          76.09\n"]}], "source": ["SARIMAX_forecast = pd.DataFrame(list(zip(list(idx),list(SARIMAX_forecast))), \n", "                                columns=['Date','ForecastValue']).set_index('Date')\n", "print(SARIMAX_forecast)"]}, {"cell_type": "code", "execution_count": 6, "id": "748eabcf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                     SARIMAX Results                                      \n", "==========================================================================================\n", "Dep. Variable:        TURKEY CAPACITY UTILIZATION   No. Observations:                  156\n", "Model:             SARIMAX(1, 0, 0)x(1, 0, 0, 12)   Log Likelihood                -392.361\n", "Date:                            Wed, 12 Jun 2024   AIC                            790.722\n", "Time:                                    20:57:56   BIC                            799.872\n", "Sample:                                         0   HQIC                           794.438\n", "                                            - 156                                         \n", "Covariance Type:                              opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "ar.L1          0.9998      0.000   5324.803      0.000       0.999       1.000\n", "ar.S.L12       0.9999      0.000   7899.364      0.000       1.000       1.000\n", "sigma2         4.1891      0.069     60.423      0.000       4.053       4.325\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.10   Jarque-Bera (JB):              6450.28\n", "Prob(Q):                              0.76   Prob(JB):                         0.00\n", "Heteroskedasticity (H):              10.77   Skew:                             0.32\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        34.50\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "[[[9.59247996e-02 9.28706159e-01 3.16880920e+00 5.03939543e+00\n", "   5.94501124e+00 6.54583000e+00 6.65472247e+00 7.57659714e+00\n", "   7.88262097e+00 8.62406738e+00 8.88715750e+00 5.26509451e+01\n", "   5.27765160e+01 5.28775604e+01 5.33584538e+01 5.34195394e+01\n", "   5.47605871e+01 5.59706542e+01 5.61167348e+01 5.66495696e+01\n", "   5.70679872e+01 5.73954250e+01 5.77647345e+01 5.94142887e+01]\n", "  [7.56775953e-01 6.28541590e-01 3.66323245e-01 2.83279112e-01\n", "   3.11606397e-01 3.64894915e-01 4.65692718e-01 4.75881757e-01\n", "   5.46017216e-01 5.68113173e-01 6.32307482e-01 4.75207290e-07\n", "   9.88282865e-07 1.99590501e-06 3.36109989e-06 6.44839853e-06\n", "   7.48594499e-06 9.00799005e-06 1.56539559e-05 2.31946645e-05\n", "   3.51098474e-05 5.38045879e-05 7.98633894e-05 7.72372947e-05]]]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#mutliplicative seasonality\n", "# arma(1,0) with seasonal AR\n", "\n", "mod2=sm.tsa.statespace.SARIMAX(training_sample,trend='n',order=(1,0,0),seasonal_order=(1,0,0,12),enforce_stationarity=True, \n", "                                         enforce_invertibility=True)\n", "\n", "\n", "results2=mod2.fit(disp=False)\n", "print(results2.summary())\n", "\n", "print(results2.test_serial_correlation(method='ljungbox', lags=None))\n", "#results.plot_diagnostics()\n", "residuals2=results2.resid\n", "residuals2=residuals2.iloc[1:-1]\n", "plot_acf(residuals2,lags=10)\n", "\n", "\n", "\n", "#in sample prediction\n", "pred2=results2.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "pred_pseudo2=pred2.predicted_mean\n", "pred_pseudo2=pred_pseudo2.reset_index()\n", "del pred_pseudo2['index']\n", "pred_pseudo2.columns = ['predicted']\n", "ypredict2=pred_pseudo2.values\n", "yactual=test_sample.values\n", "mae2=abs(yactual-ypredict2).mean()\n", "mape2=100*(abs(yactual-ypredict2)/yactual).mean()"]}, {"cell_type": "code", "execution_count": 7, "id": "61cac7ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["156    75.60\n", "157    75.20\n", "158    75.80\n", "159    74.30\n", "160    75.00\n", "161    74.50\n", "162    73.50\n", "163    74.10\n", "164    72.90\n", "165    72.80\n", "166    71.11\n", "167    73.01\n", "Name: predicted_mean, dtype: float64\n"]}], "source": ["#out of sample prediction\n", "# Getting 12 months for forecasts\n", "SARIMAX_forecast2 = round(results2.forecast(steps =12 ), 2)\n", "print(SARIMAX_forecast2)"]}, {"cell_type": "code", "execution_count": 8, "id": "ffc6b402", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["            ForecastValue\n", "Date                     \n", "2024-05-31          75.60\n", "2024-06-30          75.20\n", "2024-07-31          75.80\n", "2024-08-31          74.30\n", "2024-09-30          75.00\n", "2024-10-31          74.50\n", "2024-11-30          73.50\n", "2024-12-31          74.10\n", "2025-01-31          72.90\n", "2025-02-28          72.80\n", "2025-03-31          71.11\n", "2025-04-30          73.01\n"]}], "source": ["SARIMAX_forecast2 = pd.DataFrame(list(zip(list(idx),list(SARIMAX_forecast2))), \n", "                                columns=['Date','ForecastValue']).set_index('Date')\n", "print(SARIMAX_forecast2)"]}, {"cell_type": "code", "execution_count": 9, "id": "45346059", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.9967638306087881 3.671266381511159\n"]}], "source": ["print(mape1,mape2)"]}, {"cell_type": "code", "execution_count": null, "id": "7eac14ac", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f65f83ee", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}