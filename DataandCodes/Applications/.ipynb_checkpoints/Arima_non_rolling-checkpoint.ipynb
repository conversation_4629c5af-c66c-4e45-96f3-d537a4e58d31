{"cells": [{"cell_type": "code", "execution_count": 10, "id": "1e438544", "metadata": {}, "outputs": [], "source": ["import warnings\n", "import pandas as pd\n", "#from pandas import ExcelWriter\n", "#from pandas import ExcelFile\n", "import statsmodels.api as sm\n", "import  matplotlib.pylab as plt\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "from statsmodels.graphics.tsaplots import plot_pacf\n", "plt.style.use('fivethirtyeight')\n", "from pathlib import Path\n", "\n", "#pip install arch\n", "#ya da\n", "#\"Open Anaconda Promt and write:conda install -c bashtage arch \"\n", "import arch\n", "from arch.unitroot import ADF\n", "from arch.unitroot import DFGLS\n", "from arch.unitroot import PhillipsPerron\n", "from arch.unitroot import KPSS\n", "import statsmodels\n", "from statsmodels.tsa.stattools import adfuller\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 8, "id": "3d94e6f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>; I(0)\n"]}, {"data": {"image/png": "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***************************************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\n", "text/plain": ["<Figure size 1500x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data_folder = Path(\"C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes/Applications\")\n", "\n", "df = pd.read_excel(data_folder /'dataset_TS.xlsx', sheet_name='TUR Capacity util and IND Prod')\n", "DATA=df['TURKEY CAPACITY UTILIZATION']\n", "DATA.dropna()\n", " \n", "DATA.plot(figsize=(15,4))\n", "data2=DATA\n", "#<PERSON>inin durağanlığın kontrolü\n", "\n", "\n", "def test_stationarity(data_n):    # adf_test = ADF(data2, trend='ct', max_lags=10, method='AIC') \n", "    adfTest = adfuller(data_n, autolag='AIC')\n", "    p_value=adfTest[1]\n", "    if p_value< 0.01:\n", "            print('se<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>; I(0)') \n", "            d=0\n", "    else:\n", "          dif_ADF=data_n.diff()\n", "          dif_ADF=dif_ADF.dropna()\n", "          dif_ADF = adfuller(dif_ADF, autolag='AIC')  \n", "          p_value2=dif_ADF[1]\n", "          if p_value2< 0.01:\n", "                print('seri far<PERSON><PERSON>; I(I)') \n", "                d=1\n", "    return d\n", "\n", "\n", "d=test_stationarity(data2) "]}, {"cell_type": "code", "execution_count": 11, "id": "951c1847", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                    SARIMAX Results                                    \n", "=======================================================================================\n", "Dep. Variable:     TURKEY CAPACITY UTILIZATION   No. Observations:                  132\n", "Model:                        SARIMAX(1, 0, 0)   Log Likelihood                -242.564\n", "Date:                         Wed, 12 Jun 2024   AIC                            491.129\n", "Time:                                 20:16:19   BIC                            499.777\n", "Sample:                                      0   HQIC                           494.643\n", "                                         - 132                                         \n", "Covariance Type:                           opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     15.3590      4.100      3.746      0.000       7.324      23.394\n", "ar.L1          0.7983      0.055     14.639      0.000       0.691       0.905\n", "sigma2         2.2925      0.089     25.863      0.000       2.119       2.466\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   4.44   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JB):             14993.78\n", "Prob(Q):                              0.04   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               5.73   Skew:                            -5.74\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        53.93\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["test_size=36\n", "frsct_hrz=3\n", "training_size=len(data2)-test_size\n", "test_sample=data2[training_size:len(data2)]\n", "test_sample=test_sample.reset_index()\n", "del test_sample['index']\n", "training_sample=data2[0:training_size]\n", "mod1=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(1,d,0))\n", "\n", "results1=mod1.fit(disp=False)\n", "print(results1.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals1=results1.resid\n", "residuals1=residuals1.iloc[1:-1]\n", "plot_acf(residuals1,lags=10)"]}, {"cell_type": "code", "execution_count": 12, "id": "d3eb4b77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[75.3]\n", " [76.6]\n", " [76.7]\n", " [77.1]\n", " [78.1]\n", " [78. ]\n", " [78.1]\n", " [78.7]\n", " [77.6]\n", " [76.6]\n", " [77.3]\n", " [77.8]\n", " [78. ]\n", " [77.6]\n", " [78.2]\n", " [76.7]\n", " [77.4]\n", " [76.9]\n", " [75.9]\n", " [76.5]\n", " [75.3]\n", " [75.2]\n", " [73.5]\n", " [75.4]\n", " [76. ]\n", " [76.8]\n", " [77.1]\n", " [76.1]\n", " [77.3]\n", " [77.4]\n", " [78. ]\n", " [77.5]\n", " [76.2]\n", " [76.4]\n", " [76.2]\n", " [76.7]] [[75.94907383]\n", " [75.98824893]\n", " [76.01952197]\n", " [76.04448689]\n", " [76.0644161 ]\n", " [76.08032537]\n", " [76.09302556]\n", " [76.10316399]\n", " [76.11125738]\n", " [76.11771824]\n", " [76.12287587]\n", " [76.12699315]\n", " [76.13027993]\n", " [76.13290373]\n", " [76.13499828]\n", " [76.13667034]\n", " [76.13800512]\n", " [76.13907067]\n", " [76.13992128]\n", " [76.14060031]\n", " [76.14114238]\n", " [76.1415751 ]\n", " [76.14192054]\n", " [76.1421963 ]\n", " [76.14241643]\n", " [76.14259217]\n", " [76.14273245]\n", " [76.14284444]\n", " [76.14293384]\n", " [76.1430052 ]\n", " [76.14306218]\n", " [76.14310765]\n", " [76.14314396]\n", " [76.14317294]\n", " [76.14319608]\n", " [76.14321455]]\n"]}], "source": ["#pseudo forecast\n", "pred1=results1.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo1=pred1.predicted_mean\n", "pred_pseudo1=pred_pseudo1.reset_index()\n", "del pred_pseudo1['index']\n", "pred_pseudo1.columns = ['predicted']\n", "ypredict1=pred_pseudo1.values\n", "yactual=test_sample.values\n", "print(yactual,ypredict1)\n", "mae1=abs(yactual-ypredict1).mean()\n", "mape1=100*(abs(yactual-ypredict1)/yactual).mean()"]}, {"cell_type": "code", "execution_count": 13, "id": "533ce709", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                    SARIMAX Results                                    \n", "=======================================================================================\n", "Dep. Variable:     TURKEY CAPACITY UTILIZATION   No. Observations:                  132\n", "Model:                        SARIMAX(1, 0, 1)   Log Likelihood                -239.593\n", "Date:                         Wed, 12 Jun 2024   AIC                            487.187\n", "Time:                                 20:16:32   BIC                            498.718\n", "Sample:                                      0   HQIC                           491.872\n", "                                         - 132                                         \n", "Covariance Type:                           opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     21.7812      9.177      2.373      0.018       3.794      39.769\n", "ar.L1          0.7140      0.121      5.924      0.000       0.478       0.950\n", "ma.L1          0.2398      0.239      1.005      0.315      -0.228       0.708\n", "sigma2         2.1904      0.111     19.734      0.000       1.973       2.408\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.06   Jarque<PERSON><PERSON>ra (JB):             16614.11\n", "Prob(Q):                              0.81   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               5.65   Skew:                            -5.97\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        56.65\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "                                    SARIMAX Results                                    \n", "=======================================================================================\n", "Dep. Variable:     TURKEY CAPACITY UTILIZATION   No. Observations:                  132\n", "Model:                        SARIMAX(2, 0, 1)   Log Likelihood                -239.048\n", "Date:                         Wed, 12 Jun 2024   AIC                            488.097\n", "Time:                                 20:16:32   BIC                            502.511\n", "Sample:                                      0   HQIC                           493.954\n", "                                         - 132                                         \n", "Covariance Type:                           opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     17.5253     19.742      0.888      0.375     -21.168      56.218\n", "ar.L1          1.0843      1.123      0.966      0.334      -1.116       3.284\n", "ar.L2         -0.3145      0.880     -0.357      0.721      -2.039       1.410\n", "ma.L1         -0.1160      1.155     -0.100      0.920      -2.380       2.148\n", "sigma2         2.1727      0.104     20.860      0.000       1.969       2.377\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   Jarque-<PERSON>ra (JB):             17630.01\n", "Prob(Q):                              0.96   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               5.75   Skew:                            -6.10\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        58.29\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\base\\model.py:604: ConvergenceWarning: Maximum Likelihood optimization failed to converge. Check mle_retvals\n", "  warnings.warn(\"Maximum Likelihood optimization failed to \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                                    SARIMAX Results                                    \n", "=======================================================================================\n", "Dep. Variable:     TURKEY CAPACITY UTILIZATION   No. Observations:                  132\n", "Model:                        SARIMAX(3, 0, 1)   Log Likelihood                -239.031\n", "Date:                         Wed, 12 Jun 2024   AIC                            490.061\n", "Time:                                 20:16:32   BIC                            507.358\n", "Sample:                                      0   HQIC                           497.090\n", "                                         - 132                                         \n", "Covariance Type:                           opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     19.5954     99.088      0.198      0.843    -174.613     213.804\n", "ar.L1          0.9714      5.555      0.175      0.861      -9.916      11.858\n", "ar.L2         -0.1943      5.421     -0.036      0.971     -10.819      10.431\n", "ar.L3         -0.0344      1.185     -0.029      0.977      -2.356       2.287\n", "ma.L1         -0.0050      5.590     -0.001      0.999     -10.961      10.951\n", "sigma2         2.1718      0.115     18.882      0.000       1.946       2.397\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.01   Jarque-Bera (JB):             17750.66\n", "Prob(Q):                              0.94   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               5.78   Skew:                            -6.11\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        58.48\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "                                    SARIMAX Results                                    \n", "=======================================================================================\n", "Dep. Variable:     TURKEY CAPACITY UTILIZATION   No. Observations:                  132\n", "Model:                        SARIMAX(4, 0, 1)   Log Likelihood                -239.017\n", "Date:                         Wed, 12 Jun 2024   AIC                            492.034\n", "Time:                                 20:16:32   BIC                            512.214\n", "Sample:                                      0   HQIC                           500.234\n", "                                         - 132                                         \n", "Covariance Type:                           opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     19.4156    405.189      0.048      0.962    -774.741     813.572\n", "ar.L1          0.9734     20.504      0.047      0.962     -39.213      41.160\n", "ar.L2         -0.1939     19.718     -0.010      0.992     -38.841      38.453\n", "ar.L3         -0.0480      3.777     -0.013      0.990      -7.450       7.354\n", "ar.L4          0.0135      0.788      0.017      0.986      -1.532       1.559\n", "ma.L1         -0.0074     20.543     -0.000      1.000     -40.271      40.257\n", "sigma2         2.1708      0.122     17.738      0.000       1.931       2.411\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.01   Jarque-Bera (JB):             17980.31\n", "Prob(Q):                              0.94   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               5.80   Skew:                            -6.15\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        58.84\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#model 2\n", "mod2=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(1,d,1))\n", "\n", "results2=mod2.fit(disp=False)\n", "print(results2.summary())\n", "#results.plot_diagnostics()\n", "residuals2=results2.resid\n", "residuals2=residuals2.iloc[1:-1]\n", "plot_acf(residuals2,lags=10)\n", "\n", "#pseudo forecast\n", "pred2=results2.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo2=pred2.predicted_mean\n", "pred_pseudo2=pred_pseudo2.reset_index()\n", "del pred_pseudo2['index']\n", "pred_pseudo2.columns = ['predicted']\n", "ypredict2=pred_pseudo2.values\n", "mae2=abs(yactual-ypredict2).mean()\n", "mape2=100*(abs(yactual-ypredict2)/yactual).mean()\n", "#model 3\n", "\n", "\n", "mod3=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(2,d,1)) \n", "                                  \n", "results3=mod3.fit(disp=False)\n", "print(results3.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals3=results3.resid\n", "residuals3=residuals3.iloc[1:-1]\n", "plot_acf(residuals3,lags=10)                                         \n", "\n", "\n", "pred3=results3.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo3=pred3.predicted_mean\n", "pred_pseudo3=pred_pseudo3.reset_index()\n", "del pred_pseudo3['index']\n", "pred_pseudo3.columns = ['predicted']\n", "ypredict3=pred_pseudo3.values\n", "mae3=abs(yactual-ypredict3).mean()\n", "mape3=100*(abs(yactual-ypredict3)/yactual).mean()\n", "#model 4\n", "\n", "mod4=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(3,d,1)) \n", "                                  \n", "results4=mod4.fit(disp=False)\n", "print(results4.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals4=results4.resid\n", "residuals4=residuals4.iloc[1:-1]\n", "plot_acf(residuals4,lags=10)                                         \n", "\n", "\n", "pred4=results4.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo4=pred4.predicted_mean\n", "pred_pseudo4=pred_pseudo4.reset_index()\n", "del pred_pseudo4['index']\n", "pred_pseudo4.columns = ['predicted']\n", "ypredict4=pred_pseudo4.values\n", "mae4=abs(yactual-ypredict4).mean()\n", "mape4=100*(abs(yactual-ypredict4)/yactual).mean()\n", "#model 5\n", "\n", "\n", "                                       \n", "\n", "mod5=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(4,d,1)) \n", "                                  \n", "results5=mod5.fit(disp=False)\n", "print(results5.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals5=results5.resid\n", "residuals5=residuals5.iloc[1:-1]\n", "plot_acf(residuals5,lags=10)                                         \n", "\n", "\n", "pred5=results5.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo5=pred5.predicted_mean\n", "pred_pseudo5=pred_pseudo5.reset_index()\n", "del pred_pseudo5['index']\n", "pred_pseudo5.columns = ['predicted']\n", "ypredict5=pred_pseudo5.values\n", "mae5=abs(yactual-ypredict5).mean()\n", "mape5=100*(abs(yactual-ypredict5)/yactual).mean()"]}, {"cell_type": "code", "execution_count": 14, "id": "975e4e3d", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.386016245081754 1.3505536036564547 1.351700856833121 1.324411096986792 1.3273775996454609\n"]}], "source": ["print(mape1, mape2, mape3,mape4,mape5)"]}, {"cell_type": "code", "execution_count": 18, "id": "db3c6540", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\tsa\\statespace\\kalman_filter.py:2290: ValueWarning: Dynamic prediction specified to begin during out-of-sample forecasting period, and so has no effect.\n", "  warn('Dynamic prediction specified to begin during'\n"]}], "source": ["#real out of sample predictions \n", "tpred_real=results1.get_prediction(start=len(data2),end=len(data2)+frsct_hrz,dynamic=True)\n", "pred_ci=tpred_real.conf_int()\n", "pred_real=tpred_real.predicted_mean\n", "pred_real=pred_real.reset_index()\n", "del pred_real['index']\n", "pred_real.columns = ['real predicted']"]}, {"cell_type": "code", "execution_count": 19, "id": "f15a2bf8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   real predicted\n", "0       76.143229\n", "1       76.143241\n", "2       76.143250\n", "3       76.143258\n"]}], "source": ["print(pred_real)"]}, {"cell_type": "code", "execution_count": null, "id": "22bd9d63", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}