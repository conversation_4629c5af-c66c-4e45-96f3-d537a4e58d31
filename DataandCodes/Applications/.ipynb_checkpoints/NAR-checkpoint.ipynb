{"cells": [{"cell_type": "code", "execution_count": 2, "id": "36f67dea-a800-4e16-9163-c1b8d8716e2a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_10877/2273133171.py:11: FutureWarning: YF.download() has changed argument auto_adjust default to True\n", "  sp500_data = yf.download(\"^GSPC\", start=\"2010-01-01\", progress=False)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Optimal delay selected: 1\n", "MAPE: 1.22%\n"]}], "source": ["import yfinance as yf\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from statsmodels.stats.diagnostic import acorr_ljungbox\n", "from sklearn.neural_network import MLPRegressor\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "# Step 1: Download S&P 500 data\n", "sp500_data = yf.download(\"^GSPC\", start=\"2010-01-01\", progress=False)\n", "sp500_close = sp500_data[\"Close\"].dropna()\n", "\n", "# Step 2: First difference of closing values\n", "sp500_diff = sp500_close.diff().dropna()\n", "\n", "# Step 3: Scale first differences\n", "scaler = MinMaxScaler()\n", "sp500_diff_scaled = scaler.fit_transform(sp500_diff.values.reshape(-1, 1)).flatten()\n", "\n", "# Step 4: Split into train and test with buffer for lags\n", "forecast_horizon = 21\n", "max_lag = 10\n", "train_diff = sp500_diff_scaled[:-forecast_horizon]\n", "test_diff = sp500_diff_scaled[-(forecast_horizon ):]  # buffer to allow lagging\n", "\n", "# Step 5: Ljung-Box test to determine optimal delay\n", "lb_pvalues = []\n", "for lag in range(1, max_lag + 1):\n", "    lb_test = acorr_ljungbox(train_diff, lags=[lag], return_df=True)\n", "    lb_pvalues.append(lb_test['lb_pvalue'].iloc[0])\n", "optimal_delay = next((i + 1 for i, p in enumerate(lb_pvalues) if p > 0.05), 1)\n", "print(f\"Optimal delay selected: {optimal_delay}\")\n", "\n", "# Step 6: Function to create lagged data\n", "def create_lagged_data(series, lag):\n", "    X, y = [], []\n", "    for i in range(lag, len(series)):\n", "        X.append(series[i - lag:i])\n", "        y.append(series[i])\n", "    return np.array(X), np.array(y)\n", "\n", "# Step 7: Prepare data for training and testing\n", "X_train, y_train = create_lagged_data(train_diff, optimal_delay)\n", "X_test, y_test = create_lagged_data(test_diff, optimal_delay)\n", "\n", "# Step 8: Train MLP model\n", "model = MLPRegressor(hidden_layer_sizes=(10,), activation='relu', solver='adam',\n", "                     max_iter=500, random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "# Step 9: Predict and reverse scale manually\n", "y_pred_scaled = model.predict(X_test)\n", "min_val, max_val = scaler.data_min_[0], scaler.data_max_[0]\n", "y_pred_diff = y_pred_scaled * (max_val - min_val) + min_val\n", "y_test_diff = y_test * (max_val - min_val) + min_val\n", "\n", "# Step 10: Reconstruct actual levels\n", "last_known_index = len(sp500_diff_scaled) - len(test_diff)\n", "last_known_level = sp500_close.iloc[last_known_index]\n", "last_known_level_vector = np.repeat(last_known_level, len(y_test))\n", "predicted_levels = np.cumsum(y_pred_diff) + last_known_level_vector\n", "actual_levels = np.cumsum(y_test_diff) + last_known_level_vector\n", "#calculate mape\n", "mape = np.mean(np.abs((actual_levels - predicted_levels) / actual_levels)) * 100\n", "print(f\"MAPE: {mape:.2f}%\")"]}, {"cell_type": "code", "execution_count": null, "id": "9f9d69e4-6b66-4880-8822-6cd6f2705f23", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}