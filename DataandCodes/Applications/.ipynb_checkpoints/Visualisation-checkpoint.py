# -*- coding: utf-8 -*-

# Data Visualization #

# create a line plot
import pandas as pd
import matplotlib.pyplot as plt
from pandas.plotting import lag_plot
from pandas.plotting import autocorrelation_plot
from statsmodels.tsa.seasonal import seasonal_decompose
plt.style.use('seaborn-whitegrid')

data = pd.read_excel('dataset_TS.xlsx', sheet_name="Trade Data").set_index("Date")
data.columns
TR_export_GR=data['TURKEY - EXPORTS TO GERMANY (FOB) ML USD']
GR_export_TR=data['GERMANY - EXPORTS TO TURKEY (FOB) ML USD']


# kodların tümü seçilerek birlikte çalıştırılır
plt.figure(figsize=(10, 8)) #figürun boyutları ayarlanır
TR_export_GR.plot() 
#plt.plot(TR_export_GR) #veya bu kod ile de line plot çizdirilebilir
plt.title('TURKEY - EXPORTS TO GERMANY',size=15)
plt.xlabel("Time", size=13)
plt.ylabel("ML USD", size=13)
plt.show()


# Dot plot, renk:kırmızı
plt.figure(figsize=(10, 8))
TR_export_GR.plot(style="r.")  #style argümanı ile renk (red:r) ve çizgi stili (dot: .) belirlenir
#plt.plot(TR_export_GR, "r.")
plt.title('TURKEY - EXPORTS TO GERMANY',size=15)
plt.xlabel("Time", size=13)
plt.ylabel("ML USD", size=13)
plt.show()

# verileri yıllık olarak gruplandırarak son 4 yılın yıllık versini subplot olarak çizdirme 
groups = TR_export_GR.groupby(pd.Grouper(freq='A'))
years = pd.DataFrame()
for name, group in groups:
    years[name.year] = group.values
 
years.iloc[:,30:].plot(subplots=True, legend=False)
plt.show()

# create a histogram plot
plt.figure(figsize=(10, 8))
TR_export_GR.hist(color="g")
plt.show()

# create a density plot
TR_export_GR.plot(kind='kde',style="b--")  #style argümanı ile renk (blue:b) ve çizgi stili (dashed:--) belirlenir
plt.show()

# create a boxplot
pd.DataFrame(TR_export_GR).boxplot()
plt.show()
#yıllıklandırılmış veriler kullanılarak son 5 yıla dair boxplot çizimi
years.iloc[:,29:].boxplot()
plt.show()


# create a heat map of yearly data
plt.matshow(years, interpolation=None, aspect='auto', cmap="viridis")
plt.show()

# create a scatter plot for between current and lagged values
lag_plot(TR_export_GR)
plt.show()

# create an autocorrelation plot
autocorrelation_plot(TR_export_GR)
plt.show()

# seasonal decompose plots
date_range=pd.date_range('1990.1', '2024.1', freq='M') #seasonal_decompose fonksiyonun çalışması için tarihi istediği formatta oluştururuz
TR_export_GR.index=date_range #index olarak bu tarihi ayarlarız
seasonal_decompose(TR_export_GR, model='additive').plot()
seasonal_decompose(TR_export_GR, model='multiplicative').plot()


#subplots
plt.figure(figsize=(12, 8)) 
plt.subplot(1, 2, 1) #(row number, column number, location for the graph)
plt.plot(TR_export_GR, label="Export from TR to GER", color="m")
plt.title('TURKEY - EXPORTS TO GERMANY',size=15)
plt.xlabel("Time", size=13)
plt.ylabel("ML USD", size=13)
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(GR_export_TR, label="Export from GER to TR", color="r")
plt.title('GERMANY - EXPORTS TO TURKEY',size=15)
plt.xlabel("Time", size=13)
plt.ylabel("ML USD", size=13)
plt.legend()


#scatter plot for two time series (reveals the relation between two time series)
plt.figure(figsize=(10, 8)) 
plt.scatter(TR_export_GR,GR_export_TR)
plt.xlabel("Export from TR to GER", size=13)
plt.ylabel("Export from GER to TR", size=13)
plt.show()


