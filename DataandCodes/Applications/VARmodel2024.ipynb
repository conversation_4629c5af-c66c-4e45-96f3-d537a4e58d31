{"cells": [{"cell_type": "code", "execution_count": null, "id": "14962e22", "metadata": {}, "outputs": [], "source": ["import warnings\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "\n", "from statsmodels.tsa.api import VAR\n", "from statsmodels.tsa.api import VECM\n", "from statsmodels.tools.eval_measures import rmse, aic\n", "from statsmodels.tsa.stattools import acf\n", "from pathlib import Path\n", "\n", "data_folder = Path(\"C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes/Applications\")\n", "\n", "data = pd.read_excel(data_folder /'datasetVAR.xlsx', sheet_name='Sayfa1')\n", "\n", "# Data contains the gdp, investment, export and import values of Turkey in turkish liras.\n", "idx = pd.date_range('1998-01-01', '2024-03-31', freq='Q')\n", "data.Date=idx\n", "df=data.set_index(\"Date\")\n", "df.columns\n", "\n", "df[\"GDP\"].plot()\n", "df[\"EXPORTS\"].plot()\n", "df[\"IMPORTS\"].plot()\n", "df[\"INVESTMENT\"].plot()\n", "\n", "\n", "## level data split## -- for later use\n", "df2=df[['GDP','EXPORTS','INVESTMENT']]\n", "train=df2.iloc[0:100] #1998.Q1-2022.Q4\n", "test_sample=df2.iloc[-5:] #2022.Q4-2024.Q1\n", "\n", "\n", "#creation of VAR dataset\n", "vardata = df[['GDP','EXPORTS','INVESTMENT']]\n", "vardata=vardata.diff().dropna() ### taking difference to make variables stationary\n", "train_var=vardata.iloc[0:99] # 1998.Q2-2022.Q4\n", "test_var=vardata.iloc[-5:]\n", "\n", "\n", "model_var = VAR(train_var)\n", "\n", "result_var_bic=model_var.fit( maxlags=15, ic='bic', trend='ct') #we leave the lag decision to information criteria\n", "result_var_aic=model_var.fit( maxlags=15, ic='aic', trend='ct')\n", "# or\n", "result_var=model_var.fit(5, trend='ct') #we directly write the lag\n", "stable1=result_var.is_stable(verbose=False) # when verbbose is true it gives \"eigenvalue\"s\n", "print(stable1) \n", "\"the result is true; then it is stable\"\n", "\"if it says False, then we have to cahnge the lags and compare the results\"\n", "\"lag number shouldnt be large generally, because more lags more roots; more likely to fail\"\n", "\n", "result_var.summary()\n", "result_var.plot()\n", "lag_order = result_var.k_ar # we define this for later use (in forecasting)\n", "\n", "result_var.plot_acorr()\n", "#we check whether the residuals are white noise or not; 3 dependent, 3 independent variable so 9 graphics\n", "\n", "### in-sample forecast ###\n", "pseudo_for=result_var.forecast(train_var.values[-lag_order:],steps=5) \n", "#In order to forecast, the VAR model expects up to the lag order number of observations from the past data.\n", "#This is because, the terms in the VAR model are essentially the lags of the various time series in the dataset, \n", "#so you need to provide it as many of the previous values as indicated by the lag order used by the model.\n", "#step1=2022.4 step2=2023.1 ... step5=2022.4\n", "\n", "\"-lag_order: -5 = means that use up to 5 lags\"\n", "\n", "#compare with test data\n", "\"when we compare the forecast values with the real values(observed/test) wee see that they are not close.\"\n", "\"so we can say that forecast performance with 11 lags is not good; we should change the lag order!\"\n", "\n", "pseudo_for=pd.DataFrame(pseudo_for, columns=['GDP','EXPORTS','INVESTMENT'])\n", "\n", "### out-of-sample forecast ##\n", "pseudo_for2=result_var.forecast(train_var.values[-lag_order:],steps=10)\n", "\"step=10 ==== 5 of them will be out of sample!!\"\n", "\"if the forecast performance is good then we can arrange more steps and get the predicted values\"\n", "pseudo_for2=pd.DataFrame(pseudo_for2, columns=['GDP','EXPORTS','INVESTMENT'])\n", "\n", "\n", "\n", "#  impulse-response functions  #\n", "\n", "irf = result_var.irf(20)\n", "irf.plot(orth=True)\n", "\n", "irf.plot(impulse='GDP') #just for the GDP\n", "irf.plot(impulse='EXPORTS') \n", "irf.plot(impulse='INVESTMENT') \n", "irf.plot_cum_effects(orth=True)\n", "#orth must be True or we cannot know which shock has the effect\n", "\n", "\n", "fevd = result_var.fevd(12)\n", "fevd.summary() # forecast error variance decomposition\n", "# FEVD for GDP: if there is a change exports, it can impact GDP; because the effect of export increases until period 7, and remains relatively steady after .\n", "result_var.fevd(12).plot() # it gives you the impact magnitudes visually \n", "\"\"\"\n", "Interpretation:\n", "# Dominant Influence of GDP: The forecast error variance of GDP is predominantly influenced by its own shocks, though this influence decreases over time. \n", "  Initially, GDP accounts for 100% of the variance, but it gradually drops to around 66% in the long term.\n", "# Increasing Influence of Exports: The contribution of exports to the forecast error variance of GDP increases over time, starting from 0% and rising to around 25-30%. \n", "  This indicates that exports become increasingly important in explaining the variations in GDP over time.\n", "# Moderate Influence of Investment: The contribution of investment to the forecast error variance of GDP is relatively small compared to exports, \n", "  but it increases steadily from 0% to about 8.7% in the long term.\n", "# Conclusion: The FEVD analysis indicates that while GDP's own shocks remain the largest contributor to its forecast error variance, \n", "  the influence of exports becomes more significant over time. Investment also plays a growing but smaller role. \n", "  This suggests that external factors such as exports have an increasing impact on the GDP variations, \n", "  highlighting the interconnectedness of the economy and the importance of trade dynamics.\n", "\"\"\"\n", "\n", "#### CAUSALITY CHECK ####\n", "# 1.way:\n", "print(result_var.test_causality('GDP', ['EXPORTS'], kind='f')) #f: F test\n", "#H0: EXPORTS does not Granger-cause GDP\n", "#Test statistic: 3.830 > critical value: 2.253: H0 is rejected.\n", "#It means that EXPORTS does Granger-cause GDP; \n", "#Therefore in order to incerase estimation and forecast performence for GDP, you should add EXPORTS on the right side!\n", "\n", "print(result_var.test_causality('GDP', ['EXPORTS',\"INVESTMENT\"], kind='f')) #f: F test\n", "#H0: EXPORTS and INVESTMENT do not Granger-cause GDP\n", "# Test statistic: 4.681, critical value: 1.872: H0 is rejected.\n", "#It means that EXPORTS and INVESTMENT do Granger-cause GDP; \n", "#Therefore in order to incerase estimation and forecast performence for GDP, you should add EXPORTS and INVESTMENT on the right side!\n", "\n", "\n", "# 2.way:\n", "from statsmodels.tsa.stattools import grangercausalitytests\n", "#re=grangercausalitytests(np.column_stack((train_var['GDP'],train_var['INVESTMENT'])),maxlag=10)\n", "\n", "maxlag=10\n", "test = 'ssr_chi2test'\n", "def grangers_causation_matrix(data, variables, test = 'ssr_chi2test', verbose=False):    \n", "    \"\"\"Check Granger Causality of all possible combinations of the Time series.\n", "    The rows are the response variable, columns are predictors. The values in the table \n", "    are the P-Values. P-Values lesser than the significance level (0.05), implies \n", "    the Null Hypothesis that the coefficients of the corresponding past values is \n", "    zero, that is, the X does not cause Y can be rejected.\n", "\n", "    data      : pandas dataframe containing the time series variables\n", "    variables : list containing names of the time series variables.\n", "    \"\"\"\n", "    df = pd.DataFrame(np.zeros((len(variables), len(variables))), columns=variables, index=variables)\n", "    for c in df.columns:\n", "        for r in df.index:\n", "            test_result = grangercausalitytests(data[[r, c]], maxlag=maxlag, verbose=False)\n", "            p_values = [round(test_result[i+1][0][test][1],4) for i in range(maxlag)]\n", "            if verbose: print(f'Y = {r}, X = {c}, P Values = {p_values}')\n", "            min_p_value = np.min(p_values)\n", "            df.loc[r, c] = min_p_value\n", "    df.columns = [var + '_x' for var in variables]\n", "    df.index = [var + '_y' for var in variables]\n", "    return df\n", "\n", "grangers_causation_matrix(train_var, variables = train_var.columns)  # result:\n", "\"\"\" \n", "              GDP_x  EXPORTS_x  INVESTMENT_x\n", "GDP_y           1.0        0.0           0.0\n", "EXPORTS_y       0.0        1.0           0.0\n", "INVESTMENT_y    0.0        0.0           1.0\n", "\"\"\"\n", "#If a given p-value is < significance level (0.05), \n", "#then, the corresponding X series (column) causes the Y (row).\n", "#For example, P-Value of 0.0 at (row 1, column 2) represents \n", "#the p-value of the Grangers Causality test for EXPORTS_x  causing GDP_y, \n", "#which is less that the significance level of 0.05.\n", "#So, you can reject the null hypothesis and conclude EXPORTS_x causes GDP_y.\n", "\n", "\n", "#############################################################################\n", "#########   order selection with information criteria  ###################\n", "\n", "# long-way\n", "for i in [1,2,3,4,5,6,7,8,9,10,11,12]:\n", "    result = model_var.fit(i)\n", "    print('Lag Order =', i)\n", "    print('AIC : ', result.aic)\n", "    print('BIC : ', result.bic)\n", "    print('FPE : ', result.fpe)\n", "    print('HQIC: ', result.hqic, '\\n')\n", "\n", "# short-way    \n", "x = model_var.select_order(maxlags=12)\n", "x.selected_orders\n", "\n", "\n", "\n", "##########################################################################################\n", "####    getting the level series: de-differencing just one time (1st difference case)  ###\n", "#########################################################################################\n", "basedate=train.iloc[-1:] # get the last observation from **original scale** \"train\" data\n", "merge=pd.concat([basedate,pseudo_for],axis=0, ignore_index=True) # merge last observation with the predicted data sample\n", "test_forecastlevels=merge.cumsum() # do cumulative sum to find predicted original scale values\n", "test_forecastlevels=test_forecastlevels.drop([0],axis=0) #drop the firs row which is the last observation from **original scale** \"train\" data\n", "test_forecastlevels=test_forecastlevels.reset_index(drop=True) #reset index to start index with 0\n", "\n", "## or \n", "\n", "\n", "def invert_transformation(df_train, df_forecast):\n", "    \"\"\"Revert back the differencing to get the forecast to original scale.\"\"\"\n", "    df_fc = df_forecast.copy()\n", "    columns = df_train.columns\n", "    for col in columns:\n", "        df_fc[str(col)+'_forecast'] = df_train[col].iloc[-1] + df_fc[str(col)].cumsum()\n", "    return df_fc\n", "\n", "# here we should use level train data\n", "df_results = invert_transformation(train, pseudo_for) \n", "#The forecasts are back to the original scale. \n", "\n", "\n", "\n", "##### comparing level_test and test_forecastlevels! ######\n", "\n", "\n", "def forecast_accuracy(forecast, actual):\n", "    mape = np.mean(np.abs(forecast - actual)/np.abs(actual))  # MAPE\n", "    me = np.mean(forecast - actual)             # ME\n", "    mae = np.mean(np.abs(forecast - actual))    # MAE\n", "    mpe = np.mean((forecast - actual)/actual)   # MPE\n", "    rmse = np.mean((forecast - actual)**2)**.5  # RMSE\n", "    corr = np.corrcoef(forecast, actual)[0,1]   # corr\n", "    return({'mape':mape, 'me':me, 'mae': mae, \n", "            'mpe': mpe, 'rmse':rmse, 'corr':corr})\n", "\n", "print('Forecast Accuracy of: GDP')\n", "accuracy_mod1_GDP = forecast_accuracy(df_results['GDP_forecast'].values, test_sample['GDP'].values)\n", "accuracy_mod1_GDP\n", "\n", "print('Forecast Accuracy of: EXPORTS')\n", "accuracy_mod1_EXPORTS = forecast_accuracy(df_results['EXPORTS_forecast'].values, test_sample['EXPORTS'])\n", "accuracy_mod1_EXPORTS\n", "\n", "print('Forecast Accuracy of: INVESTMENT')\n", "accuracy_mod1_INVESTMENT = forecast_accuracy(df_results['INVESTMENT_forecast'].values, test_sample['INVESTMENT'])\n", "accuracy_mod1_INVESTMENT\n", "\n", "##########################################################\n", "### one lag var model\n", "\n", "model_var2 = VAR(train_var)\n", "result_var2=model_var2.fit(1, trend='c')\n", "result_var2.summary()\n", "lag_order2 = result_var2.k_ar\n", "result_var2.plot_acorr()\n", "#we check whether the residuals are white noise or not; 3 dependent, 3 independent variable so 9 graphics\n", "\n", "pseudo_for2=result_var2.forecast(train_var.values[-lag_order2:],steps=5) \n", "pseudo_for2=pd.DataFrame(pseudo_for2,columns=['GDP','EXPORTS','INVESTMENT'])\n", "\n", "df_results2 = invert_transformation(train, pseudo_for2) \n", "#The forecasts are back to the original scale. \n", "\n", "\n", "print('Forecast Accuracy of: GDP')\n", "accuracy_mod2_GDP = forecast_accuracy(df_results['GDP_forecast'].values, test_sample['GDP'].values)\n", "accuracy_mod2_GDP\n", "\n", "print('Forecast Accuracy of: EXPORTS')\n", "accuracy_mod2_EXPORTS = forecast_accuracy(df_results['EXPORTS_forecast'].values, test_sample['EXPORTS'])\n", "accuracy_mod2_EXPORTS\n", "\n", "print('Forecast Accuracy of: INVESTMENT')\n", "accuracy_mod2_INVESTMENT = forecast_accuracy(df_results['INVESTMENT_forecast'].values, test_sample['INVESTMENT'])\n", "accuracy_mod2_INVESTMENT"]}, {"cell_type": "code", "execution_count": null, "id": "9686fa4f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}