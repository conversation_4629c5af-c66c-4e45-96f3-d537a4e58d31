{"cells": [{"cell_type": "code", "execution_count": 1, "id": "540c868f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "0\n", "1\n", "1\n", "2\n", "2\n", "3\n", "3\n", "4\n", "4\n", "5\n", "5\n", "6\n", "6\n", "7\n", "7\n", "8\n", "8\n", "9\n", "9\n", "10\n", "10\n", "11\n", "11\n", "12\n", "12\n", "13\n", "13\n", "14\n", "14\n", "15\n", "15\n", "16\n", "16\n", "17\n", "17\n", "18\n", "18\n", "19\n", "19\n", "20\n", "20\n", "21\n", "21\n", "22\n", "22\n", "23\n", "23\n", "24\n", "24\n", "25\n", "25\n", "26\n", "26\n", "27\n", "27\n", "28\n", "28\n", "29\n", "29\n", "30\n", "30\n", "31\n", "31\n", "32\n", "32\n", "33\n", "33\n", "34\n", "34\n", "35\n", "35\n", "   real_predicted\n", "0       76.552866\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\tsa\\statespace\\kalman_filter.py:2290: ValueWarning: Dynamic prediction specified to begin during out-of-sample forecasting period, and so has no effect.\n", "  warn('Dynamic prediction specified to begin during'\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "from pathlib import Path\n", "\n", "\n", "\n", "\n", "\n", "data_folder = Path(\"C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes/Applications\")\n", "\n", "df = pd.read_excel(data_folder /'dataset_TS.xlsx', sheet_name='TUR Capacity util and IND Prod')\n", "\n", "\n", "DATA=df['TURKEY CAPACITY UTILIZATION']\n", "DATA.dropna()\n", "Y=DATA\n", "Y=pd.DataFrame(Y)\n", "test_size=36\n", "train_size=len(Y)-test_size\n", "test_sample=Y[train_size:len(Y)]\n", "test_sample=test_sample.reset_index()\n", "lf=len(Y)-train_size\n", "matpredall=np.zeros((lf,1))\n", "matrix = np.zeros((1,1)) # Pre-allocate matrix\n", "\n", "testy=Y[train_size:len(Y)]\n", "testy=testy.reset_index()\n", "del testy['index']\n", "\n", "for j in range(lf):\n", "    print(j)\n", "    y_train=Y[0+j:train_size+j]\n", "    y_test=testy[0+j:1+j]\n", "    m=0\n", "    mod=sm.tsa.statespace.SARIMAX(y_train,trend='c',order=(1,1,0))\n", "    results=mod.fit(disp=False)\n", "    y_pred_ARIMA =results.get_prediction(start=train_size,end=train_size, dynamic=True)\n", "    pred_pseudo1=y_pred_ARIMA.predicted_mean\n", "    pred_pseudo1=pred_pseudo1.reset_index()\n", "    del pred_pseudo1['index']\n", "    pred_pseudo1.columns = ['predicted']\n", "    ypredict1=pred_pseudo1.values\n", "\n", "    matrix[:,m] = ypredict1 \n", "    m=m+1\n", "    print(j)   \n", "    mat<PERSON><PERSON>all[j,0]=matrix\n", "           \n", "matytraintest=Y[train_size:len(Y)]  \n", "matytraintest=np.array(matytraintest)\n", "lenmatytraintest=len( matytraintest)\n", "dfmatytraintest=pd.DataFrame(matytraintest)\n", "dfmatpredict=pd.Data<PERSON>rame(matpredall)\n", " \n", "fark=dfmatytraintest.values- dfmatpredict.values\n", "Mat_error=abs(fark) \n", "Mat_MAE=Mat_error.mean(0)\n", "Mat_MAE=Mat_MAE.tolist()\n", "  \n", "\n", "Mat_errorrate=(Mat_error/dfmatytraintest.values)*100\n", "Mat_MAPE=Mat_errorrate.mean(0)\n", "Mat_MAPE=Mat_MAPE.tolist()\n", "pseudo_predict_all=pd.DataFrame(matpredall)\n", "pseudo_predict_all.columns = ['predicted']\n", "\n", "#real out of sample predictions \n", "y_real_pred =results.get_prediction(start=len(Y),end=len(Y), dynamic=True)\n", "y_real_pred1=y_real_pred.predicted_mean\n", "y_real_pred1=y_real_pred1.reset_index()\n", "del y_real_pred1['index']\n", "y_real_pred1.columns = ['real_predicted']\n", "print(y_real_pred1 )"]}, {"cell_type": "code", "execution_count": 2, "id": "2e837444", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.9340424944980568]\n"]}], "source": ["print(Mat_MAPE)"]}, {"cell_type": "code", "execution_count": null, "id": "44023a8f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}