{"cells": [{"cell_type": "code", "execution_count": null, "id": "fcded24f", "metadata": {}, "outputs": [], "source": ["#VECTOR ERROR CORRECTION MODEL (VECM) and COINTEGRATION\n", " \n", "    #Time series models for VAR are usually based on applying VAR to stationary series with first differences \n", "    #to original series and because of that, there is always a possibility of loss of information about \n", "   # the relationship among integrated series.\n", "   # Therefore, differencing the series to make them stationary is one solution, \n", "   # but at the cost of ignoring possibly important (“long run”) relationships between the levels. \n", "#    A better solution is to test whether the levels regressions are trustworthy (“cointegration”).\n", "\n", "   # This cointegration concept origins in macroeconomics where series often seen as I(1) are\n", "  #  regressed onto, like private consumption, C, and disposable income, Yd.\n", "  #  Despite I(1), <PERSON><PERSON> and <PERSON> cannot diverge too much in either direction.\n", "  #  Because they share a common stochastic trend!\n", "\n", "  #  Through VECM we can interpret long term and short term equations. \n", "  #  In order to fit a VECM model, we need to determine the number of co-integrating relationships \n", " #   using a VEC rank test.\n", "\n", "\n", "\n", "#https://www.statsmodels.org/dev/generated/statsmodels.tsa.vector_ar.vecm.VECM.html\n", "import warnings\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import statsmodels.tsa.stattools as ts \n", "\n", "from pathlib import Path\n", "from arch.unitroot import ADF #augmented dickey fuller\n", "from arch.unitroot import DFGLS\n", "from arch.unitroot import PhillipsPerron\n", "from arch.unitroot import KPSS\n", "from statsmodels.tsa.api import VAR\n", "from statsmodels.tsa.api import VECM\n", "from statsmodels.tsa.stattools import coint\n", "from statsmodels.tsa.vector_ar.vecm import CointRankResults\n", "from statsmodels.tsa.vector_ar.vecm import coint_johansen\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "data_folder = Path(\"C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes/Applications\")\n", "\n", "data = pd.read_excel(data_folder /'datasetVAR.xlsx', sheet_name='Sayfa1')\n", "\n", "\n", "\n", "# Data contains the gdp, investment, export and import values of Turkey in turkish liras.\n", "idx = pd.date_range('1998-01-01', '2024-03-31', freq='Q')\n", "data.Date=idx\n", "df=data.set_index(\"Date\")\n", "df.columns\n", "\n", "\n", "# Engle-granger cointegration test-single approach\n", "x=df[['GDP',]]\n", "y=df[['EXPORTS', 'IMPORTS']]\n", "EG_coint=ts.coint(x, y)\n", "print(EG_coint)\n", "\n", "\n", "#unit root test: ADF\n", "print(ADF(df.GDP, trend=\"ct\"))\n", "print(ADF(df.EXPORTS, trend=\"ct\"))\n", "print(ADF(df.INVESTMENT, trend=\"ct\"))"]}, {"cell_type": "code", "execution_count": null, "id": "ffdcfb99", "metadata": {}, "outputs": [], "source": ["#  creation of VECM dataset\n", "vecmdata = df[['GDP','EXPORTS','IMPORTS']]\n", "train_vecm=vecmdata.iloc[0:100]\n", "test_vecm=vecmdata.iloc[-5:]\n", "vecmvalues=train_vecm.values   # converting dataframe into array to proceed with <PERSON><PERSON> test \n", "\n", "\n", "\n", "##### <PERSON><PERSON> test for cointegration ######\n", "##  coint_johansen(endog, det_order, k_ar_diff) ##\n", "<PERSON>_<PERSON>sen=coint_johansen(vecmvalues,0,2)\n", "#-1 - no deterministic terms; 0 - constant term; 1 - linear trend\n", "#the later is the Number of lagged differences in the model.\n", "trace_test=pd.DataFrame(Test_Johansen.lr1)\n", "trace_test.columns=[\"trace test stat\"]\n", "cvt=pd.DataFrame(Test_Johansen.cvt)\n", "cvt.columns=[\"0.1\",\"0.05\",\"0.01\"]\n", "Trace_test=pd.concat([trace_test,cvt],axis=1)\n", "print(Trace_test)\n", "meigen_test=pd.DataFrame(Test_Johansen.lr2)\n", "meigen_test.columns=[\"meigen test stat\"]\n", "cvm=pd.DataFrame(Test_Johansen.cvm)\n", "cvm.columns=[\"0.1\",\"0.05\",\"0.01\"]\n", "Meigen_test=pd.concat([meigen_test,cvm],axis=1)\n", "print(Meigen_test)\n", "#signif.levels: {0.1, 0.05, 0.01}\n", "\n", "\n", "### RULE: if both trace test statistics and max.eigen test statistics are greater than critical values, reject H0.\n", "#   for any critical values that you choose.\n", "#   r=1; there is one linear combination; there is one cointegration relationship!\"\n", "#   H0= no cointegration; HA: there is cointegration"]}, {"cell_type": "code", "execution_count": null, "id": "f1160f67", "metadata": {}, "outputs": [], "source": [" ########################################\n", "####  Vector Error Correction Model  #####\n", " ########################################\n", "model = VECM(train_vecm, k_ar_diff=2, coint_rank=1, deterministic='ci') \n", "vecm_res = model.fit()\n", "vecm_res.summary()\n", "\n", "# Loading coefficients (alpha) for equation Y: gives cointegration coefficient\n", "# as we set cointegration rank to 1, cointegration matrix contains just 1 column:\"1 linear combination\"\n", "# beta's are the coefficient of this linear combination equation\n", "\n", "vecm_res.plot_data(with_presample=True)\n", "vecmalphabeta=vecm_res.gamma.round(4) #coefficients\n", "\n", "### checking residuals if they follow WN or not \n", "residuals=vecm_res.resid\n", "\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "plot_acf(residuals[:,0]) #GDP\n", "plot_acf(residuals[:,1]) #export\n", "plot_acf(residuals[:,2]) #import\n", "\n", "## Forecast\n", "predicted_values=pd.DataFrame(vecm_res.predict(steps=5))\n", "predicted_values.columns=['GDP','EXPORTS','IMPORTS']\n", "\n", "\n", "forecast, lower, upper = vecm_res.predict(5, 0.05)\n", "print(\"lower bounds of confidence intervals:\")\n", "print(lower.round(3))\n", "print(\"\\npoint forecasts:\")\n", "print(forecast.round(1))\n", "print(\"\\nupper bounds of confidence intervals:\")\n", "print(upper.round(3))\n", "\n", "vecm_res.plot_forecast(steps=10) #out of sample forecast\n", "\n", "#impulse-response functions\n", "\n", "irf = vecm_res.irf(30)\n", "irf.plot(orth=True)\n", "\n", "irf.plot(impulse='GDP') #just for the GDP\n", "irf.plot(impulse='EXPORTS') \n", "irf.plot(impulse='IMPORTS') \n", "\n", "irf = vecm_res.irf(20)\n", "irf.plot(impulse='GDP') \n", "\n", "\n", "#### Model 2\n", "model2 = VECM(train_vecm, k_ar_diff=1, coint_rank=1, deterministic='ci') \n", "vecm_res2 = model2.fit()\n", "vecm_res2.summary()\n", "predicted_values2=pd.DataFrame(vecm_res2.predict(steps=5))\n", "predicted_values2.columns=['GDP','EXPORTS','IMPORTS']\n", "\n", "### checking residuals if they follow WN or not \n", "residuals=vecm_res2.resid\n", "\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "plot_acf(residuals[:,0]) #GDP\n", "plot_acf(residuals[:,1]) #export\n", "plot_acf(residuals[:,2]) #import\n", "\n", "\n", "## Calculation of the metrics\n", "def forecast_accuracy(forecast, actual):\n", "    mape = np.mean(np.abs(forecast - actual)/np.abs(actual))  # MAPE\n", "    mae = np.mean(np.abs(forecast - actual))    # MAE\n", "    rmse = np.mean((forecast - actual)**2)**.5  # RMSE\n", "    return({'mape':mape, 'mae': mae, \n", "             'rmse':rmse})\n", "\n", "test_values=test_vecm.reset_index(drop=True)  ## in order to perform calculation, index of two DFs must match"]}, {"cell_type": "code", "execution_count": null, "id": "3f7e3a8a", "metadata": {}, "outputs": [], "source": ["# Metrics for model 1\n", "metrics_for_GDP=forecast_accuracy(predicted_values.GDP, test_values.GDP)\n", "metrics_for_Export=forecast_accuracy(predicted_values.EXPORTS, test_values.EXPORTS)\n", "metrics_for_Import=forecast_accuracy(predicted_values.IMPORTS, test_values.IMPORTS)\n", "print(metrics_for_GDP)\n", "print(metrics_for_Export)\n", "print(metrics_for_Import)\n", "# Metrics for model 2\n", "metrics_for_GDP2=forecast_accuracy(predicted_values2.GDP, test_values.GDP)\n", "metrics_for_Export2=forecast_accuracy(predicted_values2.EXPORTS, test_values.EXPORTS)\n", "metrics_for_Import2=forecast_accuracy(predicted_values2.IMPORTS, test_values.IMPORTS)"]}, {"cell_type": "code", "execution_count": null, "id": "ad580dbc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}