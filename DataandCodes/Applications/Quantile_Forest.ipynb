{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b432e331", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n", "0\n", "1\n", "2\n", "3\n", "4\n", "0\n", "1\n", "2\n", "3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\regression\\quantile_regression.py:191: IterationLimitWarning: Maximum number of iterations (1000) reached.\n", "  warnings.warn(\"Maximum number of iterations (\" + str(max_iter) +\n", "C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\regression\\quantile_regression.py:191: IterationLimitWarning: Maximum number of iterations (1000) reached.\n", "  warnings.warn(\"Maximum number of iterations (\" + str(max_iter) +\n", "C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\regression\\quantile_regression.py:191: IterationLimitWarning: Maximum number of iterations (1000) reached.\n", "  warnings.warn(\"Maximum number of iterations (\" + str(max_iter) +\n"]}, {"name": "stdout", "output_type": "stream", "text": ["4\n", "0\n", "1\n", "2\n", "3\n", "4\n", "0\n", "1\n", "2\n", "3\n", "4\n", "0\n", "1\n", "2\n", "3\n", "4\n", "0\n", "1\n", "2\n", "3\n", "4\n", "0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\regression\\quantile_regression.py:191: IterationLimitWarning: Maximum number of iterations (1000) reached.\n", "  warnings.warn(\"Maximum number of iterations (\" + str(max_iter) +\n", "C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\regression\\quantile_regression.py:191: IterationLimitWarning: Maximum number of iterations (1000) reached.\n", "  warnings.warn(\"Maximum number of iterations (\" + str(max_iter) +\n", "C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\regression\\quantile_regression.py:191: IterationLimitWarning: Maximum number of iterations (1000) reached.\n", "  warnings.warn(\"Maximum number of iterations (\" + str(max_iter) +\n"]}, {"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "0\n", "1\n", "2\n", "3\n", "4\n", "   Real_Predicted_GDP\n", "0          524.966927\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "import matplotlib.pyplot as plt\n", "\n", "from pathlib import Path\n", "\n", "data_folder = Path(\"C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes\")\n", "\n", "data = pd.read_excel(data_folder /'ihracat.xlsx', sheet_name='Sayfa1')\n", "\n", "df2=data[['GDP','HouseholdConsumption','GoodServiceExport','GoodServiceImport']]\n", "\n", "Y=df2[['GDP']]\n", "x=df2[['HouseholdConsumption', 'GoodServiceExport', 'GoodServiceImport']]\n", "X=sm.add_constant(x)\n", "test_size=5\n", "train_size=len(Y)-test_size\n", "test_sample=Y[train_size:len(Y)]\n", "test_sample=test_sample.reset_index()\n", "lf=len(Y)-train_size\n", "matpredall=np.zeros((lf,1))\n", "\n", "matrix = np.zeros((1,1)) # Pre-allocate matrix\n", "\n", "\n", "testx=X[train_size:len(X)]\n", "testy=Y[train_size:len(X)]\n", "testx=testx.reset_index()\n", "del testx['index']\n", "testy=testy.reset_index()\n", "del testy['index']\n", "MAPE_ALL=list()\n", "def range_with_floats(start, stop, step):\n", "    while stop > start:\n", "        yield start\n", "        start += step\n", "       \n", "for qs in  range_with_floats(0.5, 0.95, 0.05):\n", "      for j in range(lf):\n", "                X_train=X[0+j:train_size+j]\n", "                y_train=Y[0+j:train_size+j]\n", "                X_test=testx[0+j:1+j]\n", "                y_test=testy[0+j:1+j]\n", "                df3=pd.concat([y_train,X_train],axis=1)\n", "                m=0\n", "                model = smf.quantreg('GDP ~ HouseholdConsumption+GoodServiceExport+GoodServiceImport',data=df3)\n", "                fitted = model.fit(q=qs)\n", "                fitted.summary()\n", "                fitted = model.fit(q=qs).predict()\n", "                y_pred = model.fit(q=qs).predict(X_test)\n", "                matrix[:,m] = y_pred\n", "                m=m+1\n", "                print(j)   \n", "                mat<PERSON><PERSON>all[j,0]=matrix\n", "                matytraintest=Y[train_size:len(Y)]  \n", "                matytraintest=np.array(matytraintest)\n", "                lenmatytraintest=len( matytraintest)\n", "                dfmatytraintest=pd.DataFrame(matytraintest)\n", "                dfmatpredict=pd.Data<PERSON>rame(matpredall)\n", "                fark=dfmatytraintest.values- dfmatpredict.values\n", "                Mat_error=abs(fark) \n", "                Mat_MAE=Mat_error.mean(0)\n", "                Mat_MAE=Mat_MAE.tolist()\n", "                Mat_errorrate=(Mat_error/dfmatytraintest.values)*100\n", "                Mat_MAPE=Mat_errorrate.mean(0)\n", "                Mat_MAPE=Mat_MAPE.tolist()\n", "                pseudo_predicted_price=pd.DataFrame(matpredall)\n", "                pseudo_predicted_price.columns=['Predicted_GDP']\n", "      MAPE_ALL.append(Mat_MAPE)\n", "      \n", "index_min = np.argmin(MAPE_ALL)\n", "qs_1=np.arange(0.5,0.95,0.05)\n", "qs=qs_1[index_min]\n", "#real out of sample prediction\n", "#verinin son satır <PERSON><PERSON>\n", "testx2=X[len(X)-1:len(X)]\n", "real_pred = model.fit(q=qs).predict(testx2)\n", "real_pred=real_pred.reset_index()\n", "del real_pred['index']\n", "real_pred.columns=['Real_Predicted_GDP']\n", "print(real_pred)"]}, {"cell_type": "code", "execution_count": null, "id": "1a8597ec", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}