# -*- coding: utf-8 -*-



import warnings
import pandas as pd
import numpy as np
import statsmodels.api as sm

from statsmodels.tsa.api import VAR
from statsmodels.tsa.api import VECM
from statsmodels.tools.eval_measures import rmse, aic
from statsmodels.tsa.stattools import acf
from pathlib import Path

data_folder = Path("C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes/Applications")

data = pd.read_excel(data_folder /'datasetVAR.xlsx', sheet_name='Sayfa1')

# Data contains the gdp, investment, export and import values of Turkey in turkish liras.
idx = pd.date_range('1998-01-01', '2024-03-31', freq='Q')
data.Date=idx
df=data.set_index("Date")
df.columns

df["GDP"].plot()
df["EXPORTS"].plot()
df["IMPORTS"].plot()
df["INVESTMENT"].plot()


## level data split## -- for later use
df2=df[['GDP','EXPORTS','INVESTMENT']]
train=df2.iloc[0:100] #1998.Q1-2022.Q4
test_sample=df2.iloc[-5:] #2022.Q4-2024.Q1


#creation of VAR dataset
vardata = df[['GDP','EXPORTS','INVESTMENT']]
vardata=vardata.diff().dropna() ### taking difference to make variables stationary
train_var=vardata.iloc[0:99] # 1998.Q2-2022.Q4
test_var=vardata.iloc[-5:]


model_var = VAR(train_var)

result_var_bic=model_var.fit( maxlags=15, ic='bic', trend='ct') #we leave the lag decision to information criteria
result_var_aic=model_var.fit( maxlags=15, ic='aic', trend='ct')
# or
result_var=model_var.fit(5, trend='ct') #we directly write the lag
stable1=result_var.is_stable(verbose=False) # when verbbose is true it gives "eigenvalue"s
print(stable1) 
"the result is true; then it is stable"
"if it says False, then we have to cahnge the lags and compare the results"
"lag number shouldnt be large generally, because more lags more roots; more likely to fail"

result_var.summary()
result_var.plot()
lag_order = result_var.k_ar # we define this for later use (in forecasting)

result_var.plot_acorr()
#we check whether the residuals are white noise or not; 3 dependent, 3 independent variable so 9 graphics

### in-sample forecast ###
pseudo_for=result_var.forecast(train_var.values[-lag_order:],steps=5) 
#In order to forecast, the VAR model expects up to the lag order number of observations from the past data.
#This is because, the terms in the VAR model are essentially the lags of the various time series in the dataset, 
#so you need to provide it as many of the previous values as indicated by the lag order used by the model.
#step1=2022.4 step2=2023.1 ... step5=2022.4

"-lag_order: -5 = means that use up to 5 lags"

#compare with test data
"when we compare the forecast values with the real values(observed/test) wee see that they are not close."
"so we can say that forecast performance with 11 lags is not good; we should change the lag order!"

pseudo_for=pd.DataFrame(pseudo_for, columns=['GDP','EXPORTS','INVESTMENT'])

### out-of-sample forecast ##
pseudo_for2=result_var.forecast(train_var.values[-lag_order:],steps=10)
"step=10 ==== 5 of them will be out of sample!!"
"if the forecast performance is good then we can arrange more steps and get the predicted values"
pseudo_for2=pd.DataFrame(pseudo_for2, columns=['GDP','EXPORTS','INVESTMENT'])



#  impulse-response functions  #

irf = result_var.irf(20)
irf.plot(orth=True)

irf.plot(impulse='GDP') #just for the GDP
irf.plot(impulse='EXPORTS') 
irf.plot(impulse='INVESTMENT') 
irf.plot_cum_effects(orth=True)
#orth must be True or we cannot know which shock has the effect


fevd = result_var.fevd(12)
fevd.summary() # forecast error variance decomposition
# FEVD for GDP: if there is a change exports, it can impact GDP; because the effect of export increases until period 7, and remains relatively steady after .
result_var.fevd(12).plot() # it gives you the impact magnitudes visually 
"""
Interpretation:
# Dominant Influence of GDP: The forecast error variance of GDP is predominantly influenced by its own shocks, though this influence decreases over time. 
  Initially, GDP accounts for 100% of the variance, but it gradually drops to around 66% in the long term.
# Increasing Influence of Exports: The contribution of exports to the forecast error variance of GDP increases over time, starting from 0% and rising to around 25-30%. 
  This indicates that exports become increasingly important in explaining the variations in GDP over time.
# Moderate Influence of Investment: The contribution of investment to the forecast error variance of GDP is relatively small compared to exports, 
  but it increases steadily from 0% to about 8.7% in the long term.
# Conclusion: The FEVD analysis indicates that while GDP's own shocks remain the largest contributor to its forecast error variance, 
  the influence of exports becomes more significant over time. Investment also plays a growing but smaller role. 
  This suggests that external factors such as exports have an increasing impact on the GDP variations, 
  highlighting the interconnectedness of the economy and the importance of trade dynamics.
"""

#### CAUSALITY CHECK ####
# 1.way:
print(result_var.test_causality('GDP', ['EXPORTS'], kind='f')) #f: F test
#H0: EXPORTS does not Granger-cause GDP
#Test statistic: 3.830 > critical value: 2.253: H0 is rejected.
#It means that EXPORTS does Granger-cause GDP; 
#Therefore in order to incerase estimation and forecast performence for GDP, you should add EXPORTS on the right side!

print(result_var.test_causality('GDP', ['EXPORTS',"INVESTMENT"], kind='f')) #f: F test
#H0: EXPORTS and INVESTMENT do not Granger-cause GDP
# Test statistic: 4.681, critical value: 1.872: H0 is rejected.
#It means that EXPORTS and INVESTMENT do Granger-cause GDP; 
#Therefore in order to incerase estimation and forecast performence for GDP, you should add EXPORTS and INVESTMENT on the right side!


# 2.way:
from statsmodels.tsa.stattools import grangercausalitytests
#re=grangercausalitytests(np.column_stack((train_var['GDP'],train_var['INVESTMENT'])),maxlag=10)

maxlag=10
test = 'ssr_chi2test'
def grangers_causation_matrix(data, variables, test = 'ssr_chi2test', verbose=False):    
    """Check Granger Causality of all possible combinations of the Time series.
    The rows are the response variable, columns are predictors. The values in the table 
    are the P-Values. P-Values lesser than the significance level (0.05), implies 
    the Null Hypothesis that the coefficients of the corresponding past values is 
    zero, that is, the X does not cause Y can be rejected.

    data      : pandas dataframe containing the time series variables
    variables : list containing names of the time series variables.
    """
    df = pd.DataFrame(np.zeros((len(variables), len(variables))), columns=variables, index=variables)
    for c in df.columns:
        for r in df.index:
            test_result = grangercausalitytests(data[[r, c]], maxlag=maxlag, verbose=False)
            p_values = [round(test_result[i+1][0][test][1],4) for i in range(maxlag)]
            if verbose: print(f'Y = {r}, X = {c}, P Values = {p_values}')
            min_p_value = np.min(p_values)
            df.loc[r, c] = min_p_value
    df.columns = [var + '_x' for var in variables]
    df.index = [var + '_y' for var in variables]
    return df

grangers_causation_matrix(train_var, variables = train_var.columns)  # result:
""" 
              GDP_x  EXPORTS_x  INVESTMENT_x
GDP_y           1.0        0.0           0.0
EXPORTS_y       0.0        1.0           0.0
INVESTMENT_y    0.0        0.0           1.0
"""
#If a given p-value is < significance level (0.05), 
#then, the corresponding X series (column) causes the Y (row).
#For example, P-Value of 0.0 at (row 1, column 2) represents 
#the p-value of the Grangers Causality test for EXPORTS_x  causing GDP_y, 
#which is less that the significance level of 0.05.
#So, you can reject the null hypothesis and conclude EXPORTS_x causes GDP_y.


#############################################################################
#########   order selection with information criteria  ###################

# long-way
for i in [1,2,3,4,5,6,7,8,9,10,11,12]:
    result = model_var.fit(i)
    print('Lag Order =', i)
    print('AIC : ', result.aic)
    print('BIC : ', result.bic)
    print('FPE : ', result.fpe)
    print('HQIC: ', result.hqic, '\n')

# short-way    
x = model_var.select_order(maxlags=12)
x.selected_orders



##########################################################################################
####    getting the level series: de-differencing just one time (1st difference case)  ###
#########################################################################################
basedate=train.iloc[-1:] # get the last observation from **original scale** "train" data
merge=pd.concat([basedate,pseudo_for],axis=0, ignore_index=True) # merge last observation with the predicted data sample
test_forecastlevels=merge.cumsum() # do cumulative sum to find predicted original scale values
test_forecastlevels=test_forecastlevels.drop([0],axis=0) #drop the firs row which is the last observation from **original scale** "train" data
test_forecastlevels=test_forecastlevels.reset_index(drop=True) #reset index to start index with 0

## or 


def invert_transformation(df_train, df_forecast):
    """Revert back the differencing to get the forecast to original scale."""
    df_fc = df_forecast.copy()
    columns = df_train.columns
    for col in columns:
        df_fc[str(col)+'_forecast'] = df_train[col].iloc[-1] + df_fc[str(col)].cumsum()
    return df_fc

# here we should use level train data
df_results = invert_transformation(train, pseudo_for) 
#The forecasts are back to the original scale. 



##### comparing level_test and test_forecastlevels! ######


def forecast_accuracy(forecast, actual):
    mape = np.mean(np.abs(forecast - actual)/np.abs(actual))  # MAPE
    me = np.mean(forecast - actual)             # ME
    mae = np.mean(np.abs(forecast - actual))    # MAE
    mpe = np.mean((forecast - actual)/actual)   # MPE
    rmse = np.mean((forecast - actual)**2)**.5  # RMSE
    corr = np.corrcoef(forecast, actual)[0,1]   # corr
    return({'mape':mape, 'me':me, 'mae': mae, 
            'mpe': mpe, 'rmse':rmse, 'corr':corr})

print('Forecast Accuracy of: GDP')
accuracy_mod1_GDP = forecast_accuracy(df_results['GDP_forecast'].values, test_sample['GDP'].values)
accuracy_mod1_GDP

print('Forecast Accuracy of: EXPORTS')
accuracy_mod1_EXPORTS = forecast_accuracy(df_results['EXPORTS_forecast'].values, test_sample['EXPORTS'])
accuracy_mod1_EXPORTS

print('Forecast Accuracy of: INVESTMENT')
accuracy_mod1_INVESTMENT = forecast_accuracy(df_results['INVESTMENT_forecast'].values, test_sample['INVESTMENT'])
accuracy_mod1_INVESTMENT

##########################################################
### one lag var model

model_var2 = VAR(train_var)
result_var2=model_var2.fit(1, trend='c')
result_var2.summary()
lag_order2 = result_var2.k_ar
result_var2.plot_acorr()
#we check whether the residuals are white noise or not; 3 dependent, 3 independent variable so 9 graphics

pseudo_for2=result_var2.forecast(train_var.values[-lag_order2:],steps=5) 
pseudo_for2=pd.DataFrame(pseudo_for2,columns=['GDP','EXPORTS','INVESTMENT'])

df_results2 = invert_transformation(train, pseudo_for2) 
#The forecasts are back to the original scale. 


print('Forecast Accuracy of: GDP')
accuracy_mod2_GDP = forecast_accuracy(df_results['GDP_forecast'].values, test_sample['GDP'].values)
accuracy_mod2_GDP

print('Forecast Accuracy of: EXPORTS')
accuracy_mod2_EXPORTS = forecast_accuracy(df_results['EXPORTS_forecast'].values, test_sample['EXPORTS'])
accuracy_mod2_EXPORTS

print('Forecast Accuracy of: INVESTMENT')
accuracy_mod2_INVESTMENT = forecast_accuracy(df_results['INVESTMENT_forecast'].values, test_sample['INVESTMENT'])
accuracy_mod2_INVESTMENT
