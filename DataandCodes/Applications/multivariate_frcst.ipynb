{"cells": [{"cell_type": "code", "execution_count": 1, "id": "58395dcb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_3060\\1617817327.py:11: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['price']=df[['Close']]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["['Date', 'price', 'price1', 'Volume', '<PERSON>ın', 'SP500', 'USDTRY', 'BIST100', 'Brent Petrol', 'Fiyat-Kazanç Oranı', 'Piyasa <PERSON>/De<PERSON>ğ<PERSON>', '2 yıllık tahvil faizi', 'enflasyon', 'TTM ROA', 'TTM ROE', 'beta', 'rvol', 'rvol_dolar', 'rvol_bist']\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.316e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:12   Log-Likelihood:                -1288.9\n", "No. Observations:                2103   AIC:                             2604.\n", "Df Residuals:                    2090   BIC:                             2677.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0364      0.168      0.217      0.828      -0.293       0.366\n", "price1                          0.9886      0.007    141.351      0.000       0.975       1.002\n", "Volume                      -1.257e-10   1.71e-10     -0.736      0.462   -4.61e-10    2.09e-10\n", "SP500                       -5.123e-05   3.44e-05     -1.490      0.136      -0.000    1.62e-05\n", "USDTRY                          0.0374      0.010      3.803      0.000       0.018       0.057\n", "BIST100                      -2.72e-05   4.32e-05     -0.629      0.530      -0.000    5.76e-05\n", "Fiyat-<PERSON><PERSON>             -0.0069      0.014     -0.513      0.608      -0.033       0.020\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1908      0.156      1.223      0.221      -0.115       0.497\n", "2 yıllık tahvil faizi           0.0016      0.004      0.454      0.650      -0.005       0.009\n", "enflasyon                      -0.0022      0.002     -1.160      0.246      -0.006       0.001\n", "TTM ROA                        -0.0590      0.085     -0.691      0.490      -0.227       0.109\n", "TTM ROE                         0.0042      0.010      0.406      0.685      -0.016       0.025\n", "rvol                           -0.0100      0.013     -0.758      0.448      -0.036       0.016\n", "==============================================================================\n", "Omnibus:                      698.237   <PERSON><PERSON><PERSON>-<PERSON>:                   1.968\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            20532.360\n", "Skew:                           0.944   Prob(JB):                         0.00\n", "Kurtosis:                      18.191   Cond. No.                     2.26e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.26e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "0\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.366e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:12   Log-Likelihood:                -1293.8\n", "No. Observations:                2103   AIC:                             2614.\n", "Df Residuals:                    2090   BIC:                             2687.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0432      0.168      0.257      0.797      -0.287       0.373\n", "price1                          0.9897      0.007    141.352      0.000       0.976       1.003\n", "Volume                      -1.271e-10   1.71e-10     -0.743      0.458   -4.63e-10    2.08e-10\n", "SP500                       -4.835e-05   3.45e-05     -1.403      0.161      -0.000    1.92e-05\n", "USDTRY                          0.0363      0.010      3.686      0.000       0.017       0.056\n", "BIST100                     -2.592e-05   4.33e-05     -0.598      0.550      -0.000    5.91e-05\n", "Fiyat-<PERSON><PERSON>             -0.0087      0.014     -0.645      0.519      -0.035       0.018\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1976      0.156      1.263      0.207      -0.109       0.504\n", "2 yıllık tahvil faizi           0.0011      0.004      0.302      0.763      -0.006       0.008\n", "enflasyon                      -0.0020      0.002     -1.076      0.282      -0.006       0.002\n", "TTM ROA                        -0.0620      0.086     -0.723      0.470      -0.230       0.106\n", "TTM ROE                         0.0040      0.010      0.383      0.702      -0.016       0.024\n", "rvol                           -0.0089      0.013     -0.675      0.500      -0.035       0.017\n", "==============================================================================\n", "Omnibus:                      673.657   <PERSON><PERSON><PERSON>-Watson:                   1.986\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19913.982\n", "Skew:                           0.886   Prob(JB):                         0.00\n", "Kurtosis:                      17.971   Cond. No.                     2.26e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.26e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "1\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.451e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1294.1\n", "No. Observations:                2103   AIC:                             2614.\n", "Df Residuals:                    2090   BIC:                             2688.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0411      0.168      0.244      0.807      -0.289       0.371\n", "price1                          0.9893      0.007    141.521      0.000       0.976       1.003\n", "Volume                      -1.277e-10   1.71e-10     -0.746      0.456   -4.63e-10    2.08e-10\n", "SP500                       -4.907e-05   3.45e-05     -1.424      0.155      -0.000    1.85e-05\n", "USDTRY                          0.0366      0.010      3.718      0.000       0.017       0.056\n", "BIST100                     -2.613e-05   4.34e-05     -0.603      0.547      -0.000    5.89e-05\n", "Fiyat-<PERSON><PERSON>             -0.0083      0.014     -0.611      0.541      -0.035       0.018\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1962      0.157      1.253      0.210      -0.111       0.503\n", "2 yıllık tahvil faizi           0.0012      0.004      0.343      0.732      -0.006       0.008\n", "enflasyon                      -0.0021      0.002     -1.097      0.273      -0.006       0.002\n", "TTM ROA                        -0.0613      0.086     -0.716      0.474      -0.229       0.107\n", "TTM ROE                         0.0041      0.010      0.389      0.697      -0.016       0.025\n", "rvol                           -0.0091      0.013     -0.691      0.490      -0.035       0.017\n", "==============================================================================\n", "Omnibus:                      680.207   <PERSON><PERSON><PERSON>-Watson:                   1.992\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19962.374\n", "Skew:                           0.903   Prob(JB):                         0.00\n", "Kurtosis:                      17.985   Cond. No.                     2.26e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.26e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "2\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.478e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1302.6\n", "No. Observations:                2103   AIC:                             2631.\n", "Df Residuals:                    2090   BIC:                             2705.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0520      0.169      0.308      0.758      -0.280       0.384\n", "price1                          0.9910      0.007    141.401      0.000       0.977       1.005\n", "Volume                      -1.319e-10   1.72e-10     -0.768      0.443   -4.69e-10    2.05e-10\n", "SP500                        -4.57e-05   3.46e-05     -1.320      0.187      -0.000    2.22e-05\n", "USDTRY                          0.0352      0.010      3.562      0.000       0.016       0.055\n", "BIST100                     -2.528e-05   4.35e-05     -0.581      0.561      -0.000    6.01e-05\n", "Fiyat-<PERSON><PERSON>             -0.0103      0.014     -0.760      0.447      -0.037       0.016\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1985      0.157      1.261      0.207      -0.110       0.507\n", "2 yıllık tahvil faizi           0.0006      0.004      0.163      0.870      -0.007       0.008\n", "enflasyon                      -0.0019      0.002     -1.007      0.314      -0.006       0.002\n", "TTM ROA                        -0.0656      0.086     -0.763      0.446      -0.234       0.103\n", "TTM ROE                         0.0039      0.010      0.375      0.707      -0.017       0.025\n", "rvol                           -0.0080      0.013     -0.606      0.545      -0.034       0.018\n", "==============================================================================\n", "Omnibus:                      653.118   <PERSON><PERSON><PERSON>-Watson:                   1.991\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19232.256\n", "Skew:                           0.840   Prob(JB):                         0.00\n", "Kurtosis:                      17.719   Cond. No.                     2.26e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.26e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "3\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.333e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1334.1\n", "No. Observations:                2103   AIC:                             2694.\n", "Df Residuals:                    2090   BIC:                             2768.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0706      0.172      0.411      0.681      -0.266       0.407\n", "price1                          0.9947      0.007    140.116      0.000       0.981       1.009\n", "Volume                       -1.36e-10   1.74e-10     -0.780      0.436   -4.78e-10    2.06e-10\n", "SP500                       -3.766e-05   3.51e-05     -1.072      0.284      -0.000    3.12e-05\n", "USDTRY                          0.0319      0.010      3.180      0.001       0.012       0.052\n", "BIST100                     -2.472e-05   4.42e-05     -0.560      0.576      -0.000    6.19e-05\n", "Fiyat-<PERSON><PERSON>             -0.0140      0.014     -1.020      0.308      -0.041       0.013\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1972      0.160      1.234      0.217      -0.116       0.511\n", "2 yıllık tahvil faizi          -0.0007      0.004     -0.198      0.843      -0.008       0.006\n", "enflasyon                      -0.0015      0.002     -0.810      0.418      -0.005       0.002\n", "TTM ROA                        -0.0725      0.087     -0.830      0.406      -0.244       0.099\n", "TTM ROE                         0.0036      0.011      0.341      0.733      -0.017       0.025\n", "rvol                           -0.0061      0.013     -0.454      0.650      -0.033       0.020\n", "==============================================================================\n", "Omnibus:                      668.894   <PERSON><PERSON><PERSON>-Watson:                   1.945\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            20741.260\n", "Skew:                           0.861   Prob(JB):                         0.00\n", "Kurtosis:                      18.289   Cond. No.                     2.26e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.26e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "4\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.410e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1338.5\n", "No. Observations:                2103   AIC:                             2703.\n", "Df Residuals:                    2090   BIC:                             2777.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0764      0.172      0.444      0.657      -0.261       0.414\n", "price1                          0.9964      0.007    140.523      0.000       0.983       1.010\n", "Volume                      -1.332e-10   1.75e-10     -0.762      0.446   -4.76e-10     2.1e-10\n", "SP500                       -3.394e-05   3.52e-05     -0.964      0.335      -0.000    3.51e-05\n", "USDTRY                          0.0303      0.010      3.021      0.003       0.011       0.050\n", "BIST100                     -2.497e-05   4.43e-05     -0.564      0.573      -0.000    6.19e-05\n", "Fiyat-<PERSON><PERSON>             -0.0154      0.014     -1.119      0.263      -0.042       0.012\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1960      0.160      1.223      0.222      -0.118       0.510\n", "2 yıllık tahvil faizi          -0.0013      0.004     -0.354      0.723      -0.009       0.006\n", "enflasyon                      -0.0014      0.002     -0.724      0.469      -0.005       0.002\n", "TTM ROA                        -0.0744      0.087     -0.850      0.395      -0.246       0.097\n", "TTM ROE                         0.0034      0.011      0.322      0.748      -0.017       0.024\n", "rvol                           -0.0052      0.013     -0.387      0.699      -0.032       0.021\n", "==============================================================================\n", "Omnibus:                      641.011   <PERSON><PERSON><PERSON>-Watson:                   1.950\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            20135.109\n", "Skew:                           0.792   Prob(JB):                         0.00\n", "Kurtosis:                      18.076   Cond. No.                     2.26e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.26e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "5\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.409e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1354.1\n", "No. Observations:                2103   AIC:                             2734.\n", "Df Residuals:                    2090   BIC:                             2808.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0874      0.173      0.504      0.614      -0.252       0.427\n", "price1                          1.0001      0.007    140.565      0.000       0.986       1.014\n", "Volume                      -1.396e-10   1.76e-10     -0.793      0.428   -4.85e-10    2.06e-10\n", "SP500                        -2.67e-05   3.54e-05     -0.753      0.451   -9.62e-05    4.28e-05\n", "USDTRY                          0.0274      0.010      2.710      0.007       0.008       0.047\n", "BIST100                     -2.711e-05   4.46e-05     -0.608      0.543      -0.000    6.04e-05\n", "Fiyat-<PERSON><PERSON>             -0.0176      0.014     -1.268      0.205      -0.045       0.010\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1850      0.161      1.145      0.252      -0.132       0.502\n", "2 yıllık tahvil faizi          -0.0024      0.004     -0.650      0.516      -0.010       0.005\n", "enflasyon                      -0.0011      0.002     -0.563      0.574      -0.005       0.003\n", "TTM ROA                        -0.0772      0.088     -0.876      0.381      -0.250       0.096\n", "TTM ROE                         0.0031      0.011      0.289      0.773      -0.018       0.024\n", "rvol                           -0.0034      0.014     -0.248      0.804      -0.030       0.023\n", "==============================================================================\n", "Omnibus:                      609.641   <PERSON><PERSON><PERSON>-Watson:                   1.930\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19510.560\n", "Skew:                           0.712   Prob(JB):                         0.00\n", "Kurtosis:                      17.854   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "6\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.494e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1358.2\n", "No. Observations:                2103   AIC:                             2742.\n", "Df Residuals:                    2090   BIC:                             2816.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0822      0.174      0.473      0.636      -0.258       0.423\n", "price1                          0.9979      0.007    140.767      0.000       0.984       1.012\n", "Volume                      -1.672e-10   1.76e-10     -0.949      0.343   -5.13e-10    1.78e-10\n", "SP500                       -3.025e-05   3.55e-05     -0.852      0.394   -9.99e-05    3.94e-05\n", "USDTRY                          0.0292      0.010      2.889      0.004       0.009       0.049\n", "BIST100                     -2.518e-05   4.47e-05     -0.563      0.573      -0.000    6.25e-05\n", "Fiyat-<PERSON><PERSON>             -0.0163      0.014     -1.174      0.241      -0.044       0.011\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1888      0.162      1.166      0.244      -0.129       0.506\n", "2 yıllık tahvil faizi          -0.0018      0.004     -0.497      0.620      -0.009       0.005\n", "enflasyon                      -0.0012      0.002     -0.640      0.522      -0.005       0.003\n", "TTM ROA                        -0.0774      0.088     -0.876      0.381      -0.251       0.096\n", "TTM ROE                         0.0034      0.011      0.318      0.751      -0.018       0.025\n", "rvol                           -0.0038      0.014     -0.282      0.778      -0.031       0.023\n", "==============================================================================\n", "Omnibus:                      632.963   <PERSON><PERSON><PERSON>-Watson:                   1.952\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19499.610\n", "Skew:                           0.779   Prob(JB):                         0.00\n", "Kurtosis:                      17.836   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "7\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.613e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1358.2\n", "No. Observations:                2103   AIC:                             2742.\n", "Df Residuals:                    2090   BIC:                             2816.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0821      0.174      0.472      0.637      -0.259       0.423\n", "price1                          0.9979      0.007    141.347      0.000       0.984       1.012\n", "Volume                      -1.672e-10   1.76e-10     -0.949      0.343   -5.13e-10    1.78e-10\n", "SP500                       -3.014e-05   3.55e-05     -0.849      0.396   -9.97e-05    3.95e-05\n", "USDTRY                          0.0291      0.010      2.890      0.004       0.009       0.049\n", "BIST100                     -2.519e-05   4.47e-05     -0.564      0.573      -0.000    6.24e-05\n", "Fiyat-<PERSON><PERSON>             -0.0163      0.014     -1.176      0.240      -0.044       0.011\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1890      0.162      1.167      0.243      -0.129       0.507\n", "2 yıllık tahvil faizi          -0.0019      0.004     -0.501      0.617      -0.009       0.005\n", "enflasyon                      -0.0012      0.002     -0.638      0.524      -0.005       0.003\n", "TTM ROA                        -0.0774      0.088     -0.876      0.381      -0.251       0.096\n", "TTM ROE                         0.0034      0.011      0.317      0.751      -0.018       0.025\n", "rvol                           -0.0038      0.014     -0.281      0.779      -0.031       0.023\n", "==============================================================================\n", "Omnibus:                      632.425   <PERSON><PERSON><PERSON>-<PERSON>:                   1.956\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19494.208\n", "Skew:                           0.778   Prob(JB):                         0.00\n", "Kurtosis:                      17.834   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "8\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.729e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1358.4\n", "No. Observations:                2103   AIC:                             2743.\n", "Df Residuals:                    2090   BIC:                             2816.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0791      0.174      0.455      0.649      -0.262       0.420\n", "price1                          0.9974      0.007    141.939      0.000       0.984       1.011\n", "Volume                      -1.674e-10   1.76e-10     -0.950      0.342   -5.13e-10    1.78e-10\n", "SP500                       -3.067e-05   3.55e-05     -0.864      0.388      -0.000    3.89e-05\n", "USDTRY                          0.0295      0.010      2.925      0.003       0.010       0.049\n", "BIST100                     -2.445e-05   4.47e-05     -0.547      0.584      -0.000    6.32e-05\n", "Fiyat-<PERSON><PERSON>             -0.0161      0.014     -1.162      0.245      -0.043       0.011\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1929      0.162      1.190      0.234      -0.125       0.511\n", "2 yıllık tahvil faizi          -0.0017      0.004     -0.473      0.637      -0.009       0.006\n", "enflasyon                      -0.0012      0.002     -0.647      0.517      -0.005       0.003\n", "TTM ROA                        -0.0770      0.088     -0.872      0.383      -0.250       0.096\n", "TTM ROE                         0.0034      0.011      0.315      0.753      -0.018       0.025\n", "rvol                           -0.0038      0.014     -0.280      0.779      -0.031       0.023\n", "==============================================================================\n", "Omnibus:                      638.977   <PERSON><PERSON><PERSON>-Watson:                   1.956\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19539.749\n", "Skew:                           0.796   Prob(JB):                         0.00\n", "Kurtosis:                      17.848   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "9\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.826e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1361.7\n", "No. Observations:                2103   AIC:                             2749.\n", "Df Residuals:                    2090   BIC:                             2823.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0847      0.174      0.487      0.627      -0.257       0.426\n", "price1                          0.9993      0.007    142.735      0.000       0.986       1.013\n", "Volume                      -1.731e-10   1.76e-10     -0.981      0.327   -5.19e-10    1.73e-10\n", "SP500                        -2.81e-05   3.55e-05     -0.791      0.429   -9.78e-05    4.16e-05\n", "USDTRY                          0.0286      0.010      2.836      0.005       0.009       0.048\n", "BIST100                      -2.83e-05   4.47e-05     -0.633      0.527      -0.000    5.94e-05\n", "Fiyat-<PERSON><PERSON>             -0.0168      0.014     -1.207      0.228      -0.044       0.010\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1830      0.162      1.126      0.260      -0.136       0.502\n", "2 yıllık tahvil faizi          -0.0022      0.004     -0.592      0.554      -0.009       0.005\n", "enflasyon                      -0.0012      0.002     -0.600      0.549      -0.005       0.003\n", "TTM ROA                        -0.0780      0.088     -0.882      0.378      -0.251       0.095\n", "TTM ROE                         0.0034      0.011      0.316      0.752      -0.018       0.025\n", "rvol                           -0.0036      0.014     -0.265      0.791      -0.030       0.023\n", "==============================================================================\n", "Omnibus:                      615.446   <PERSON><PERSON><PERSON>-Watson:                   1.957\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19090.568\n", "Skew:                           0.736   Prob(JB):                         0.00\n", "Kurtosis:                      17.687   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "10\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.917e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1366.5\n", "No. Observations:                2103   AIC:                             2759.\n", "Df Residuals:                    2090   BIC:                             2832.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0883      0.175      0.506      0.613      -0.254       0.431\n", "price1                          1.0019      0.007    143.761      0.000       0.988       1.016\n", "Volume                      -1.819e-10   1.77e-10     -1.029      0.304   -5.29e-10    1.65e-10\n", "SP500                       -2.455e-05   3.56e-05     -0.689      0.491   -9.44e-05    4.53e-05\n", "USDTRY                          0.0277      0.010      2.745      0.006       0.008       0.048\n", "BIST100                     -3.509e-05   4.48e-05     -0.784      0.433      -0.000    5.27e-05\n", "Fiyat-<PERSON><PERSON>             -0.0173      0.014     -1.240      0.215      -0.045       0.010\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1691      0.163      1.038      0.299      -0.150       0.488\n", "2 yıllık tahvil faizi          -0.0027      0.004     -0.736      0.462      -0.010       0.005\n", "enflasyon                      -0.0011      0.002     -0.548      0.584      -0.005       0.003\n", "TTM ROA                        -0.0789      0.089     -0.890      0.373      -0.253       0.095\n", "TTM ROE                         0.0035      0.011      0.325      0.745      -0.018       0.025\n", "rvol                           -0.0036      0.014     -0.260      0.795      -0.030       0.023\n", "==============================================================================\n", "Omnibus:                      588.938   <PERSON><PERSON><PERSON>-Watson:                   1.951\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            18590.849\n", "Skew:                           0.666   Prob(JB):                         0.00\n", "Kurtosis:                      17.505   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "11\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.707e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1405.0\n", "No. Observations:                2103   AIC:                             2836.\n", "Df Residuals:                    2090   BIC:                             2909.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0861      0.178      0.484      0.628      -0.263       0.435\n", "price1                          0.9939      0.007    141.186      0.000       0.980       1.008\n", "Volume                      -1.635e-10    1.8e-10     -0.908      0.364   -5.17e-10     1.9e-10\n", "SP500                       -3.549e-05   3.63e-05     -0.978      0.328      -0.000    3.56e-05\n", "USDTRY                          0.0298      0.010      2.901      0.004       0.010       0.050\n", "BIST100                     -1.089e-05   4.55e-05     -0.239      0.811      -0.000    7.84e-05\n", "Fiyat-<PERSON><PERSON>             -0.0164      0.014     -1.156      0.248      -0.044       0.011\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2119      0.166      1.276      0.202      -0.114       0.537\n", "2 yıllık tahvil faizi          -0.0012      0.004     -0.318      0.750      -0.009       0.006\n", "enflasyon                      -0.0013      0.002     -0.668      0.505      -0.005       0.003\n", "TTM ROA                        -0.0779      0.090     -0.863      0.388      -0.255       0.099\n", "TTM ROE                         0.0031      0.011      0.285      0.775      -0.018       0.025\n", "rvol                           -0.0036      0.014     -0.256      0.798      -0.031       0.024\n", "==============================================================================\n", "Omnibus:                      575.342   <PERSON><PERSON><PERSON>-Watson:                   1.939\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            22575.022\n", "Skew:                           0.556   Prob(JB):                         0.00\n", "Kurtosis:                      19.012   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "12\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.812e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1405.6\n", "No. Observations:                2103   AIC:                             2837.\n", "Df Residuals:                    2090   BIC:                             2911.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0869      0.178      0.488      0.626      -0.262       0.436\n", "price1                          0.9931      0.007    141.747      0.000       0.979       1.007\n", "Volume                      -1.602e-10    1.8e-10     -0.889      0.374   -5.14e-10    1.93e-10\n", "SP500                       -3.691e-05   3.63e-05     -1.017      0.309      -0.000    3.43e-05\n", "USDTRY                          0.0301      0.010      2.922      0.004       0.010       0.050\n", "BIST100                     -8.738e-06   4.55e-05     -0.192      0.848   -9.79e-05    8.05e-05\n", "Fiyat-<PERSON><PERSON>             -0.0162      0.014     -1.142      0.254      -0.044       0.012\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2138      0.166      1.287      0.198      -0.112       0.540\n", "2 yıllık tahvil faizi          -0.0010      0.004     -0.268      0.788      -0.008       0.006\n", "enflasyon                      -0.0014      0.002     -0.688      0.492      -0.005       0.003\n", "TTM ROA                        -0.0777      0.090     -0.860      0.390      -0.255       0.099\n", "TTM ROE                         0.0031      0.011      0.285      0.775      -0.018       0.025\n", "rvol                           -0.0037      0.014     -0.268      0.789      -0.031       0.024\n", "==============================================================================\n", "Omnibus:                      583.499   <PERSON><PERSON><PERSON>-Watson:                   1.963\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            22514.104\n", "Skew:                           0.583   Prob(JB):                         0.00\n", "Kurtosis:                      18.987   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "13\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.919e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1406.1\n", "No. Observations:                2103   AIC:                             2838.\n", "Df Residuals:                    2090   BIC:                             2912.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0886      0.178      0.497      0.619      -0.261       0.438\n", "price1                          0.9938      0.007    142.433      0.000       0.980       1.007\n", "Volume                      -1.616e-10    1.8e-10     -0.897      0.370   -5.15e-10    1.92e-10\n", "SP500                       -3.612e-05   3.63e-05     -0.995      0.320      -0.000    3.51e-05\n", "USDTRY                          0.0299      0.010      2.903      0.004       0.010       0.050\n", "BIST100                     -1.051e-05   4.55e-05     -0.231      0.817   -9.97e-05    7.86e-05\n", "Fiyat-<PERSON><PERSON>             -0.0164      0.014     -1.155      0.248      -0.044       0.011\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2102      0.166      1.264      0.206      -0.116       0.536\n", "2 yıllık tahvil faizi          -0.0012      0.004     -0.307      0.759      -0.009       0.006\n", "enflasyon                      -0.0013      0.002     -0.677      0.498      -0.005       0.003\n", "TTM ROA                        -0.0782      0.090     -0.865      0.387      -0.255       0.099\n", "TTM ROE                         0.0032      0.011      0.289      0.773      -0.018       0.025\n", "rvol                           -0.0037      0.014     -0.266      0.791      -0.031       0.024\n", "==============================================================================\n", "Omnibus:                      575.426   <PERSON><PERSON><PERSON>-Watson:                   1.965\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            22467.695\n", "Skew:                           0.558   Prob(JB):                         0.00\n", "Kurtosis:                      18.974   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "14\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 9.032e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1406.1\n", "No. Observations:                2103   AIC:                             2838.\n", "Df Residuals:                    2090   BIC:                             2912.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0890      0.178      0.499      0.618      -0.261       0.439\n", "price1                          0.9941      0.007    143.232      0.000       0.980       1.008\n", "Volume                      -1.608e-10    1.8e-10     -0.892      0.372   -5.14e-10    1.93e-10\n", "SP500                       -3.584e-05   3.63e-05     -0.987      0.324      -0.000    3.54e-05\n", "USDTRY                          0.0298      0.010      2.897      0.004       0.010       0.050\n", "BIST100                     -1.136e-05   4.54e-05     -0.250      0.802      -0.000    7.77e-05\n", "Fiyat-<PERSON><PERSON>             -0.0165      0.014     -1.160      0.246      -0.044       0.011\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2089      0.166      1.255      0.209      -0.117       0.535\n", "2 yıllık tahvil faizi          -0.0012      0.004     -0.320      0.749      -0.009       0.006\n", "enflasyon                      -0.0013      0.002     -0.673      0.501      -0.005       0.003\n", "TTM ROA                        -0.0782      0.090     -0.865      0.387      -0.255       0.099\n", "TTM ROE                         0.0032      0.011      0.289      0.772      -0.018       0.025\n", "rvol                           -0.0037      0.014     -0.267      0.790      -0.031       0.024\n", "==============================================================================\n", "Omnibus:                      572.447   <PERSON><PERSON><PERSON>-Watson:                   1.966\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            22464.670\n", "Skew:                           0.548   Prob(JB):                         0.00\n", "Kurtosis:                      18.974   Cond. No.                     2.29e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.29e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "15\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.904e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1432.7\n", "No. Observations:                2103   AIC:                             2891.\n", "Df Residuals:                    2090   BIC:                             2965.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0755      0.181      0.418      0.676      -0.279       0.430\n", "price1                          0.9898      0.007    141.286      0.000       0.976       1.004\n", "Volume                      -1.436e-10   1.82e-10     -0.787      0.431   -5.01e-10    2.14e-10\n", "SP500                       -4.314e-05   3.68e-05     -1.173      0.241      -0.000     2.9e-05\n", "USDTRY                          0.0323      0.010      3.102      0.002       0.012       0.053\n", "BIST100                     -2.347e-06    4.6e-05     -0.051      0.959   -9.25e-05    8.78e-05\n", "Fiyat-<PERSON><PERSON>             -0.0137      0.014     -0.953      0.341      -0.042       0.014\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2151      0.169      1.275      0.202      -0.116       0.546\n", "2 yıllık tahvil faizi           0.0005      0.004      0.119      0.905      -0.007       0.008\n", "enflasyon                      -0.0020      0.002     -0.981      0.327      -0.006       0.002\n", "TTM ROA                        -0.0912      0.091     -0.997      0.319      -0.271       0.088\n", "TTM ROE                         0.0052      0.011      0.469      0.639      -0.017       0.027\n", "rvol                           -0.0045      0.014     -0.319      0.750      -0.032       0.023\n", "==============================================================================\n", "Omnibus:                      569.417   <PERSON><PERSON><PERSON>-Watson:                   1.940\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            23293.105\n", "Skew:                           0.524   Prob(JB):                         0.00\n", "Kurtosis:                      19.270   Cond. No.                     2.29e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.29e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "16\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 8.969e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1436.0\n", "No. Observations:                2103   AIC:                             2898.\n", "Df Residuals:                    2090   BIC:                             2971.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0680      0.181      0.375      0.707      -0.287       0.423\n", "price1                          0.9885      0.007    141.135      0.000       0.975       1.002\n", "Volume                      -1.356e-10   1.83e-10     -0.742      0.458   -4.94e-10    2.23e-10\n", "SP500                       -4.499e-05   3.69e-05     -1.221      0.222      -0.000    2.73e-05\n", "USDTRY                          0.0331      0.010      3.172      0.002       0.013       0.053\n", "BIST100                     -5.244e-07    4.6e-05     -0.011      0.991   -9.08e-05    8.98e-05\n", "Fiyat-<PERSON><PERSON>             -0.0126      0.014     -0.877      0.381      -0.041       0.016\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2170      0.169      1.283      0.200      -0.115       0.549\n", "2 yıllık tahvil faizi           0.0010      0.004      0.258      0.796      -0.006       0.008\n", "enflasyon                      -0.0022      0.002     -1.079      0.281      -0.006       0.002\n", "TTM ROA                        -0.0947      0.092     -1.034      0.301      -0.274       0.085\n", "TTM ROE                         0.0059      0.011      0.527      0.598      -0.016       0.028\n", "rvol                           -0.0049      0.014     -0.347      0.729      -0.033       0.023\n", "==============================================================================\n", "Omnibus:                      582.805   <PERSON><PERSON><PERSON>-Watson:                   1.942\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            22973.738\n", "Skew:                           0.574   Prob(JB):                         0.00\n", "Kurtosis:                      19.151   Cond. No.                     2.29e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.29e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "17\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 9.027e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1439.6\n", "No. Observations:                2103   AIC:                             2905.\n", "Df Residuals:                    2090   BIC:                             2979.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0630      0.182      0.347      0.729      -0.293       0.419\n", "price1                          0.9875      0.007    140.916      0.000       0.974       1.001\n", "Volume                      -1.308e-10   1.83e-10     -0.715      0.475    -4.9e-10    2.28e-10\n", "SP500                       -4.711e-05   3.69e-05     -1.276      0.202      -0.000    2.53e-05\n", "USDTRY                          0.0338      0.010      3.238      0.001       0.013       0.054\n", "BIST100                      5.111e-07   4.61e-05      0.011      0.991   -8.99e-05     9.1e-05\n", "Fiyat-<PERSON><PERSON>             -0.0114      0.014     -0.791      0.429      -0.040       0.017\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2132      0.169      1.258      0.209      -0.119       0.546\n", "2 yıllık tahvil faizi           0.0015      0.004      0.399      0.690      -0.006       0.009\n", "enflasyon                      -0.0024      0.002     -1.183      0.237      -0.006       0.002\n", "TTM ROA                        -0.0981      0.092     -1.070      0.285      -0.278       0.082\n", "TTM ROE                         0.0066      0.011      0.591      0.555      -0.015       0.029\n", "rvol                           -0.0054      0.014     -0.380      0.704      -0.033       0.022\n", "==============================================================================\n", "Omnibus:                      596.060   <PERSON><PERSON><PERSON>-Watson:                   1.933\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            22676.262\n", "Skew:                           0.621   Prob(JB):                         0.00\n", "Kurtosis:                      19.039   Cond. No.                     2.29e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.29e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "18\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 9.040e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1447.5\n", "No. Observations:                2103   AIC:                             2921.\n", "Df Residuals:                    2090   BIC:                             2994.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0591      0.182      0.324      0.746      -0.298       0.417\n", "price1                          0.9864      0.007    140.318      0.000       0.973       1.000\n", "Volume                      -1.178e-10   1.84e-10     -0.641      0.522   -4.78e-10    2.43e-10\n", "SP500                        -5.13e-05   3.71e-05     -1.385      0.166      -0.000    2.14e-05\n", "USDTRY                          0.0351      0.010      3.348      0.001       0.015       0.056\n", "BIST100                      6.807e-07   4.63e-05      0.015      0.988   -9.01e-05    9.15e-05\n", "Fiyat-<PERSON><PERSON>             -0.0094      0.014     -0.649      0.516      -0.038       0.019\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2025      0.170      1.190      0.234      -0.131       0.536\n", "2 yıllık tahvil faizi           0.0023      0.004      0.612      0.541      -0.005       0.010\n", "enflasyon                      -0.0027      0.002     -1.355      0.176      -0.007       0.001\n", "TTM ROA                        -0.1038      0.092     -1.127      0.260      -0.284       0.077\n", "TTM ROE                         0.0078      0.011      0.694      0.488      -0.014       0.030\n", "rvol                           -0.0063      0.014     -0.444      0.657      -0.034       0.022\n", "==============================================================================\n", "Omnibus:                      609.015   <PERSON><PERSON><PERSON>-Watson:                   1.918\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            22257.256\n", "Skew:                           0.667   Prob(JB):                         0.00\n", "Kurtosis:                      18.882   Cond. No.                     2.30e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.3e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "19\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 9.122e+04\n", "Date:                Wed, 12 Jun 2024   Prob (F-statistic):               0.00\n", "Time:                        21:15:13   Log-Likelihood:                -1447.4\n", "No. Observations:                2103   AIC:                             2921.\n", "Df Residuals:                    2090   BIC:                             2994.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                           0.0569      0.182      0.312      0.755      -0.301       0.415\n", "price1                          0.9864      0.007    140.369      0.000       0.973       1.000\n", "Volume                       -1.19e-10   1.84e-10     -0.648      0.517   -4.79e-10    2.41e-10\n", "SP500                       -5.068e-05   3.71e-05     -1.368      0.172      -0.000     2.2e-05\n", "USDTRY                          0.0350      0.010      3.345      0.001       0.014       0.056\n", "BIST100                      6.496e-07   4.63e-05      0.014      0.989   -9.01e-05    9.14e-05\n", "Fiyat-<PERSON><PERSON>             -0.0095      0.014     -0.659      0.510      -0.038       0.019\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2059      0.170      1.209      0.227      -0.128       0.540\n", "2 yıllık tahvil faizi           0.0023      0.004      0.595      0.552      -0.005       0.010\n", "enflasyon                      -0.0027      0.002     -1.339      0.181      -0.007       0.001\n", "TTM ROA                        -0.1032      0.092     -1.121      0.263      -0.284       0.077\n", "TTM ROE                         0.0077      0.011      0.684      0.494      -0.014       0.030\n", "rvol                           -0.0062      0.014     -0.434      0.664      -0.034       0.022\n", "==============================================================================\n", "Omnibus:                      607.747   <PERSON><PERSON><PERSON>-Watson:                   1.926\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            22259.935\n", "Skew:                           0.663   Prob(JB):                         0.00\n", "Kurtosis:                      18.883   Cond. No.                     2.30e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.3e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "20\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "from pathlib import Path\n", "\n", "def var_lags(data,nlags):\n", "        alldata=data\n", "        str1='price'\n", "        alldata=alldata.drop([\"Open\",\"High\",\"Low\"],axis=1)\n", "        df=alldata[['Date','Close']]\n", "        df['price']=df[['Close']]\n", "        df2=df.drop([\"Close\"],axis=1)\n", "        for i in range(nlags):\n", "            df2[str1+str(i+1)]=df2['price'].shift(i+1)\n", "        return df2\n", "\n", "def dgpprice(alldata,lags):\n", "\n", "\n", "    \n", "    \n", "    df=alldata.set_index(\"Date\")\n", "    \n", "    data=df[[\"Close\",\"BIST100\",\"USDTRY\"]]\n", "    data_r=np.log(data).diff()\n", "    data_r=data_r.dropna()\n", "    data_r=data_r*100\n", "   \n", "    \n", "   # \"\"\"realized volatility hesaplaması\"\"\"\n", "    data2=data_r/100 ## eğer returnler 100 ile çarpılı halde ise bölmek gerekiyor\n", "    RT=data2[\"Close\"]**2 \n", "    RTm=RT.rolling(21).mean()\n", "#    Realvol=100*np.sqrt(RTm*252) #annualized_volatility\n", "    Realvol_daily=100*np.sqrt(RTm) #daily_volatility\n", "    rvol=pd.DataFrame(Realvol_daily)\n", "    rvol.columns=[\"rvol\"]\n", "   \n", "    \n", "    RT_dolar=data2[\"USDTRY\"]**2 \n", "    RTm_dolar=RT_dolar.rolling(21).mean()\n", "#    Realvol_dolar=100*np.sqrt(RTm_dolar*252) #annualized_volatility\n", "    Realvol_dolar_daily=100*np.sqrt(RTm_dolar) #daily_volatility\n", "    rvol_dolar=pd.DataFrame(Realvol_dolar_daily)\n", "    rvol_dolar.columns=[\"rvol_dolar\"]\n", "    \n", "    \n", "    RT_bist=data2[\"BIST100\"]**2 \n", "    RTm_bist=RT_bist.rolling(21).mean()\n", "#    Realvol_bist_an=100*np.sqrt(RTm_bist*252) annualized_volatility\n", "    Realvol_bist_daily=100*np.sqrt(RTm_bist) #daily_volatility\n", "    rvol_bist=pd.DataFrame(Realvol_bist_daily)\n", "    rvol_bist.columns=[\"rvol_bist\"]\n", "    \n", "    \n", "   # \"\"\" beta hesaplaması\"\"\"\n", "    betadf=data_r[[\"Close\", \"BIST100\"]]\n", "    bist100_return=betadf[[\"BIST100\"]]\n", "    varbist=pd.DataFrame(bist100_return.rolling(21).var())\n", "    covariance=pd.DataFrame(betadf.rolling(21).cov().unstack()['Close']['BIST100'])\n", "    covariance.columns=[\"kovaryans\"]\n", "    beta=pd.DataFrame(covariance.kovaryans/varbist.BIST100)\n", "    beta.columns=[\"beta\"]\n", "     \n", "    \n", "\n", "#    indikatörler2=indikatörler.shift(1)\n", "    indicators=pd.concat([beta,rvol,rvol_dolar,rvol_bist],axis=1)\n", "\n", "    df=df.iloc[:,4:len(df)]\n", "    df2=pd.concat([df,indicators],axis=1)\n", "    df3=df2.shift(1)\n", "    Price=alldata.iloc[:,1]\n", "    Price=pd.DataFrame(Price)\n", "    dfyeni=var_lags(alldata,1)\n", "    dfyeni=dfyeni.set_index(\"Date\")\n", "    alldatason=pd.concat([d<PERSON><PERSON>,df3],axis=1)\n", "    alldatason2=alldatason.dropna()\n", "    alldatason3=alldatason2.reset_index()\n", "    return alldatason3\n", "data_folder = Path(\"C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes/Applications\")\n", "alldata=pd.read_excel(data_folder/'akbank2.xlsx', sheet_name='DATA')\n", "# data with one time lag \n", "alldata2=dgpprice(alldata,1)\n", "writer = pd.ExcelWriter(data_folder/'akbnk_adjusted.xlsx', engine='xlsxwriter')\n", "alldata2.to_excel(writer, sheet_name='akbank',index = False)\n", "writer.save()\n", "alldata2=pd.read_excel(data_folder/'akbnk_adjusted.xlsx', sheet_name='akbank')\n", "column_names = list(alldata2.columns)\n", "print(column_names)\n", "len_data=len(column_names)\n", "X=alldata2[['price1','Volume','SP500','USDTRY','BIST100','Fiyat-Kazanç Oranı', '<PERSON>yasa <PERSON>/<PERSON><PERSON>', '2 yıllık tahvil faizi', 'enflasyon', 'TTM ROA', 'TTM ROE','rvol']]\n", "corr1=X.corr()\n", "X=sm.add_constant(X)\n", "\n", "Y=alldata2.iloc[:,1:2]  \n", "Y=pd.DataFrame(Y)\n", "\n", "test_size=21\n", "train_size=len(Y)-test_size\n", "test_sample=Y[train_size:len(Y)]\n", "test_sample=test_sample.reset_index()\n", "lf=len(Y)-train_size\n", "matpredall=np.zeros((lf,1))\n", "matrix = np.zeros((1,1)) # Pre-allocate matrix\n", "\n", "testx=X[train_size:len(X)]\n", "testy=Y[train_size:len(X)]\n", "testx=testx.reset_index()\n", "del testx['index']\n", "testy=testy.reset_index()\n", "del testy['index']\n", "\n", "for j in range(lf):\n", "    X_train=X[0+j:train_size+j]\n", "    y_train=Y[0+j:train_size+j]\n", "    X_test=testx[0+j:1+j]\n", "    y_test=testy[0+j:1+j]\n", "    m=0\n", "    results = sm.OLS(endog=y_train, exog=X_train).fit()\n", "    print(results.summary())\n", "    y_pred_OLS = results.predict(X_test)\n", "    matrix[:,m] = y_pred_OLS\n", "    m=m+1\n", "    print(j)   \n", "    mat<PERSON><PERSON>all[j,0]=matrix\n", "           \n", "matytraintest=Y[train_size:len(Y)]  \n", "matytraintest=np.array(matytraintest)\n", "lenmatytraintest=len( matytraintest)\n", "dfmatytraintest=pd.DataFrame(matytraintest)\n", "dfmatpredict=pd.Data<PERSON>rame(matpredall)\n", " \n", "fark=dfmatytraintest.values- dfmatpredict.values\n", "Mat_error=abs(fark) \n", "Mat_MAE=Mat_error.mean(0)\n", "Mat_MAE=Mat_MAE.tolist()\n", "  \n", "\n", "Mat_errorrate=(Mat_error/dfmatytraintest.values)*100\n", "Mat_MAPE=Mat_errorrate.mean(0)\n", "Mat_MAPE=Mat_MAPE.tolist()\n", "pseudo_predicted_price=pd.DataFrame(matpredall)\n", "pseudo_predicted_price.columns=['Predicted_Prices']\n", "\n", "#real out of sample prediction\n", "#verinin son satır <PERSON><PERSON>\n", "testx2=X[len(X)-1:len(X)]\n", "real_pred = results.predict(testx2)\n", "real_pred=real_pred.reset_index()\n", "del real_pred['index']\n", "real_pred.columns=['Real_Predicted_Prices']"]}, {"cell_type": "code", "execution_count": 2, "id": "8ff9bf33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2.282309579541983]\n"]}], "source": ["print(Mat_MAPE)"]}, {"cell_type": "code", "execution_count": 3, "id": "2434233f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Real_Predicted_Prices\n", "0              59.509644\n"]}], "source": ["print(real_pred)"]}, {"cell_type": "code", "execution_count": null, "id": "c122b409", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}