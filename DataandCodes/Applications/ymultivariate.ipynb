{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b1596a0a", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26916\\806970289.py:11: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['price']=df[['Close']]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["['Date', 'price', 'price1', 'Volume', '<PERSON>ın', 'SP500', 'USDTRY', 'BIST100', 'Brent Petrol', 'Fiyat-Kazanç Oranı', 'Piyasa <PERSON>/De<PERSON>ğ<PERSON>', '2 yıllık tahvil faizi', 'enflasyon', 'TTM ROA', 'TTM ROE', 'beta', 'rvol', 'rvol_dolar', 'rvol_bist']\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.384e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:55   Log-Likelihood:                -890.76\n", "No. Observations:                2057   AIC:                             1808.\n", "Df Residuals:                    2044   BIC:                             1881.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0606      0.142     -0.425      0.671      -0.340       0.219\n", "price1                          0.9680      0.008    123.800      0.000       0.953       0.983\n", "Volume                      -7.667e-12   1.44e-10     -0.053      0.957   -2.89e-10    2.74e-10\n", "SP500                       -6.152e-05   2.92e-05     -2.106      0.035      -0.000   -4.23e-06\n", "USDTRY                          0.0347      0.008      4.160      0.000       0.018       0.051\n", "BIST100                      2.209e-05   3.83e-05      0.577      0.564    -5.3e-05    9.72e-05\n", "Fiyat-<PERSON><PERSON>              0.0125      0.012      1.054      0.292      -0.011       0.036\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1842      0.132      1.399      0.162      -0.074       0.442\n", "2 yıllık tahvil faizi           0.0050      0.003      1.576      0.115      -0.001       0.011\n", "enflasyon                      -0.0020      0.002     -1.291      0.197      -0.005       0.001\n", "TTM ROA                         0.0246      0.073      0.337      0.737      -0.119       0.168\n", "TTM ROE                        -0.0007      0.009     -0.076      0.939      -0.018       0.017\n", "rvol                           -0.0186      0.011     -1.655      0.098      -0.041       0.003\n", "==============================================================================\n", "Omnibus:                      663.137   <PERSON><PERSON><PERSON>-<PERSON>:                   1.979\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            16564.387\n", "Skew:                           0.946   Prob(JB):                         0.00\n", "Kurtosis:                      16.773   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "0\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.403e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:55   Log-Likelihood:                -895.57\n", "No. Observations:                2057   AIC:                             1817.\n", "Df Residuals:                    2044   BIC:                             1890.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0847      0.143     -0.594      0.553      -0.364       0.195\n", "price1                          0.9664      0.008    123.487      0.000       0.951       0.982\n", "Volume                      -8.207e-12   1.44e-10     -0.057      0.955    -2.9e-10    2.74e-10\n", "SP500                       -6.009e-05   2.93e-05     -2.052      0.040      -0.000   -2.66e-06\n", "USDTRY                          0.0355      0.008      4.242      0.000       0.019       0.052\n", "BIST100                      2.046e-05   3.84e-05      0.533      0.594   -5.48e-05    9.57e-05\n", "Fiyat-<PERSON><PERSON>              0.0144      0.012      1.217      0.224      -0.009       0.038\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1947      0.132      1.474      0.141      -0.064       0.454\n", "2 yıllık tahvil faizi           0.0048      0.003      1.533      0.125      -0.001       0.011\n", "enflasyon                      -0.0019      0.002     -1.182      0.237      -0.005       0.001\n", "TTM ROA                         0.0384      0.073      0.525      0.600      -0.105       0.182\n", "TTM ROE                        -0.0019      0.009     -0.220      0.826      -0.019       0.015\n", "rvol                           -0.0179      0.011     -1.587      0.113      -0.040       0.004\n", "==============================================================================\n", "Omnibus:                      665.114   <PERSON><PERSON><PERSON>-<PERSON>:                   1.987\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            16432.511\n", "Skew:                           0.954   Prob(JB):                         0.00\n", "Kurtosis:                      16.715   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "1\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.415e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:55   Log-Likelihood:                -900.64\n", "No. Observations:                2057   AIC:                             1827.\n", "Df Residuals:                    2044   BIC:                             1900.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.1112      0.143     -0.779      0.436      -0.391       0.169\n", "price1                          0.9657      0.008    123.126      0.000       0.950       0.981\n", "Volume                      -3.049e-12   1.44e-10     -0.021      0.983   -2.86e-10     2.8e-10\n", "SP500                       -5.895e-05   2.94e-05     -2.008      0.045      -0.000   -1.37e-06\n", "USDTRY                          0.0362      0.008      4.323      0.000       0.020       0.053\n", "BIST100                      1.504e-05   3.84e-05      0.391      0.696   -6.03e-05    9.04e-05\n", "Fiyat-<PERSON><PERSON>              0.0172      0.012      1.457      0.145      -0.006       0.040\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1883      0.132      1.422      0.155      -0.071       0.448\n", "2 yıllık tahvil faizi           0.0051      0.003      1.604      0.109      -0.001       0.011\n", "enflasyon                      -0.0019      0.002     -1.195      0.232      -0.005       0.001\n", "TTM ROA                         0.0478      0.073      0.651      0.515      -0.096       0.192\n", "TTM ROE                        -0.0022      0.009     -0.247      0.805      -0.019       0.015\n", "rvol                           -0.0179      0.011     -1.580      0.114      -0.040       0.004\n", "==============================================================================\n", "Omnibus:                      665.503   <PERSON><PERSON><PERSON>-Watson:                   1.976\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            16272.976\n", "Skew:                           0.958   Prob(JB):                         0.00\n", "Kurtosis:                      16.645   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "2\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.461e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:55   Log-Likelihood:                -901.19\n", "No. Observations:                2057   AIC:                             1828.\n", "Df Residuals:                    2044   BIC:                             1902.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.1049      0.143     -0.735      0.462      -0.385       0.175\n", "price1                          0.9658      0.008    123.105      0.000       0.950       0.981\n", "Volume                       -5.23e-12   1.44e-10     -0.036      0.971   -2.88e-10    2.78e-10\n", "SP500                       -5.914e-05   2.94e-05     -2.014      0.044      -0.000   -1.54e-06\n", "USDTRY                          0.0360      0.008      4.304      0.000       0.020       0.052\n", "BIST100                      1.675e-05   3.84e-05      0.436      0.663   -5.86e-05    9.21e-05\n", "Fiyat-<PERSON><PERSON>              0.0164      0.012      1.386      0.166      -0.007       0.040\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1926      0.133      1.452      0.147      -0.067       0.453\n", "2 yıllık tahvil faizi           0.0050      0.003      1.586      0.113      -0.001       0.011\n", "enflasyon                      -0.0019      0.002     -1.186      0.236      -0.005       0.001\n", "TTM ROA                         0.0449      0.073      0.612      0.541      -0.099       0.189\n", "TTM ROE                        -0.0021      0.009     -0.239      0.811      -0.019       0.015\n", "rvol                           -0.0179      0.011     -1.579      0.114      -0.040       0.004\n", "==============================================================================\n", "Omnibus:                      661.515   <PERSON><PERSON><PERSON>-Watson:                   1.984\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            16183.866\n", "Skew:                           0.948   Prob(JB):                         0.00\n", "Kurtosis:                      16.610   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "3\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.499e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:55   Log-Likelihood:                -902.54\n", "No. Observations:                2057   AIC:                             1831.\n", "Df Residuals:                    2044   BIC:                             1904.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.1159      0.143     -0.813      0.417      -0.396       0.164\n", "price1                          0.9655      0.008    122.995      0.000       0.950       0.981\n", "Volume                       -3.32e-12   1.44e-10     -0.023      0.982   -2.86e-10     2.8e-10\n", "SP500                       -5.835e-05   2.94e-05     -1.985      0.047      -0.000   -7.08e-07\n", "USDTRY                          0.0362      0.008      4.323      0.000       0.020       0.053\n", "BIST100                      1.493e-05   3.84e-05      0.389      0.698   -6.04e-05    9.03e-05\n", "Fiyat-<PERSON><PERSON>              0.0175      0.012      1.485      0.138      -0.006       0.041\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.1909      0.133      1.438      0.150      -0.069       0.451\n", "2 yıllık tahvil faizi           0.0050      0.003      1.587      0.113      -0.001       0.011\n", "enflasyon                      -0.0018      0.002     -1.171      0.242      -0.005       0.001\n", "TTM ROA                         0.0500      0.073      0.682      0.496      -0.094       0.194\n", "TTM ROE                        -0.0024      0.009     -0.270      0.787      -0.020       0.015\n", "rvol                           -0.0177      0.011     -1.564      0.118      -0.040       0.004\n", "==============================================================================\n", "Omnibus:                      664.874   <PERSON><PERSON><PERSON>-Watson:                   1.985\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            16160.084\n", "Skew:                           0.958   Prob(JB):                         0.00\n", "Kurtosis:                      16.597   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "4\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.422e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:55   Log-Likelihood:                -920.62\n", "No. Observations:                2057   AIC:                             1867.\n", "Df Residuals:                    2044   BIC:                             1940.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0780      0.144     -0.542      0.588      -0.360       0.204\n", "price1                          0.9663      0.008    122.036      0.000       0.951       0.982\n", "Volume                      -9.376e-12   1.46e-10     -0.064      0.949   -2.95e-10    2.76e-10\n", "SP500                       -6.156e-05   2.97e-05     -2.076      0.038      -0.000   -3.41e-06\n", "USDTRY                          0.0361      0.008      4.271      0.000       0.020       0.053\n", "BIST100                      2.068e-05   3.88e-05      0.534      0.594   -5.53e-05    9.67e-05\n", "Fiyat-<PERSON><PERSON>              0.0132      0.012      1.114      0.265      -0.010       0.037\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2045      0.134      1.526      0.127      -0.058       0.467\n", "2 yıllık tahvil faizi           0.0049      0.003      1.540      0.124      -0.001       0.011\n", "enflasyon                      -0.0019      0.002     -1.198      0.231      -0.005       0.001\n", "TTM ROA                         0.0332      0.074      0.449      0.654      -0.112       0.178\n", "TTM ROE                        -0.0016      0.009     -0.180      0.857      -0.019       0.016\n", "rvol                           -0.0184      0.011     -1.614      0.107      -0.041       0.004\n", "==============================================================================\n", "Omnibus:                      674.869   <PERSON><PERSON><PERSON>-Watson:                   1.980\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            16064.160\n", "Skew:                           0.987   Prob(JB):                         0.00\n", "Kurtosis:                      16.547   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "5\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.458e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:55   Log-Likelihood:                -923.60\n", "No. Observations:                2057   AIC:                             1873.\n", "Df Residuals:                    2044   BIC:                             1946.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0624      0.144     -0.434      0.664      -0.345       0.220\n", "price1                          0.9670      0.008    122.003      0.000       0.951       0.983\n", "Volume                      -1.248e-11   1.46e-10     -0.086      0.932   -2.98e-10    2.73e-10\n", "SP500                       -6.223e-05   2.97e-05     -2.096      0.036      -0.000   -3.99e-06\n", "USDTRY                          0.0357      0.008      4.221      0.000       0.019       0.052\n", "BIST100                      2.313e-05   3.88e-05      0.596      0.551   -5.29e-05    9.92e-05\n", "Fiyat-<PERSON><PERSON>              0.0114      0.012      0.963      0.336      -0.012       0.035\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2076      0.134      1.546      0.122      -0.056       0.471\n", "2 yıllık tahvil faizi           0.0048      0.003      1.510      0.131      -0.001       0.011\n", "enflasyon                      -0.0019      0.002     -1.204      0.229      -0.005       0.001\n", "TTM ROA                         0.0255      0.074      0.345      0.730      -0.119       0.171\n", "TTM ROE                        -0.0013      0.009     -0.141      0.888      -0.019       0.016\n", "rvol                           -0.0183      0.011     -1.600      0.110      -0.041       0.004\n", "==============================================================================\n", "Omnibus:                      665.286   <PERSON><PERSON><PERSON>-<PERSON>:                   1.980\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            15754.763\n", "Skew:                           0.967   Prob(JB):                         0.00\n", "Kurtosis:                      16.419   Cond. No.                     2.27e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.27e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "6\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.498e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -926.47\n", "No. Observations:                2057   AIC:                             1879.\n", "Df Residuals:                    2044   BIC:                             1952.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0436      0.144     -0.303      0.762      -0.326       0.239\n", "price1                          0.9680      0.008    122.047      0.000       0.952       0.984\n", "Volume                      -1.232e-11   1.46e-10     -0.084      0.933   -2.99e-10    2.74e-10\n", "SP500                       -6.312e-05   2.97e-05     -2.122      0.034      -0.000    -4.8e-06\n", "USDTRY                          0.0352      0.008      4.150      0.000       0.019       0.052\n", "BIST100                      2.543e-05   3.88e-05      0.655      0.513   -5.07e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0096      0.012      0.806      0.420      -0.014       0.033\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2056      0.134      1.529      0.127      -0.058       0.469\n", "2 yıllık tahvil faizi           0.0048      0.003      1.491      0.136      -0.002       0.011\n", "enflasyon                      -0.0020      0.002     -1.227      0.220      -0.005       0.001\n", "TTM ROA                         0.0169      0.074      0.228      0.819      -0.128       0.162\n", "TTM ROE                        -0.0008      0.009     -0.087      0.931      -0.018       0.017\n", "rvol                           -0.0182      0.011     -1.589      0.112      -0.041       0.004\n", "==============================================================================\n", "Omnibus:                      655.561   <PERSON><PERSON><PERSON>-Watson:                   1.976\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            15475.330\n", "Skew:                           0.945   Prob(JB):                         0.00\n", "Kurtosis:                      16.304   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "7\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.535e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -930.08\n", "No. Observations:                2057   AIC:                             1886.\n", "Df Residuals:                    2044   BIC:                             1959.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0274      0.144     -0.190      0.849      -0.310       0.255\n", "price1                          0.9692      0.008    122.158      0.000       0.954       0.985\n", "Volume                      -1.022e-11   1.46e-10     -0.070      0.944   -2.97e-10    2.77e-10\n", "SP500                        -6.35e-05   2.98e-05     -2.130      0.033      -0.000   -5.05e-06\n", "USDTRY                          0.0347      0.008      4.090      0.000       0.018       0.051\n", "BIST100                      2.641e-05   3.89e-05      0.679      0.497   -4.99e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0076      0.012      0.642      0.521      -0.016       0.031\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2064      0.135      1.531      0.126      -0.058       0.471\n", "2 yıllık tahvil faizi           0.0046      0.003      1.449      0.148      -0.002       0.011\n", "enflasyon                      -0.0020      0.002     -1.240      0.215      -0.005       0.001\n", "TTM ROA                         0.0083      0.074      0.112      0.911      -0.137       0.154\n", "TTM ROE                        -0.0003      0.009     -0.036      0.971      -0.018       0.017\n", "rvol                           -0.0180      0.011     -1.569      0.117      -0.041       0.005\n", "==============================================================================\n", "Omnibus:                      644.742   <PERSON><PERSON><PERSON>-Watson:                   1.970\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            15151.763\n", "Skew:                           0.922   Prob(JB):                         0.00\n", "Kurtosis:                      16.168   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "8\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.501e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -942.45\n", "No. Observations:                2057   AIC:                             1911.\n", "Df Residuals:                    2044   BIC:                             1984.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0560      0.145     -0.386      0.699      -0.340       0.228\n", "price1                          0.9662      0.008    121.373      0.000       0.951       0.982\n", "Volume                      -4.126e-12   1.47e-10     -0.028      0.978   -2.93e-10    2.84e-10\n", "SP500                       -6.358e-05      3e-05     -2.119      0.034      -0.000   -4.75e-06\n", "USDTRY                          0.0353      0.009      4.133      0.000       0.019       0.052\n", "BIST100                      2.861e-05   3.91e-05      0.731      0.465   -4.81e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0111      0.012      0.934      0.350      -0.012       0.034\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2099      0.136      1.546      0.122      -0.056       0.476\n", "2 yıllık tahvil faizi           0.0050      0.003      1.551      0.121      -0.001       0.011\n", "enflasyon                      -0.0019      0.002     -1.202      0.230      -0.005       0.001\n", "TTM ROA                         0.0244      0.074      0.327      0.743      -0.122       0.170\n", "TTM ROE                        -0.0013      0.009     -0.143      0.887      -0.019       0.016\n", "rvol                           -0.0185      0.012     -1.604      0.109      -0.041       0.004\n", "==============================================================================\n", "Omnibus:                      636.669   <PERSON><PERSON><PERSON>-Watson:                   1.972\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            15115.175\n", "Skew:                           0.900   Prob(JB):                         0.00\n", "Kurtosis:                      16.157   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "9\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.519e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -947.05\n", "No. Observations:                2057   AIC:                             1920.\n", "Df Residuals:                    2044   BIC:                             1993.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0675      0.145     -0.465      0.642      -0.353       0.218\n", "price1                          0.9649      0.008    121.102      0.000       0.949       0.981\n", "Volume                      -8.685e-13   1.47e-10     -0.006      0.995    -2.9e-10    2.88e-10\n", "SP500                       -6.393e-05   3.01e-05     -2.126      0.034      -0.000   -4.95e-06\n", "USDTRY                          0.0355      0.009      4.144      0.000       0.019       0.052\n", "BIST100                      2.889e-05   3.92e-05      0.737      0.461    -4.8e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0131      0.012      1.096      0.273      -0.010       0.036\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2061      0.136      1.514      0.130      -0.061       0.473\n", "2 yıllık tahvil faizi           0.0051      0.003      1.590      0.112      -0.001       0.011\n", "enflasyon                      -0.0019      0.002     -1.180      0.238      -0.005       0.001\n", "TTM ROA                         0.0337      0.075      0.452      0.651      -0.112       0.180\n", "TTM ROE                        -0.0018      0.009     -0.203      0.839      -0.019       0.016\n", "rvol                           -0.0190      0.012     -1.640      0.101      -0.042       0.004\n", "==============================================================================\n", "Omnibus:                      640.623   <PERSON><PERSON><PERSON>-Watson:                   1.963\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            15007.074\n", "Skew:                           0.913   Prob(JB):                         0.00\n", "Kurtosis:                      16.106   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "10\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.552e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -949.12\n", "No. Observations:                2057   AIC:                             1924.\n", "Df Residuals:                    2044   BIC:                             1997.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0748      0.146     -0.514      0.607      -0.360       0.211\n", "price1                          0.9644      0.008    120.968      0.000       0.949       0.980\n", "Volume                       4.432e-12   1.48e-10      0.030      0.976   -2.85e-10    2.94e-10\n", "SP500                       -6.401e-05   3.01e-05     -2.125      0.034      -0.000   -4.94e-06\n", "USDTRY                          0.0355      0.009      4.148      0.000       0.019       0.052\n", "BIST100                      2.814e-05   3.93e-05      0.717      0.474   -4.89e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0143      0.012      1.200      0.230      -0.009       0.038\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2023      0.136      1.483      0.138      -0.065       0.470\n", "2 yıllık tahvil faizi           0.0052      0.003      1.607      0.108      -0.001       0.012\n", "enflasyon                      -0.0019      0.002     -1.166      0.244      -0.005       0.001\n", "TTM ROA                         0.0399      0.075      0.535      0.593      -0.106       0.186\n", "TTM ROE                        -0.0022      0.009     -0.241      0.810      -0.020       0.015\n", "rvol                           -0.0193      0.012     -1.669      0.095      -0.042       0.003\n", "==============================================================================\n", "Omnibus:                      645.330   <PERSON><PERSON><PERSON>-Watson:                   1.959\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14969.246\n", "Skew:                           0.927   Prob(JB):                         0.00\n", "Kurtosis:                      16.085   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "11\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.597e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -949.74\n", "No. Observations:                2057   AIC:                             1925.\n", "Df Residuals:                    2044   BIC:                             1999.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0721      0.146     -0.495      0.620      -0.358       0.213\n", "price1                          0.9646      0.008    120.968      0.000       0.949       0.980\n", "Volume                       4.238e-13   1.48e-10      0.003      0.998   -2.89e-10     2.9e-10\n", "SP500                       -6.381e-05   3.01e-05     -2.117      0.034      -0.000    -4.7e-06\n", "USDTRY                          0.0355      0.009      4.148      0.000       0.019       0.052\n", "BIST100                      2.872e-05   3.93e-05      0.731      0.465   -4.83e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0136      0.012      1.141      0.254      -0.010       0.037\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2067      0.137      1.514      0.130      -0.061       0.474\n", "2 yıllık tahvil faizi           0.0052      0.003      1.593      0.111      -0.001       0.012\n", "enflasyon                      -0.0019      0.002     -1.166      0.244      -0.005       0.001\n", "TTM ROA                         0.0368      0.074      0.493      0.622      -0.109       0.183\n", "TTM ROE                        -0.0020      0.009     -0.225      0.822      -0.020       0.016\n", "rvol                           -0.0191      0.012     -1.647      0.100      -0.042       0.004\n", "==============================================================================\n", "Omnibus:                      640.457   <PERSON><PERSON><PERSON>-Watson:                   1.963\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14872.638\n", "Skew:                           0.915   Prob(JB):                         0.00\n", "Kurtosis:                      16.045   Cond. No.                     2.28e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.28e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "12\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.535e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -965.92\n", "No. Observations:                2057   AIC:                             1958.\n", "Df Residuals:                    2044   BIC:                             2031.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0497      0.147     -0.339      0.735      -0.338       0.238\n", "price1                          0.9663      0.008    120.301      0.000       0.951       0.982\n", "Volume                      -1.607e-11   1.49e-10     -0.108      0.914   -3.08e-10    2.76e-10\n", "SP500                       -6.504e-05   3.04e-05     -2.140      0.032      -0.000   -5.43e-06\n", "USDTRY                          0.0360      0.009      4.173      0.000       0.019       0.053\n", "BIST100                      2.781e-05   3.96e-05      0.703      0.482   -4.98e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0105      0.012      0.876      0.381      -0.013       0.034\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2114      0.138      1.535      0.125      -0.059       0.481\n", "2 yıllık tahvil faizi           0.0051      0.003      1.559      0.119      -0.001       0.011\n", "enflasyon                      -0.0020      0.002     -1.240      0.215      -0.005       0.001\n", "TTM ROA                         0.0192      0.075      0.256      0.798      -0.128       0.166\n", "TTM ROE                        -0.0008      0.009     -0.091      0.928      -0.019       0.017\n", "rvol                           -0.0186      0.012     -1.588      0.113      -0.041       0.004\n", "==============================================================================\n", "Omnibus:                      642.344   <PERSON><PERSON><PERSON>-Watson:                   1.944\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14657.182\n", "Skew:                           0.925   Prob(JB):                         0.00\n", "Kurtosis:                      15.946   Cond. No.                     2.29e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.29e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "13\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.589e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -966.38\n", "No. Observations:                2057   AIC:                             1959.\n", "Df Residuals:                    2044   BIC:                             2032.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0457      0.147     -0.311      0.756      -0.334       0.242\n", "price1                          0.9668      0.008    120.514      0.000       0.951       0.982\n", "Volume                      -1.812e-11   1.49e-10     -0.122      0.903    -3.1e-10    2.74e-10\n", "SP500                       -6.505e-05   3.04e-05     -2.139      0.033      -0.000    -5.4e-06\n", "USDTRY                          0.0360      0.009      4.169      0.000       0.019       0.053\n", "BIST100                      2.744e-05   3.96e-05      0.693      0.488   -5.02e-05       0.000\n", "Fiyat-<PERSON>ç <PERSON>              0.0100      0.012      0.833      0.405      -0.014       0.033\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2109      0.138      1.530      0.126      -0.059       0.481\n", "2 yıllık tahvil faizi           0.0051      0.003      1.549      0.122      -0.001       0.011\n", "enflasyon                      -0.0020      0.002     -1.252      0.211      -0.005       0.001\n", "TTM ROA                         0.0160      0.075      0.214      0.831      -0.131       0.163\n", "TTM ROE                        -0.0006      0.009     -0.067      0.947      -0.018       0.017\n", "rvol                           -0.0184      0.012     -1.571      0.116      -0.041       0.005\n", "==============================================================================\n", "Omnibus:                      637.878   <PERSON><PERSON><PERSON>-Watson:                   1.954\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14581.154\n", "Skew:                           0.914   Prob(JB):                         0.00\n", "Kurtosis:                      15.914   Cond. No.                     2.29e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.29e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "14\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.511e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -985.85\n", "No. Observations:                2057   AIC:                             1998.\n", "Df Residuals:                    2044   BIC:                             2071.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0239      0.148     -0.161      0.872      -0.315       0.267\n", "price1                          0.9697      0.008    119.930      0.000       0.954       0.986\n", "Volume                      -2.678e-11    1.5e-10     -0.178      0.859   -3.21e-10    2.68e-10\n", "SP500                       -6.424e-05   3.07e-05     -2.091      0.037      -0.000   -3.99e-06\n", "USDTRY                          0.0358      0.009      4.110      0.000       0.019       0.053\n", "BIST100                      2.456e-05      4e-05      0.615      0.539   -5.38e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0064      0.012      0.531      0.596      -0.017       0.030\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2124      0.139      1.525      0.127      -0.061       0.486\n", "2 yıllık tahvil faizi           0.0048      0.003      1.454      0.146      -0.002       0.011\n", "enflasyon                      -0.0021      0.002     -1.304      0.192      -0.005       0.001\n", "TTM ROA                        -0.0035      0.076     -0.046      0.963      -0.152       0.145\n", "TTM ROE                         0.0007      0.009      0.075      0.940      -0.017       0.019\n", "rvol                           -0.0172      0.012     -1.456      0.145      -0.040       0.006\n", "==============================================================================\n", "Omnibus:                      644.964   <PERSON><PERSON><PERSON>-Watson:                   1.934\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14609.602\n", "Skew:                           0.933   Prob(JB):                         0.00\n", "Kurtosis:                      15.922   Cond. No.                     2.29e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.29e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "15\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.562e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -988.07\n", "No. Observations:                2057   AIC:                             2002.\n", "Df Residuals:                    2044   BIC:                             2075.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0181      0.149     -0.122      0.903      -0.309       0.273\n", "price1                          0.9710      0.008    120.332      0.000       0.955       0.987\n", "Volume                      -1.744e-11    1.5e-10     -0.116      0.908   -3.12e-10    2.77e-10\n", "SP500                       -6.335e-05   3.08e-05     -2.059      0.040      -0.000      -3e-06\n", "USDTRY                          0.0355      0.009      4.066      0.000       0.018       0.053\n", "BIST100                      2.301e-05      4e-05      0.575      0.565   -5.54e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0051      0.012      0.420      0.675      -0.019       0.029\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2133      0.140      1.528      0.127      -0.060       0.487\n", "2 yıllık tahvil faizi           0.0047      0.003      1.414      0.157      -0.002       0.011\n", "enflasyon                      -0.0022      0.002     -1.317      0.188      -0.005       0.001\n", "TTM ROA                        -0.0097      0.076     -0.128      0.898      -0.158       0.139\n", "TTM ROE                         0.0011      0.009      0.116      0.907      -0.017       0.019\n", "rvol                           -0.0167      0.012     -1.411      0.158      -0.040       0.006\n", "==============================================================================\n", "Omnibus:                      633.431   <PERSON><PERSON><PERSON>-Watson:                   1.939\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14363.915\n", "Skew:                           0.906   Prob(JB):                         0.00\n", "Kurtosis:                      15.818   Cond. No.                     2.30e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.3e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "16\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.631e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -988.00\n", "No. Observations:                2057   AIC:                             2002.\n", "Df Residuals:                    2044   BIC:                             2075.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0205      0.149     -0.138      0.890      -0.312       0.271\n", "price1                          0.9714      0.008    120.929      0.000       0.956       0.987\n", "Volume                      -1.952e-11    1.5e-10     -0.130      0.897   -3.14e-10    2.75e-10\n", "SP500                       -6.217e-05   3.08e-05     -2.020      0.044      -0.000   -1.81e-06\n", "USDTRY                          0.0354      0.009      4.056      0.000       0.018       0.052\n", "BIST100                      2.213e-05      4e-05      0.554      0.580   -5.63e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0047      0.012      0.388      0.698      -0.019       0.028\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2167      0.140      1.551      0.121      -0.057       0.491\n", "2 yıllık tahvil faizi           0.0046      0.003      1.385      0.166      -0.002       0.011\n", "enflasyon                      -0.0021      0.002     -1.301      0.193      -0.005       0.001\n", "TTM ROA                        -0.0111      0.076     -0.147      0.883      -0.159       0.137\n", "TTM ROE                         0.0011      0.009      0.122      0.903      -0.017       0.019\n", "rvol                           -0.0163      0.012     -1.379      0.168      -0.039       0.007\n", "==============================================================================\n", "Omnibus:                      630.260   <PERSON><PERSON><PERSON>-Watson:                   1.941\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14331.481\n", "Skew:                           0.898   Prob(JB):                         0.00\n", "Kurtosis:                      15.806   Cond. No.                     2.30e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.3e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "17\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.629e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -996.57\n", "No. Observations:                2057   AIC:                             2019.\n", "Df Residuals:                    2044   BIC:                             2092.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0314      0.149     -0.210      0.834      -0.324       0.261\n", "price1                          0.9680      0.008    120.618      0.000       0.952       0.984\n", "Volume                      -2.094e-11   1.51e-10     -0.139      0.890   -3.17e-10    2.75e-10\n", "SP500                        -6.48e-05   3.09e-05     -2.097      0.036      -0.000   -4.19e-06\n", "USDTRY                          0.0359      0.009      4.102      0.000       0.019       0.053\n", "BIST100                      2.878e-05   4.01e-05      0.718      0.473   -4.99e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0071      0.012      0.584      0.559      -0.017       0.031\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2225      0.140      1.585      0.113      -0.053       0.498\n", "2 yıllık tahvil faizi           0.0050      0.003      1.502      0.133      -0.002       0.011\n", "enflasyon                      -0.0021      0.002     -1.281      0.200      -0.005       0.001\n", "TTM ROA                         0.0003      0.076      0.004      0.997      -0.149       0.149\n", "TTM ROE                         0.0003      0.009      0.037      0.970      -0.018       0.018\n", "rvol                           -0.0175      0.012     -1.475      0.140      -0.041       0.006\n", "==============================================================================\n", "Omnibus:                      632.783   <PERSON><PERSON><PERSON>-Watson:                   1.932\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14238.907\n", "Skew:                           0.907   Prob(JB):                         0.00\n", "Kurtosis:                      15.761   Cond. No.                     2.30e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.3e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "18\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.689e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -996.69\n", "No. Observations:                2057   AIC:                             2019.\n", "Df Residuals:                    2044   BIC:                             2093.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0303      0.149     -0.203      0.839      -0.323       0.263\n", "price1                          0.9684      0.008    121.109      0.000       0.953       0.984\n", "Volume                      -2.126e-11   1.51e-10     -0.141      0.888   -3.17e-10    2.75e-10\n", "SP500                       -6.458e-05   3.09e-05     -2.089      0.037      -0.000   -3.96e-06\n", "USDTRY                          0.0359      0.009      4.101      0.000       0.019       0.053\n", "BIST100                      2.796e-05   4.01e-05      0.698      0.485   -5.06e-05       0.000\n", "Fiyat-<PERSON><PERSON>              0.0068      0.012      0.564      0.573      -0.017       0.031\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2218      0.140      1.579      0.114      -0.054       0.497\n", "2 yıllık tahvil faizi           0.0049      0.003      1.493      0.136      -0.002       0.011\n", "enflasyon                      -0.0021      0.002     -1.286      0.199      -0.005       0.001\n", "TTM ROA                        -0.0011      0.076     -0.015      0.988      -0.150       0.148\n", "TTM ROE                         0.0005      0.009      0.049      0.961      -0.018       0.018\n", "rvol                           -0.0174      0.012     -1.465      0.143      -0.041       0.006\n", "==============================================================================\n", "Omnibus:                      630.243   <PERSON><PERSON><PERSON>-<PERSON>:                   1.942\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14206.140\n", "Skew:                           0.901   Prob(JB):                         0.00\n", "Kurtosis:                      15.748   Cond. No.                     2.30e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.3e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "19\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                  price   R-squared:                       0.998\n", "Model:                            OLS   Adj. R-squared:                  0.998\n", "Method:                 Least Squares   F-statistic:                 7.620e+04\n", "Date:                Mon, 01 Apr 2024   Prob (F-statistic):               0.00\n", "Time:                        20:44:56   Log-Likelihood:                -1015.4\n", "No. Observations:                2057   AIC:                             2057.\n", "Df Residuals:                    2044   BIC:                             2130.\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================================\n", "                                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-----------------------------------------------------------------------------------------------\n", "const                          -0.0218      0.151     -0.144      0.885      -0.317       0.274\n", "price1                          0.9727      0.008    121.026      0.000       0.957       0.989\n", "Volume                       -4.24e-11   1.52e-10     -0.278      0.781   -3.41e-10    2.56e-10\n", "SP500                       -6.066e-05   3.12e-05     -1.944      0.052      -0.000    5.21e-07\n", "USDTRY                          0.0360      0.009      4.075      0.000       0.019       0.053\n", "BIST100                      1.703e-05   4.04e-05      0.421      0.674   -6.22e-05    9.63e-05\n", "Fiyat-<PERSON><PERSON>              0.0039      0.012      0.318      0.751      -0.020       0.028\n", "<PERSON><PERSON><PERSON>/<PERSON><PERSON>     0.2178      0.142      1.536      0.125      -0.060       0.496\n", "2 yıllık tahvil faizi           0.0044      0.003      1.319      0.187      -0.002       0.011\n", "enflasyon                      -0.0022      0.002     -1.305      0.192      -0.005       0.001\n", "TTM ROA                        -0.0178      0.076     -0.233      0.816      -0.168       0.132\n", "TTM ROE                         0.0017      0.009      0.181      0.856      -0.017       0.020\n", "rvol                           -0.0154      0.012     -1.289      0.197      -0.039       0.008\n", "==============================================================================\n", "Omnibus:                      633.459   <PERSON><PERSON><PERSON>-Watson:                   1.927\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            14188.226\n", "Skew:                           0.910   Prob(JB):                         0.00\n", "Kurtosis:                      15.737   Cond. No.                     2.30e+09\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 2.3e+09. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "20\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "from pathlib import Path\n", "\n", "def var_lags(data,nlags):\n", "        alldata=data\n", "        str1='price'\n", "        alldata=alldata.drop([\"Open\",\"High\",\"Low\"],axis=1)\n", "        df=alldata[['Date','Close']]\n", "        df['price']=df[['Close']]\n", "        df2=df.drop([\"Close\"],axis=1)\n", "        for i in range(nlags):\n", "            df2[str1+str(i+1)]=df2['price'].shift(i+1)\n", "        return df2\n", "\n", "def dgpprice(alldata,lags):\n", "\n", "\n", "    \n", "    \n", "    df=alldata.set_index(\"Date\")\n", "    \n", "    data=df[[\"Close\",\"BIST100\",\"USDTRY\"]]\n", "    data_r=np.log(data).diff()\n", "    data_r=data_r.dropna()\n", "    data_r=data_r*100\n", "   \n", "    \n", "   # \"\"\"realized volatility hesaplaması\"\"\"\n", "    data2=data_r/100 ## eğer returnler 100 ile çarpılı halde ise bölmek gerekiyor\n", "    RT=data2[\"Close\"]**2 \n", "    RTm=RT.rolling(21).mean()\n", "#    Realvol=100*np.sqrt(RTm*252) #annualized_volatility\n", "    Realvol_daily=100*np.sqrt(RTm) #daily_volatility\n", "    rvol=pd.DataFrame(Realvol_daily)\n", "    rvol.columns=[\"rvol\"]\n", "   \n", "    \n", "    RT_dolar=data2[\"USDTRY\"]**2 \n", "    RTm_dolar=RT_dolar.rolling(21).mean()\n", "#    Realvol_dolar=100*np.sqrt(RTm_dolar*252) #annualized_volatility\n", "    Realvol_dolar_daily=100*np.sqrt(RTm_dolar) #daily_volatility\n", "    rvol_dolar=pd.DataFrame(Realvol_dolar_daily)\n", "    rvol_dolar.columns=[\"rvol_dolar\"]\n", "    \n", "    \n", "    RT_bist=data2[\"BIST100\"]**2 \n", "    RTm_bist=RT_bist.rolling(21).mean()\n", "#    Realvol_bist_an=100*np.sqrt(RTm_bist*252) annualized_volatility\n", "    Realvol_bist_daily=100*np.sqrt(RTm_bist) #daily_volatility\n", "    rvol_bist=pd.DataFrame(Realvol_bist_daily)\n", "    rvol_bist.columns=[\"rvol_bist\"]\n", "    \n", "    \n", "   # \"\"\" beta hesaplaması\"\"\"\n", "    betadf=data_r[[\"Close\", \"BIST100\"]]\n", "    bist100_return=betadf[[\"BIST100\"]]\n", "    varbist=pd.DataFrame(bist100_return.rolling(21).var())\n", "    covariance=pd.DataFrame(betadf.rolling(21).cov().unstack()['Close']['BIST100'])\n", "    covariance.columns=[\"kovaryans\"]\n", "    beta=pd.DataFrame(covariance.kovaryans/varbist.BIST100)\n", "    beta.columns=[\"beta\"]\n", "     \n", "    \n", "\n", "#    indikatörler2=indikatörler.shift(1)\n", "    indicators=pd.concat([beta,rvol,rvol_dolar,rvol_bist],axis=1)\n", "\n", "    df=df.iloc[:,4:len(df)]\n", "    df2=pd.concat([df,indicators],axis=1)\n", "    df3=df2.shift(1)\n", "    Price=alldata.iloc[:,1]\n", "    Price=pd.DataFrame(Price)\n", "    dfyeni=var_lags(alldata,1)\n", "    dfyeni=dfyeni.set_index(\"Date\")\n", "    alldatason=pd.concat([d<PERSON><PERSON>,df3],axis=1)\n", "    alldatason2=alldatason.dropna()\n", "    alldatason3=alldatason2.reset_index()\n", "    return alldatason3\n", "data_folder = Path(\"c:/FinansveRiskuygulamalar/\")\n", "PORTFOY = pd.read_excel(data_folder/'PORTFOY2024.xlsx', sheet_name='Sheet1')\n", "\n", "alldata=pd.read_excel(data_folder/'akbank2.xlsx', sheet_name='DATA')\n", "# data with one time lag \n", "alldata2=dgpprice(alldata,1)\n", "writer = pd.ExcelWriter(data_folder/'akbnk_adjusted.xlsx', engine='xlsxwriter')\n", "alldata2.to_excel(writer, sheet_name='akbank',index = False)\n", "writer.save()\n", "alldata2=pd.read_excel(data_folder/'akbnk_adjusted.xlsx', sheet_name='akbank')\n", "column_names = list(alldata2.columns)\n", "print(column_names)\n", "len_data=len(column_names)\n", "X=alldata2[['price1','Volume','SP500','USDTRY','BIST100','Fiyat-Kazanç Oranı', '<PERSON>yasa <PERSON>/<PERSON><PERSON>', '2 yıllık tahvil faizi', 'enflasyon', 'TTM ROA', 'TTM ROE','rvol']]\n", "corr1=X.corr()\n", "X=sm.add_constant(X)\n", "\n", "Y=alldata2.iloc[:,1:2]  \n", "Y=pd.DataFrame(Y)\n", "\n", "test_size=21\n", "train_size=len(Y)-test_size\n", "test_sample=Y[train_size:len(Y)]\n", "test_sample=test_sample.reset_index()\n", "lf=len(Y)-train_size\n", "matpredall=np.zeros((lf,1))\n", "matrix = np.zeros((1,1)) # Pre-allocate matrix\n", "\n", "testx=X[train_size:len(X)]\n", "testy=Y[train_size:len(X)]\n", "testx=testx.reset_index()\n", "del testx['index']\n", "testy=testy.reset_index()\n", "del testy['index']\n", "\n", "for j in range(lf):\n", "    X_train=X[0+j:train_size+j]\n", "    y_train=Y[0+j:train_size+j]\n", "    X_test=testx[0+j:1+j]\n", "    y_test=testy[0+j:1+j]\n", "    m=0\n", "    results = sm.OLS(endog=y_train, exog=X_train).fit()\n", "    print(results.summary())\n", "    y_pred_OLS = results.predict(X_test)\n", "    matrix[:,m] = y_pred_OLS\n", "    m=m+1\n", "    print(j)   \n", "    mat<PERSON><PERSON>all[j,0]=matrix\n", "           \n", "matytraintest=Y[train_size:len(Y)]  \n", "matytraintest=np.array(matytraintest)\n", "lenmatytraintest=len( matytraintest)\n", "dfmatytraintest=pd.DataFrame(matytraintest)\n", "dfmatpredict=pd.Data<PERSON>rame(matpredall)\n", " \n", "fark=dfmatytraintest.values- dfmatpredict.values\n", "Mat_error=abs(fark) \n", "Mat_MAE=Mat_error.mean(0)\n", "Mat_MAE=Mat_MAE.tolist()\n", "  \n", "\n", "Mat_errorrate=(Mat_error/dfmatytraintest.values)*100\n", "Mat_MAPE=Mat_errorrate.mean(0)\n", "Mat_MAPE=Mat_MAPE.tolist()\n", "pseudo_predicted_price=pd.DataFrame(matpredall)\n", "pseudo_predicted_price.columns=['Predicted_Prices']\n", "\n", "#real out of sample prediction\n", "#verinin son satır <PERSON><PERSON>\n", "testx2=X[len(X)-1:len(X)]\n", "real_pred = results.predict(testx2)\n", "real_pred=real_pred.reset_index()\n", "del real_pred['index']\n", "real_pred.columns=['Real_Predicted_Prices']"]}, {"cell_type": "code", "execution_count": 2, "id": "e432961e", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2.647866821789258]\n"]}], "source": ["print(Mat_MAPE)"]}, {"cell_type": "code", "execution_count": 3, "id": "431fb882", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Real_Predicted_Prices\n", "0               46.70809\n"]}], "source": ["print(real_pred)"]}, {"cell_type": "code", "execution_count": null, "id": "c122b409", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}