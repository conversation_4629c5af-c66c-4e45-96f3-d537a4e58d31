library(zoo)
library(readxl)
library(timeSeries)
library(tseries)
library(forecast)


data(AirPassengers)
AP <- AirPassengers
plot(AP, ylab = "Passengers (1000's)")


data <- read_excel("Airlinedata.xlsx", sheet = "Data",col_names = TRUE)
passenger<-data$Passengers
class(passenger)
plot(passenger, ylab = "Passengers")


ts_passengers<-ts(passenger, frequency = 4, start = c(2000, 1)) # First Quarter of 2000
class(ts_passengers)
plot(ts_passengers, ylab = "Passengers")


layout(1:3)
plot(aggregate(AP))
boxplot(AP ~ cycle(AP))

layout(1:2)
plot(aggregate(ts_passengers))
boxplot(ts_passengers ~ cycle(ts_passengers))




df <- read_excel("stockdata2.xlsx")
date<- df$date
df2<-df[-c(1)]#removing the first column

Stocks<-zoo(df2, seq(from = as.Date("2016-01-04"), to = as.Date("2022-04-14"), by = 1))
plot(Stocks, xlab="Time")


data <- read.csv("temperature.csv")
df3<-data[,2]
#print(df3)
#plot(df3,ylab="Global Temperature")
#layout(1:3)
#plot(aggregate(temp))
#boxplot(temp ~ cycle(temp))
temp<-ts(df3, frequency = 12, start = c(1850, 1))
#temp<-ts(df3, frequency = 12, start = c(1850, 1),end=c(2020,1))
New.series <- window(temp, start=c(1970, 1), end=c(2022, 02))
New.time <- time(New.series)
print(New.time)
print(New.series) #extracts the time from a time series object
plot(New.series); abline(reg=lm(New.series ~ New.time))

#Decomposition of additive the series 
Temp_dec<-decompose(temp, type ="add")
plot(Temp_dec)
temp_seasonal_component<-Temp_dec$seasonal
temp_trend_component<-Temp_dec$trend
temp_deaseasonalized_series=temp-temp_seasonal_component
tem_detrended_series<-temp-temp_trend_component


#Decomposition of multiplicative the series 
sanayi <- read_excel("sanayi.xlsx", sheet = "Sayfa1",col_names = TRUE)
df4<-sanayi[,2]
ip<-ts(df4, frequency = 12, start = c(2009, 8))
plot(ip,ylab="Sanayi \retimi")
layout(1:3)
plot(aggregate(ip))
boxplot(ip ~ cycle(ip))

ip_dec<-decompose(ip, type ="mult")
plot(ip_dec)
ip_seasonal_component<-ip_dec$seasonal
ip_trend_component<-ip_dec$trend
ip_deaseasonalized_series=ip/ip_seasonal_component
ip_detrended_series<-ip/ip_trend_component
print(ip_detrended_series)

#forecasting future temperature
#constructing the trend
trend<-seq(1,length(temp), by=1)
#construct 12 out of sample observation for the trend
trend_out<-seq(length(temp)+1, length(temp)+12, by=1)
class(trend_out)
trend_out<-as.data.frame(trend_out)
colnames(trend_out) <- "trend"
class(trend_out)
model<-lm(temp ~ trend)
summary(model)
forecasted_temp1<-predict(model, newdata = trend_out)

#starting the trend from 1970.1
trend<-seq(1,length(New.series), by=1)
#construct 12 out of sample observation for the trend
trend_out<-seq(length(New.series)+1, length(New.series)+12, by=1)
class(trend_out)
trend_out<-as.data.frame(trend_out)
colnames(trend_out) <- "trend"
class(trend_out)
model2<-lm(New.series ~ trend)
summary(model2)
forecasted_temp2<-predict(model2, newdata = trend_out)


#Seasonal Adjustment and detrending using Dummies  for multiplicative series
dummies=seasonaldummy(ip)
tarih<-sanayi$Tarih
dummy_df<-cbind(tarih,dummies)
dummy.ts<-ts(dummy_df, frequency = 12, start = c(2009, 8))

ln_ip<-log(ip)
plot(ln_ip, ylab="ln_ip")
plot(ip,ylab="ip")
plot(cbind(ip,ln_ip))
trend<-seq(1,length(ln_ip),by=1)
trend<-as.data.frame(trend)
dummy_df<-dummy_df[,2:12]
dummy_df<-as.data.frame(dummy_df)
ln_ip<-as.data.frame(ln_ip)
colnames(ln_ip) <- "ln_ip"
data_ip<-cbind(ln_ip,trend, dummy_df)
model3<-lm(ln_ip~trend+Jan+Feb+Mar+Apr+Jun+Jul+Aug+Sep+Oct+Nov, data=data_ip )
summary(model3)
#forecast for the period 2022-3 to 2022-12
#constructing out of sample data
#out of sample dummies
#from october to december for any year
dummies_o_s<-window(dummy.ts, start=c(2010, 3), end=c(2010, 12))
dummies_o_s<-dummies_o_s[,2:12]#removing the first column 
dummies_o_s<-as.data.frame(dummies_o_s)
trend_out<-seq(length(ip)+1, length(ip)+10, by=1)
trend_out<-as.data.frame(trend_out)
colnames(trend_out) <- "trend"
dum_trend<-cbind(trend_out,dummies_o_s)
forecasted_ln_ip<-predict(model3, newdata = dum_trend)
#inverse transformation 
#calculate correction factor asssuming errors are normally distributed (page 20 2009 book introductory t series..with R)
sigma<-summary(model3)$sigma
log_correction_factor<-exp(0.5*sigma^2)
forecasted_ip<-exp(forecasted_ln_ip)*log_correction_factor
#calculate correction factor asssuming errors arent normally distributed (page 117 2009 book introductory t series..with R)
empirical_correction_factor<-mean(exp(resid(model3)))
forecasted_ip2<-exp(forecasted_ln_ip)*empirical_correction_factor
