{"cells": [{"cell_type": "code", "execution_count": null, "id": "3e1fbef0", "metadata": {}, "outputs": [], "source": ["import warnings\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import statsmodels.tsa.stattools as ts \n", "\n", "from pathlib import Path\n", "\n", "from statsmodels.tsa.api import VAR\n", "from statsmodels.tsa.api import VECM\n", "from statsmodels.tsa.stattools import coint\n", "from statsmodels.tsa.vector_ar.vecm import CointRankResults\n", "from statsmodels.tsa.vector_ar.vecm import coint_johansen\n", "\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "data_folder = Path(\"D:/TimeSeriesandForecasting/DataandCodes\")\n", "\n", "data = md = pd.read_excel(data_folder /'ihracat.xlsx', sheet_name='Sayfa1')\n", "df= data.set_index('Date')\n", "\n", "\n", "# Engle-granger cointegration test-single approach\n", "x=df[['GDP',]]\n", "y=df[['HouseholdConsumption', 'GoodServiceExport', 'GoodServiceImport']]\n", "EG_coint=ts.coint(x, y)\n", "print(EG_coint)\n", "\n", "\n", "#  creation of VECM dataset\n", "vecmdata = df[['HouseholdConsumption', 'GoodServiceExport', 'GoodServiceImport']]\n", "train_vecm=vecmdata.iloc[0:92]\n", "test_vecm=vecmdata.iloc[-5:]\n", "vecmvalues=train_vecm.values   # converting datafrane into array to proceed with <PERSON><PERSON> test \n", "\n", "##### <PERSON><PERSON> test for cointegration ######\n", "##  coint_johansen(endog, det_order, k_ar_diff) ##\n", "<PERSON>_<PERSON>sen=coint_johansen(vecmvalues,0,2)\n", "#-1 - no deterministic terms; 0 - constant term; 1 - linear trend\n", "#the later is the Number of lagged differences in the model.\n", "trace_test=pd.DataFrame(Test_Johansen.lr1)\n", "trace_test.columns=[\"trace test stat\"]\n", "cvt=pd.DataFrame(Test_Johansen.cvt)\n", "cvt.columns=[\"0.1\",\"0.05\",\"0.01\"]\n", "Trace_test=pd.concat([trace_test,cvt],axis=1)\n", "\n", "meigen_test=pd.DataFrame(Test_Johansen.lr2)\n", "meigen_test.columns=[\"meigen test stat\"]\n", "cvm=pd.DataFrame(Test_Johansen.cvm)\n", "cvm.columns=[\"0.1\",\"0.05\",\"0.01\"]\n", "Meigen_test=pd.concat([meigen_test,cvm],axis=1)\n", "\n", "#signif.levels: {0.1, 0.05, 0.01}\n", "\n", "\n", "### RULE: if both trace test statistics and max.eigen test statistics are greater than critical values, reject H0.\n", "#   for any critical values that you choose.\n", "#   r=1; there is one linear combination; there is one cointegration relationship!\"\n", "#   H0= no cointegration; HA: there is cointegration\n", "\n", "\n", " ########################################\n", "####  Vector Error Correction Model  #####\n", " ########################################\n", "model = VECM(train_vecm, k_ar_diff=2, coint_rank=1, deterministic='ci') \n", "vecm_res = model.fit()\n", "vecm_res.summary()\n", "\n", "# Loading coefficients (alpha) for equation Y: gives cointegration coefficient\n", "# as we set cointegration rank to 1, cointegration matrix contains just 1 column:\"1 linear combination\"\n", "# beta's are the coefficient of this linear combination equation\n", "\n", "vecm_res.plot_data(with_presample=True)\n", "vecmalphabeta=vecm_res.gamma.round(4) #coefficients\n", "\n", "## Forecast\n", "predicted_values=pd.DataFrame(vecm_res.predict(steps=5))\n", "predicted_values.columns=['HouseholdConsumption', 'GoodServiceExport', 'GoodServiceImport']\n", "\n", "\n", "forecast, lower, upper = vecm_res.predict(5, 0.05)\n", "print(\"lower bounds of confidence intervals:\")\n", "print(lower.round(3))\n", "print(\"\\npoint forecasts:\")\n", "print(forecast.round(1))\n", "print(\"\\nupper bounds of confidence intervals:\")\n", "print(upper.round(3))\n", "\n", "vecm_res.plot_forecast(steps=10) #out of sample forecast\n", "\n", "#impulse-response functions\n", "\n", "irf = vecm_res.irf(50)\n", "irf.plot(orth=True)\n", "\n", "irf.plot(impulse='HouseholdConsumption') #just for the HouseholdConsumption\n", "irf.plot(impulse='GoodServiceExport') \n", "irf.plot(impulse='GoodServiceImport') \n", "\n", "irf = vecm_res.irf(80)\n", "irf.plot(impulse='HouseholdConsumption') \n", "\n", "\n", "\n", "## Calculation of the metrics\n", "def forecast_accuracy(forecast, actual):\n", "    mape = np.mean(np.abs(forecast - actual)/np.abs(actual))  # MAPE\n", "    mae = np.mean(np.abs(forecast - actual))    # MAE\n", "    rmse = np.mean((forecast - actual)**2)**.5  # RMSE\n", "    return({'mape':mape, 'mae': mae, \n", "             'rmse':rmse})\n", "test_values=test_vecm.reset_index(drop=True)  ## in order to perform calculation, index of two DFs must match\n", "metrics_for_Consumption=forecast_accuracy(predicted_values.HouseholdConsumption, test_values.HouseholdConsumption)\n", "metrics_for_Export=forecast_accuracy(predicted_values.GoodServiceExport, test_values.GoodServiceExport)\n", "metrics_for_Import=forecast_accuracy(predicted_values.GoodServiceImport, test_values.GoodServiceImport)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}