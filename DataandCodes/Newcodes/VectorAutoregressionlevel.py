# -*- coding: utf-8 -*-
"""
Created on Sat Feb 23 00:50:28 2019

@author: Bulent GULOGLU
"""

import warnings
import pandas as pd
import numpy as np
import statsmodels.api as sm
from pathlib import Path

from statsmodels.tsa.api import VAR
from statsmodels.tsa.api import VECM

data_folder = Path("D:/TimeSeriesandForecasting/DataandCodes")

md = pd.read_excel(data_folder /'Money.xlsx', sheet_name='Sayfa1')
df=md
ip=df["ip"]
Real_Money=df["Real_Money"]
interest_rate=df["interest_rate"]

#creation of VAR dataset
tempdata= df[['Real_Money','interest_rate']]
tempdata2=tempdata.diff().dropna()
vardata=pd.concat([ip,tempdata2],axis=1)
vardata=vardata.dropna()


train_var=vardata.iloc[0:120]
test_var=vardata.iloc[-12:]
model_var = VAR(train_var)
result_var=model_var.fit( maxlags=15, ic='aic', trend='c')
stable1=result_var.is_stable(verbose=False) 
print(stable1) 
#if stable true model is stable 

#reconstruction of VAR
tempdata= df[['ip','Real_Money','interest_rate']]
vardata=tempdata.diff().dropna()    
train_var=vardata.iloc[0:120]
test_var=vardata.iloc[-12:]
model_var = VAR(train_var)
result_var=model_var.fit( maxlags=15, ic='aic', trend='c')
stable1=result_var.is_stable(verbose=False) 
print(stable1) 
 
result_var.summary()
result_var.plot()
result_var.plot_acorr()
nobs=result_var.nobs
lag_order = result_var.k_ar
print(lag_order)
nobs_train_var=len(train_var)
pseudo_for=result_var.forecast(train_var.values[-lag_order:],steps=12)




lag_order = result_var.k_ar
print(lag_order)
# Input data for forecasting
input_data = vardata.values[-lag_order:]
print(input_data)
# forecasting
pred = result_var.forecast(y=input_data, steps=12)
pred = (pd.DataFrame(pred, index=test_var.index, columns= test_var.columns + '_pred'))
print(pred)

def invert_transformation(df_train, pred):
  forecast = pred.copy()
  columns = df_train.columns
  for col in columns:
      forecast[str(col)+'_pred'] = df_train[col].iloc[-1] + forecast[str(col)+'_pred'].cumsum()                   
  return forecast


output = invert_transformation(train_var, pred)
output.loc[:, ['Real_Money_pred']]
#getting levels
pseudo_for=pd.DataFrame(pseudo_for)
pseudo_for.rename(columns={0:"ip",1:"realmoney", 2:"interest_rate"}, inplace=True)

predictions_var_diff =result_var.fittedvalues






#taking base year as 1961.4 and doing cumulative sum
data = df[['RGDP','M2','TB3mo']]
basedate=data.iloc[11:12]
merge=pd.concat([basedate,predictions_var_diff],axis=0, ignore_index=True)
levelforecast=merge.cumsum()


#taking base year as 1999.4 and doing cumulative sum for forecast values
basedate2=levelforecast.iloc[-1:]
merge2=pd.concat([basedate2,pseudo_for],axis=0, ignore_index=True)
test_forecastlevels=merge2.cumsum()
test_forecastlevels=test_forecastlevels.drop([0],axis=0)

#reindexing work; because when indices are different, 
#substracting two dataframe yiels "Nan"
data2=md[['RGDP','M2','TB3mo']]
level_test=data2.iloc[-5:]
mergeforindexing=pd.concat([level_test,basedate2],axis=0, ignore_index=True)
level_test=mergeforindexing.iloc[0:5]


#comparing level_test and test_forecastlevels!

mae1=abs(level_test-test_forecastlevels).mean()
mape1=100*(abs(level_test-test_forecastlevels)/level_test).mean()



#  impulse-response functions

irf = result_var.irf(12)
irf.plot(orth=True)

irf.plot(impulse='RGDP') #just for the RGDP
irf.plot(impulse='M2') 
irf.plot(impulse='TB3mo') 
irf.plot_cum_effects(orth=True)


fevd = result_var.fevd(12)
fevd.summary()

result_var.fevd(12).plot()
print(result_var.test_causality('RGDP', ['M2', 'TB3mo'], kind='f'))


from statsmodels.tsa.stattools import grangercausalitytests
re=grangercausalitytests(np.column_stack((train_var['RGDP'],train_var['M2'])),maxlag=10)

