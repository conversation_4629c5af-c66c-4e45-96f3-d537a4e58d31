{"cells": [{"cell_type": "code", "execution_count": null, "id": "b44e154f", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import warnings\n", "import numpy as np\n", "from math import sqrt\n", "from numpy import concatenate\n", "from matplotlib import pyplot\n", "from pandas import read_csv\n", "from pandas import DataFrame\n", "from pandas import concat\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.metrics import mean_squared_error\n", "from keras.models import Sequential\n", "from keras.layers import Dense\n", "from keras.layers import LSTM\n", "from pandas import read_csv\n", "from datetime import datetime\n", "from pathlib import Path\n", "from pandas import read_csv\n", "from matplotlib import pyplot\n", "\n", "# load data\n", "\n", "# save to file\n", "\n", "data_folder = Path(\"D:/TimeSeriesandForecasting/DataandCodes\")\n", "data = pd.read_excel(data_folder /'ihracat.xlsx', sheet_name='Sayfa1')\n", "data=data[['GDP','HouseholdConsumption','GoodServiceExport','GoodServiceImport']]\n", "\n", "df2=data\n", "dataval=df2.values\n", "groups = [0, 1, 2,3]\n", "i = 1\n", "# plot each column\n", "pyplot.figure()\n", "\n", "for group in groups:\n", "\tpyplot.subplot(len(groups), 1, i)\n", "\tpyplot.plot(dataval[:, group])\n", "\tpyplot.title(data.columns[group], y=0.5, loc='right')\n", "\ti += 1\n", "pyplot.show()\n", "\n", "\n", "\n", "\n", "vardata=df2.diff().dropna()\n", "\n", "# load dataset\n", "values = vardata.values\n", "\n", "\n", "\n", "\n", "# convert series to supervised learning\n", "def series_to_supervised(vardata, n_in=1, n_out=1, dropnan=True):\n", "\tn_vars = 1 if type(vardata) is list else vardata.shape[1]\n", "\tdf = DataFrame(vardata)\n", "\tcols, names = list(), list()\n", "\t# input sequence (t-n, ... t-1)\n", "\tfor i in range(n_in, 0, -1):\n", "\t\tcols.append(df.shift(i))\n", "\t\tnames += [('var%d(t-%d)' % (j+1, i)) for j in range(n_vars)]\n", "\t# forecast sequence (t, t+1, ... t+n)\n", "\tfor i in range(0, n_out):\n", "\t\tcols.append(df.shift(-i))\n", "\t\tif i == 0:\n", "\t\t\tnames += [('var%d(t)' % (j+1)) for j in range(n_vars)]\n", "\t\telse:\n", "\t\t\tnames += [('var%d(t+%d)' % (j+1, i)) for j in range(n_vars)]\n", "\t# put it all together\n", "\tagg = concat(cols, axis=1)\n", "\tagg.columns = names\n", "\t# drop rows with NaN values\n", "\tif dropnan:\n", "\t\tagg.dropna(inplace=True)\n", "\treturn agg\n", " \n", "\n", "\n", "# integer encode direction\n", "#encoder = LabelEncoder()\n", "#values[:,4] = encoder.fit_transform(values[:,4])\n", "# ensure all data is float\n", "#values = values.astype('float32')\n", "# normalize features\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled = scaler.fit_transform(values)\n", "# frame as supervised learning\n", "reframed = series_to_supervised(scaled, 1, 1)\n", "\n", "print(reframed.head())\n", "\n", "# split into train and test sets\n", "values = reframed.values\n", "# drop columns we don't want to predict\n", "reframed.drop(reframed.columns[[5,6,7]], axis=1, inplace=True)\n", "values = reframed.values\n", "n_obs=len(values)\n", "h=5 #forecast horizon\n", "train_sample=(n_obs-h)\n", "train=values[0:train_sample,:]\n", "test = values[train_sample:, :]\n", "\n", "\n", "# split into input and outputs\n", "train_X, train_y = train[:, :-1], train[:, -1]\n", "test_X, test_y = test[:, :-1], test[:, -1]\n", "# reshape input to be 3D [samples, timesteps, features]\n", "train_X = train_X.reshape((train_X.shape[0], 1, train_X.shape[1]))\n", "test_X = test_X.reshape((test_X.shape[0], 1, test_X.shape[1]))\n", "print(train_X.shape, train_y.shape, test_X.shape, test_y.shape)\n", " \n", "# design network\n", "model = Sequential()\n", "model.add(LSTM(50, input_shape=(train_X.shape[1], train_X.shape[2])))\n", "model.add(<PERSON><PERSON>(1))\n", "model.compile(loss='mae', optimizer='adam')\n", "# fit network\n", "history = model.fit(train_X, train_y, epochs=50, batch_size=100, validation_data=(test_X, test_y), verbose=2, shuffle=False)\n", "# plot history\n", "pyplot.plot(history.history['loss'], label='train')\n", "pyplot.plot(history.history['val_loss'], label='test')\n", "pyplot.legend()\n", "pyplot.show()\n", " \n", "# make a prediction\n", "yhat = model.predict(test_X)\n", "#reducing to 2D\n", "test_X = test_X.reshape((test_X.shape[0], test_X.shape[2]))\n", "# invert scaling for forecast\n", "inv_yhat = concatenate((yhat, test_X[:, 1:]), axis=1)\n", "inv_yhat = scaler.inverse_transform(inv_yhat)\n", "inv_yhat = inv_yhat[:,0]\n", "\n", "\n", "# invert scaling for actual\n", "test_y = test_y.reshape((len(test_y), 1))\n", "inv_y = concatenate((test_y, test_X[:, 1:]), axis=1)\n", "inv_y = scaler.inverse_transform(inv_y)\n", "inv_y = inv_y[:,0]\n", "\n", "\n", "y_GDPF=inv_yhat\n", "\n", "\n", "size1=len(data)\n", "yGDPF=pd.DataFrame(y_GDPF)\n", "\n", "yGDPF.rename(columns={0:'GDP'}, inplace=True)\n", "\n", "basedate=data.iloc[size1-h-1:size1-h,:]\n", "GDP_basedate=basedate['GDP']\n", "forecast2=yGDPF[['GDP' ]]\n", "GDP_basedate=GDP_basedate.reset_index()\n", "# del GDP_basedate['date']\n", "\n", "merge2=pd.concat([GDP_basedate,forecast2],axis=0,ignore_index=True)\n", "GDP_forecastlevels=merge2.cumsum()\n", "GDP_forecastlevels=GDP_forecastlevels.iloc[1:len(GDP_forecastlevels),:]\n", "GDP_forecastlevels=GDP_forecastlevels.reset_index()\n", "del GDP_forecastlevels['index']\n", "test_level=data.iloc[size1-h:size1,:]\n", "GDP_test=test_level['GDP']\n", "\n", "# calculate RMSE\n", "GDP_test=GDP_test.reset_index()\n", "del GDP_test['index']\n", "del GDP_forecastlevels['level_0']\n", "rmse = sqrt(mean_squared_error(GDP_test, GDP_forecastlevels))\n", "print('RMSE: %.3f' % rmse)\n", "\n", "mape=100*(abs(GDP_test-GDP_forecastlevels)/GDP_test).mean()\n", "print('MAPE: %.3f' % mape)\n", "\n", "#out of sample real prediction\n", "test_r1=test_X[-1,0:]\n", "test_r1 = test_r1.reshape(1,1, test_r1.shape[0])\n", "\n", "real_prd_GDP=model.predict(test_r1)\n", "test_r1=test_r1.reshape(1,4)\n", "inv_real_prd_GDP = concatenate((real_prd_GDP, test_r1[:, 1:]), axis=1)\n", "inv_real_prd_GDP = scaler.inverse_transform(inv_real_prd_GDP)\n", "inv_real_prd_GDP = inv_real_prd_GDP[:,0]\n", "\n", "pred_real_GDP=pd.DataFrame(inv_real_prd_GDP)\n", "pred_real_GDP.rename(columns={0:'GDP'}, inplace=True)\n", "#conversion of oopr to level\n", "\n", "baseoopr=data[-1:]\n", "GDP_baseoopr=baseoopr['GDP']\n", "GDP_baseoopr=GDP_baseoopr.reset_index()\n", "\n", "\n", "merge3=pd.concat([GDP_baseoopr,pred_real_GDP ],axis=0,ignore_index=True)\n", "GDP_ooprlevels=merge3.cumsum()\n", "GDP_ooprlevels=GDP_ooprlevels.iloc[1:len(GDP_ooprlevels),:]\n", "GDP_ooprlevels=GDP_ooprlevels.reset_index()\n", "del GDP_ooprlevels['index']\n", "del GDP_ooprlevels['level_0']\n", "##############################################\n", "#LSTM for 2nd Equation\n", "values = vardata.values\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled = scaler.fit_transform(values)\n", "# frame as supervised learning\n", "reframed = series_to_supervised(scaled, 1, 1)\n", "\n", "print(reframed.head())\n", "\n", "# split into train and test sets\n", "values = reframed.values\n", "# drop columns we don't want to predict \n", "#to change for each equation\n", "reframed.drop(reframed.columns[[4,6,7]], axis=1, inplace=True)\n", "values = reframed.values\n", "train_sample=(n_obs-h)\n", "train=values[0:train_sample,:]\n", "test = values[train_sample:, :]\n", "\n", "\n", "# split into input and outputs\n", "train_X, train_y = train[:, :-1], train[:, -1]\n", "test_X, test_y = test[:, :-1], test[:, -1]\n", "# reshape input to be 3D [samples, timesteps, features]\n", "train_X = train_X.reshape((train_X.shape[0], 1, train_X.shape[1]))\n", "test_X = test_X.reshape((test_X.shape[0], 1, test_X.shape[1]))\n", "print(train_X.shape, train_y.shape, test_X.shape, test_y.shape)\n", "# design network\n", "model2 = Sequential()\n", "model2.add(LSTM(50, input_shape=(train_X.shape[1], train_X.shape[2])))\n", "model2.add(<PERSON><PERSON>(1))\n", "model2.compile(loss='mae', optimizer='adam')\n", "# fit network\n", "history = model2.fit(train_X, train_y, epochs=50, batch_size=100, validation_data=(test_X, test_y), verbose=2, shuffle=False)\n", "# plot history\n", "pyplot.plot(history.history['loss'], label='train')\n", "pyplot.plot(history.history['val_loss'], label='test')\n", "pyplot.legend()\n", "pyplot.show()\n", "# make a prediction\n", "yhat = model2.predict(test_X)\n", "#reducing to 2D\n", "test_X = test_X.reshape((test_X.shape[0], test_X.shape[2]))\n", "# invert scaling for forecast\n", "inv_yhat = concatenate((yhat, test_X[:, 1:]), axis=1)\n", "inv_yhat = scaler.inverse_transform(inv_yhat)\n", "inv_yhat = inv_yhat[:,0]\n", "\n", "\n", "# invert scaling for actual\n", "test_y = test_y.reshape((len(test_y), 1))\n", "inv_y = concatenate((test_y, test_X[:, 1:]), axis=1)\n", "inv_y = scaler.inverse_transform(inv_y)\n", "inv_y = inv_y[:,0]\n", "\n", "\n", "y_HouseholdConsumptionF=inv_yhat\n", "\n", "\n", "size1=len(data)\n", "yHouseholdConsumptionF=pd.DataFrame(y_HouseholdConsumptionF)\n", "\n", "yHouseholdConsumptionF.rename(columns={0:'HouseholdConsumption'}, inplace=True)\n", "\n", "basedate=data.iloc[size1-h-1:size1-h,:]\n", "HouseholdConsumption_basedate=basedate['HouseholdConsumption']\n", "forecast2=yHouseholdConsumptionF[['HouseholdConsumption' ]]\n", "HouseholdConsumption_basedate=HouseholdConsumption_basedate.reset_index()\n", "\n", "\n", "merge2=pd.concat([HouseholdConsumption_basedate,forecast2],axis=0,ignore_index=True)\n", "HouseholdConsumption_forecastlevels=merge2.cumsum()\n", "HouseholdConsumption_forecastlevels=HouseholdConsumption_forecastlevels.iloc[1:len(HouseholdConsumption_forecastlevels),:]\n", "HouseholdConsumption_forecastlevels=HouseholdConsumption_forecastlevels.reset_index()\n", "del HouseholdConsumption_forecastlevels['index']\n", "test_level=data.iloc[size1-h:size1,:]\n", "HouseholdConsumption_test=test_level['HouseholdConsumption']\n", "\n", "# calculate RMSE\n", "HouseholdConsumption_test=HouseholdConsumption_test.reset_index()\n", "del HouseholdConsumption_test['index']\n", "del HouseholdConsumption_forecastlevels['level_0']\n", "rmse = sqrt(mean_squared_error(HouseholdConsumption_test, HouseholdConsumption_forecastlevels))\n", "print('RMSE: %.3f' % rmse)\n", "mape=100*(abs(HouseholdConsumption_test-HouseholdConsumption_forecastlevels)/HouseholdConsumption_test).mean()\n", "print('MAPE: %.3f' % mape)\n", "\n", "#out of sample real prediction\n", "test_r1=test_X[-1,0:]\n", "test_r1 = test_r1.reshape(1,1, test_r1.shape[0])\n", "\n", "real_prd_HouseholdConsumption=model.predict(test_r1)\n", "test_r1=test_r1.reshape(1,4)\n", "inv_real_prd_HouseholdConsumption= concatenate((real_prd_HouseholdConsumption, test_r1[:, 1:]), axis=1)\n", "inv_real_prd_HouseholdConsumption = scaler.inverse_transform(inv_real_prd_HouseholdConsumption)\n", "inv_real_prd_HouseholdConsumption = inv_real_prd_HouseholdConsumption[:,0]\n", "\n", "pred_real_HouseholdConsumption=pd.DataFrame(inv_real_prd_HouseholdConsumption)\n", "pred_real_HouseholdConsumption.rename(columns={0:'HouseholdConsumption'}, inplace=True)\n", "#conversion of oopr to level\n", "\n", "baseoopr=data[-1:]\n", "HouseholdConsumption_baseoopr=baseoopr['HouseholdConsumption']\n", "HouseholdConsumption_baseoopr=HouseholdConsumption_baseoopr.reset_index()\n", "\n", "\n", "merge3=pd.concat([HouseholdConsumption_baseoopr,pred_real_HouseholdConsumption],axis=0,ignore_index=True)\n", "HouseholdConsumption_ooprlevels=merge3.cumsum()\n", "HouseholdConsumption_ooprlevels=HouseholdConsumption_ooprlevels.iloc[1:len(HouseholdConsumption_ooprlevels),:]\n", "HouseholdConsumption_ooprlevels=HouseholdConsumption_ooprlevels.reset_index()\n", "del HouseholdConsumption_ooprlevels['index']\n", "\n", "\n", "\n", "##############################################\n", "#LSTM for 3nd Equation\n", "values = vardata.values\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled = scaler.fit_transform(values)\n", "# frame as supervised learning\n", "reframed = series_to_supervised(scaled, 1, 1)\n", "\n", "print(reframed.head())\n", "\n", "# split into train and test sets\n", "values = reframed.values\n", "# drop columns we don't want to predict \n", "#to change for each equation\n", "reframed.drop(reframed.columns[[4,5,7]], axis=1, inplace=True)\n", "\n", "\n", "values = reframed.values\n", "values = reframed.values\n", "train_sample=(n_obs-h)\n", "train=values[0:train_sample,:]\n", "test = values[train_sample:, :]\n", "\n", "\n", "# split into input and outputs\n", "train_X, train_y = train[:, :-1], train[:, -1]\n", "test_X, test_y = test[:, :-1], test[:, -1]\n", "# reshape input to be 3D [samples, timesteps, features]\n", "train_X = train_X.reshape((train_X.shape[0], 1, train_X.shape[1]))\n", "test_X = test_X.reshape((test_X.shape[0], 1, test_X.shape[1]))\n", "print(train_X.shape, train_y.shape, test_X.shape, test_y.shape)\n", "# design network\n", "model3 = Sequential()\n", "model3.add(LSTM(50, input_shape=(train_X.shape[1], train_X.shape[2])))\n", "model3.add(<PERSON><PERSON>(1))\n", "model3.compile(loss='mae', optimizer='adam')\n", "# fit network\n", "history = model3.fit(train_X, train_y, epochs=50, batch_size=100, validation_data=(test_X, test_y), verbose=2, shuffle=False)\n", "# plot history\n", "pyplot.plot(history.history['loss'], label='train')\n", "pyplot.plot(history.history['val_loss'], label='test')\n", "pyplot.legend()\n", "pyplot.show()\n", "# make a prediction\n", "yhat = model3.predict(test_X)\n", "#reducing to 2D\n", "test_X = test_X.reshape((test_X.shape[0], test_X.shape[2]))\n", "# invert scaling for forecast\n", "inv_yhat = concatenate((yhat, test_X[:, 1:]), axis=1)\n", "inv_yhat = scaler.inverse_transform(inv_yhat)\n", "inv_yhat = inv_yhat[:,0]\n", "\n", "\n", "# invert scaling for actual\n", "test_y = test_y.reshape((len(test_y), 1))\n", "inv_y = concatenate((test_y, test_X[:, 1:]), axis=1)\n", "inv_y = scaler.inverse_transform(inv_y)\n", "inv_y = inv_y[:,0]\n", "\n", "\n", "y_GoodServiceExportF=inv_yhat\n", "\n", "\n", "size1=len(data)\n", "yGoodServiceExportF=pd.DataFrame(y_GoodServiceExportF)\n", "\n", "yGoodServiceExportF.rename(columns={0:'GoodServiceExport'}, inplace=True)\n", "\n", "basedate=data.iloc[size1-h-1:size1-h,:]\n", "GoodServiceExport_basedate=basedate['GoodServiceExport']\n", "forecast2=yGoodServiceExportF[['GoodServiceExport' ]]\n", "GoodServiceExport_basedate=GoodServiceExport_basedate.reset_index()\n", "\n", "\n", "merge3=pd.concat([GoodServiceExport_basedate,forecast2],axis=0,ignore_index=True)\n", "GoodServiceExport_forecastlevels=merge3.cumsum()\n", "GoodServiceExport_forecastlevels=GoodServiceExport_forecastlevels.iloc[1:len(GoodServiceExport_forecastlevels),:]\n", "GoodServiceExport_forecastlevels=GoodServiceExport_forecastlevels.reset_index()\n", "del GoodServiceExport_forecastlevels['index']\n", "test_level=data.iloc[size1-h:size1,:]\n", "GoodServiceExport_test=test_level['GoodServiceExport']\n", "\n", "# calculate RMSE\n", "GoodServiceExport_test=GoodServiceExport_test.reset_index()\n", "del GoodServiceExport_test['index']\n", "del GoodServiceExport_forecastlevels['level_0']\n", "rmse = sqrt(mean_squared_error(GoodServiceExport_test, GoodServiceExport_forecastlevels))\n", "print('RMSE: %.3f' % rmse)\n", "mape=100*(abs(GoodServiceExport_test-GoodServiceExport_forecastlevels)/GoodServiceExport_test).mean()\n", "print('MAPE: %.3f' % mape)\n", "\n", "#out of sample real prediction\n", "test_r1=test_X[-1,0:]\n", "test_r1 = test_r1.reshape(1,1, test_r1.shape[0])\n", "\n", "real_prd_GoodServiceExport=model3.predict(test_r1)\n", "test_r1=test_r1.reshape(1,4)\n", "inv_real_prd_GoodServiceExport = concatenate((real_prd_GoodServiceExport, test_r1[:, 1:]), axis=1)\n", "inv_real_prd_GoodServiceExport = scaler.inverse_transform(inv_real_prd_GoodServiceExport)\n", "inv_real_prd_GoodServiceExport = inv_real_prd_GoodServiceExport[:,0]\n", "\n", "pred_real_GoodServiceExport=pd.DataFrame(inv_real_prd_GoodServiceExport)\n", "pred_real_GoodServiceExport.rename(columns={0:'GoodServiceExport'}, inplace=True)\n", "#conversion of oopr to level\n", "\n", "baseoopr=data[-1:]\n", "GoodServiceExport_baseoopr=baseoopr['GoodServiceExport']\n", "GoodServiceExport_baseoopr=GoodServiceExport_baseoopr.reset_index()\n", "\n", "\n", "merge3=pd.concat([GoodServiceExport_baseoopr,pred_real_GoodServiceExport ],axis=0,ignore_index=True)\n", "GoodServiceExport_ooprlevels=merge3.cumsum()\n", "GoodServiceExport_ooprlevels=GoodServiceExport_ooprlevels.iloc[1:len(GoodServiceExport_ooprlevels),:]\n", "GoodServiceExport_ooprlevels=GoodServiceExport_ooprlevels.reset_index()\n", "del GoodServiceExport_ooprlevels['index']\n", "\n", "#LSTM for4nd Equation\n", "values = vardata.values\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled = scaler.fit_transform(values)\n", "# frame as supervised learning\n", "reframed = series_to_supervised(scaled, 1, 1)\n", "\n", "print(reframed.head())\n", "\n", "# split into train and test sets\n", "values = reframed.values\n", "# drop columns we don't want to predict \n", "#to change for each equation\n", "reframed.drop(reframed.columns[[4,5,6]], axis=1, inplace=True)\n", "print(reframed.head())\n", "\n", "# split into train and test sets\n", "values = reframed.values\n", "# drop columns we don't want to predict \n", "#to change for each equation\n", "values = reframed.values\n", "values = reframed.values\n", "train_sample=(n_obs-h)\n", "train=values[0:train_sample,:]\n", "test = values[train_sample:, :]\n", "\n", "\n", "# split into input and outputs\n", "train_X, train_y = train[:, :-1], train[:, -1]\n", "test_X, test_y = test[:, :-1], test[:, -1]\n", "# reshape input to be 3D [samples, timesteps, features]\n", "train_X = train_X.reshape((train_X.shape[0], 1, train_X.shape[1]))\n", "test_X = test_X.reshape((test_X.shape[0], 1, test_X.shape[1]))\n", "print(train_X.shape, train_y.shape, test_X.shape, test_y.shape)\n", "# design network\n", "model4 = Sequential()\n", "model4.add(LSTM(50, input_shape=(train_X.shape[1], train_X.shape[2])))\n", "model4.add(<PERSON><PERSON>(1))\n", "model4.compile(loss='mae', optimizer='adam')\n", "# fit network\n", "history = model4.fit(train_X, train_y, epochs=50, batch_size=100, validation_data=(test_X, test_y), verbose=2, shuffle=False)\n", "# plot history\n", "pyplot.plot(history.history['loss'], label='train')\n", "pyplot.plot(history.history['val_loss'], label='test')\n", "pyplot.legend()\n", "pyplot.show()\n", "# make a prediction\n", "yhat = model4.predict(test_X)\n", "#reducing to 2D\n", "test_X = test_X.reshape((test_X.shape[0], test_X.shape[2]))\n", "# invert scaling for forecast\n", "inv_yhat = concatenate((yhat, test_X[:, 1:]), axis=1)\n", "inv_yhat = scaler.inverse_transform(inv_yhat)\n", "inv_yhat = inv_yhat[:,0]\n", "\n", "\n", "# invert scaling for actual\n", "test_y = test_y.reshape((len(test_y), 1))\n", "inv_y = concatenate((test_y, test_X[:, 1:]), axis=1)\n", "inv_y = scaler.inverse_transform(inv_y)\n", "inv_y = inv_y[:,0]\n", "\n", "\n", "y_GoodServiceImportF=inv_yhat\n", "\n", "\n", "size1=len(data)\n", "yGoodServiceImportF=pd.DataFrame(y_GoodServiceImportF)\n", "\n", "yGoodServiceImportF.rename(columns={0:'GoodServiceImport'}, inplace=True)\n", "\n", "basedate=data.iloc[size1-h-1:size1-h,:]\n", "GoodServiceImport_basedate=basedate['GoodServiceImport']\n", "forecast2=yGoodServiceImportF[['GoodServiceImport' ]]\n", "GoodServiceImport_basedate=GoodServiceImport_basedate.reset_index()\n", "\n", "\n", "merge4=pd.concat([GoodServiceImport_basedate,forecast2],axis=0,ignore_index=True)\n", "GoodServiceImport_forecastlevels=merge4.cumsum()\n", "GoodServiceImport_forecastlevels=GoodServiceImport_forecastlevels.iloc[1:len(GoodServiceImport_forecastlevels),:]\n", "GoodServicImport_forecastlevels=GoodServiceImport_forecastlevels.reset_index()\n", "del GoodServiceImport_forecastlevels['index']\n", "test_level=data.iloc[size1-h:size1,:]\n", "GoodServiceImport_test=test_level['GoodServiceImport']\n", "\n", "# calculate RMSE\n", "GoodServiceImport_test=GoodServiceImport_test.reset_index()\n", "del GoodServiceImport_test['index']\n", "rmse = sqrt(mean_squared_error(GoodServiceImport_test, GoodServiceImport_forecastlevels))\n", "print('RMSE: %.3f' % rmse)\n", "mape=100*(abs(GoodServiceImport_test-GoodServiceImport_forecastlevels)/GoodServiceImport_test).mean()\n", "print('MAPE: %.3f' % mape)\n", "\n", "#out of sample real prediction\n", "test_r1=test_X[-1,0:]\n", "test_r1 = test_r1.reshape(1,1, test_r1.shape[0])\n", "\n", "real_prd_GoodServiceImport=model4.predict(test_r1)\n", "test_r1=test_r1.reshape(1,4)\n", "inv_real_prd_GoodServiceImport = concatenate((real_prd_GoodServiceImport, test_r1[:, 1:]), axis=1)\n", "inv_real_prd_GoodServiceImport = scaler.inverse_transform(inv_real_prd_GoodServiceImport)\n", "inv_real_prd_GoodServiceImport = inv_real_prd_GoodServiceImport[:,0]\n", "\n", "pred_real_GoodServiceImport=pd.DataFrame(inv_real_prd_GoodServiceImport)\n", "pred_real_GoodServiceImport.rename(columns={0:'GoodServiceImport'}, inplace=True)\n", "#conversion of oopr to level\n", "\n", "baseoopr=data[-1:]\n", "GoodServiceImport_baseoopr=baseoopr['GoodServiceImport']\n", "GoodServiceImport_baseoopr=GoodServiceImport_baseoopr.reset_index()\n", "\n", "\n", "merge4=pd.concat([GoodServiceImport_baseoopr,pred_real_GoodServiceImport ],axis=0,ignore_index=True)\n", "GoodServiceImport_ooprlevels=merge4.cumsum()\n", "GoodServiceImport_ooprlevels=GoodServiceImport_ooprlevels.iloc[1:len(GoodServiceImport_ooprlevels),:]\n", "GoodServiceImport_ooprlevels=GoodServiceImport_ooprlevels.reset_index()\n", "del GoodServiceImport_ooprlevels['index']\n", "\n", "\n", "\n", "#2 Step ahead prediction for GDP\n", "\n", "inv_yd = concatenate((real_prd_GDP,real_prd_HouseholdConsumption, real_prd_GoodServiceExport,real_prd_GoodServiceImport), axis=0)\n", "inv_yd=inv_yd.reshape(1,1,inv_yd.shape[0])\n", "real_prd2_GDP=model.predict(inv_yd)\n", "\n", "inv_yd=inv_yd.reshape(1,4)\n", "inv_real_prd2_GDP = concatenate((real_prd2_GDP, inv_yd[:, 1:]), axis=1)\n", "inv_real_prd2_GDP = scaler.inverse_transform(inv_real_prd2_GDP)\n", "inv_real_prd2_GDP = inv_real_prd2_GDP[:,0]\n", "\n", "#2 Step ahead prediction for HOUSEHOLDCONSUMPTION\n", "inv_yd = concatenate((real_prd_GDP,real_prd_HouseholdConsumption, real_prd_GoodServiceExport,real_prd_GoodServiceImport), axis=0)\n", "inv_yd=inv_yd.reshape(1,1,inv_yd.shape[0])\n", "real_prd2_HouseholdConsumption=model2.predict(inv_yd)\n", "inv_yd=inv_yd.reshape(1,4)\n", "inv_real_prd2_HouseholdConsumption = concatenate((real_prd2_HouseholdConsumption, inv_yd[:, 1:]), axis=1)\n", "inv_real_prd2_HouseholdConsumption = scaler.inverse_transform(inv_real_prd2_HouseholdConsumption)\n", "inv_real_prd2_HouseholdConsumption = inv_real_prd2_HouseholdConsumption[:,0]\n", "\n", "#2 Step ahead prediction for GOODSERVICEEXPORT\n", "inv_yd = concatenate((real_prd_GDP,real_prd_HouseholdConsumption, real_prd_GoodServiceExport,real_prd_GoodServiceImport), axis=0)\n", "inv_yd=inv_yd.reshape(1,1,inv_yd.shape[0])\n", "real_prd2_GoodServiceExport=model3.predict(inv_yd)\n", "inv_yd=inv_yd.reshape(1,4)\n", "inv_real_prd2_GoodServiceExport = concatenate((real_prd2_GoodServiceExport, inv_yd[:, 1:]), axis=1)\n", "inv_real_prd2_GoodServiceExport = scaler.inverse_transform(inv_real_prd2_GoodServiceExport)\n", "inv_real_prd2_GoodServiceExport= inv_real_prd2_GoodServiceExport[:,0]\n", "\n", "\n", "#2 Step ahead prediction for GOODSERVICEIMPORT\n", "inv_yd = concatenate((real_prd_GDP,real_prd_HouseholdConsumption, real_prd_GoodServiceExport,real_prd_GoodServiceImport), axis=0)\n", "inv_yd=inv_yd.reshape(1,1,inv_yd.shape[0])\n", "real_prd2_GoodServiceImport=model4.predict(inv_yd)\n", "inv_yd=inv_yd.reshape(1,4)\n", "inv_real_prd2_GoodServiceImport = concatenate((real_prd2_GoodServiceImport, inv_yd[:, 1:]), axis=1)\n", "inv_real_prd2_GoodServiceImport = scaler.inverse_transform(inv_real_prd2_GoodServiceImport)\n", "inv_real_prd2_GoodServiceImport= inv_real_prd2_GoodServiceImport[:,0]\n", "#conversion of pred2 to level\n", "\n", "inv_real_prd2_GDP=pd.DataFrame(inv_real_prd2_GDP)\n", "inv_real_prd2_GDP.rename(columns={0:'GDP'}, inplace=True)\n", "inv_real_prd2_HouseholdConsumption=pd.DataFrame(inv_real_prd2_HouseholdConsumption)\n", "inv_real_prd2_HouseholdConsumption.rename(columns={0:'HouseholdConsumption'}, inplace=True)\n", "inv_real_prd2_GoodServiceExport=pd.DataFrame(inv_real_prd2_GoodServiceExport)\n", "inv_real_prd2_GoodServiceExport.rename(columns={0:'GoodServiceExport'}, inplace=True)\n", "inv_real_prd2_GoodServiceImport=pd.DataFrame(inv_real_prd2_GoodServiceImport)\n", "inv_real_prd2_GoodServiceImport.rename(columns={0:'GoodServiceImport'}, inplace=True)\n", "\n", "\n", "\n", "Pred1=pd.concat([GDP_ooprlevels,HouseholdConsumption_ooprlevels,GoodServiceExport_ooprlevels,GoodServiceImport_ooprlevels],axis=1)\n", "del Pred1['level_0']\n", "\n", "Pred2=pd.concat([inv_real_prd2_GDP,inv_real_prd2_HouseholdConsumption,inv_real_prd2_GoodServiceExport,inv_real_prd2_GoodServiceImport],axis=1)\n", "predson=pd.concat([Pred1,Pred2],axis=0)\n", "predson=predson.cumsum()\n", "\n", "predson=predson.reset_index()\n", "<PERSON> pre<PERSON>['index']\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}