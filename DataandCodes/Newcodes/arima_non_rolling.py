# -*- coding: utf-8 -*-
"""
Created on Sun Jan 27 00:46:40 2019

@author: pc
"""


#www.digitalocean.com

#https://www.statsmodels.org/dev/tsa.html
#https://www.statsmodels.org/dev/statespace.html#statespace
#https://www.statsmodels.org/dev/examples/notebooks/generated/statespace_sarimax_stata.html
import warnings
import pandas as pd
#from pandas import ExcelWriter
#from pandas import ExcelFile
import statsmodels.api as sm
import  matplotlib.pylab as plt
from statsmodels.graphics.tsaplots import plot_acf
from statsmodels.graphics.tsaplots import plot_pacf
plt.style.use('fivethirtyeight')
from pathlib import Path

#pip install arch
#ya da
#"Open Anaconda Promt and write:conda install -c bashtage arch "
import arch
from arch.unitroot import ADF
from arch.unitroot import DFGLS
from arch.unitroot import PhillipsPerron
from arch.unitroot import KPSS
import statsmodels
from statsmodels.tsa.stattools import adfuller
from pathlib import Path





data_folder = Path("D:/TimeSeriesandForecasting/DataandCodes")

PORTFOY = pd.read_excel(data_folder /'PORTFOY2022.xlsx', sheet_name='Sheet1')
#PORTFOY = pd.read_excel('PORTFOY2022.xlsx', sheet_name='Sheet1')


DAX=PORTFOY['DAX']
DAX.plot(figsize=(15,4))
data2=DAX
#Serinin durağanlığın kontrolü


def test_stationarity(data_n):    # adf_test = ADF(data2, trend='ct', max_lags=10, method='AIC') 
    adfTest = adfuller(data_n, autolag='AIC')
    p_value=adfTest[1]
    if p_value< 0.01:
            print('seri düzeyde durağan; I(0)') 
            d=0
    else:
          dif_ADF=data_n.diff()
          dif_ADF=dif_ADF.dropna()
          dif_ADF = adfuller(dif_ADF, autolag='AIC')  
          p_value2=dif_ADF[1]
          if p_value2< 0.01:
                print('seri farkta durağan; I(I)') 
                d=1
    return d


d=test_stationarity(data2) 
test_size=21
training_size=len(data2)-test_size
test_sample=data2[training_size:len(data2)]
test_sample=test_sample.reset_index()
del test_sample['index']
training_sample=data2[0:training_size]
mod1=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(1,d,0))

results1=mod1.fit(disp=False)
print(results1.summary())

#results.plot_diagnostics()
residuals1=results1.resid
residuals1=residuals1.iloc[1:-1]
plot_acf(residuals1,lags=10)

#pseudo forecast
pred1=results1.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)

pred_pseudo1=pred1.predicted_mean
pred_pseudo1=pred_pseudo1.reset_index()
del pred_pseudo1['index']
pred_pseudo1.columns = ['predicted']
ypredict1=pred_pseudo1.values
yactual=test_sample.values
mae1=abs(yactual-ypredict1).mean()
mape1=100*(abs(yactual-ypredict1)/yactual).mean()
#model 2
mod2=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(1,d,1))

results2=mod2.fit(disp=False)
print(results2.summary())
#results.plot_diagnostics()
residuals2=results2.resid
residuals2=residuals2.iloc[1:-1]
plot_acf(residuals2,lags=10)

#pseudo forecast
pred2=results2.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)

pred_pseudo2=pred2.predicted_mean
pred_pseudo2=pred_pseudo2.reset_index()
del pred_pseudo2['index']
pred_pseudo2.columns = ['predicted']
ypredict2=pred_pseudo2.values
mae2=abs(yactual-ypredict2).mean()
mape2=100*(abs(yactual-ypredict2)/yactual).mean()
#model 3


mod3=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(2,d,1)) 
                                  
results3=mod3.fit(disp=False)
print(results3.summary())

#results.plot_diagnostics()
residuals3=results3.resid
residuals3=residuals3.iloc[1:-1]
plot_acf(residuals3,lags=10)                                         


pred3=results3.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)

pred_pseudo3=pred3.predicted_mean
pred_pseudo3=pred_pseudo3.reset_index()
del pred_pseudo3['index']
pred_pseudo3.columns = ['predicted']
ypredict3=pred_pseudo3.values
mae3=abs(yactual-ypredict3).mean()
mape3=100*(abs(yactual-ypredict3)/yactual).mean()
#model 4

mod4=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(3,d,1)) 
                                  
results4=mod4.fit(disp=False)
print(results4.summary())

#results.plot_diagnostics()
residuals4=results4.resid
residuals4=residuals4.iloc[1:-1]
plot_acf(residuals4,lags=10)                                         


pred4=results4.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)

pred_pseudo4=pred4.predicted_mean
pred_pseudo4=pred_pseudo4.reset_index()
del pred_pseudo4['index']
pred_pseudo4.columns = ['predicted']
ypredict4=pred_pseudo4.values
mae4=abs(yactual-ypredict4).mean()
mape4=100*(abs(yactual-ypredict4)/yactual).mean()
#model 5


                                       

mod5=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(4,d,1)) 
                                  
results5=mod5.fit(disp=False)
print(results5.summary())

#results.plot_diagnostics()
residuals5=results5.resid
residuals5=residuals5.iloc[1:-1]
plot_acf(residuals5,lags=10)                                         


pred5=results5.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)

pred_pseudo5=pred5.predicted_mean
pred_pseudo5=pred_pseudo5.reset_index()
del pred_pseudo5['index']
pred_pseudo5.columns = ['predicted']
ypredict5=pred_pseudo5.values
mae5=abs(yactual-ypredict5).mean()
mape5=100*(abs(yactual-ypredict5)/yactual).mean()

#real out of sample predictions 
tpred_real=results3.get_prediction(start=len(data2),end=len(data2)+test_size,dynamic=True)
pred_ci=tpred_real.conf_int()
pred_real=tpred_real.predicted_mean
pred_real=pred_real.reset_index()
del pred_real['index']
pred_real.columns = ['real predicted']
print(mape1, mape2, mape3,mape4,mape5)