# -*- coding: utf-8 -*-
"""
Created on <PERSON>e Jan 29 16:01:06 2019

@author: pc
"""
#https://www.statsmodels.org/dev/tsa.html
#https://www.statsmodels.org/dev/statespace.html#statespace
#https://www.statsmodels.org/dev/examples/notebooks/generated/statespace_sarimax_stata.html





import warnings
import pandas as pd
#from pandas import ExcelWriter
#from pandas import ExcelFile
import numpy as np
import statsmodels.api as sm
import  matplotlib.pylab as plt
from statsmodels.graphics.tsaplots import plot_acf
from statsmodels.graphics.tsaplots import plot_pacf
plt.style.use('fivethirtyeight')
from pathlib import Path
data_folder = Path("D:/TimeSeriesandForecasting/DataandCodes")

# data_folder = Path("C:/data/")
# warnings.filterwarnings("ignore")
md = pd.read_excel(data_folder /'Money.xlsx', sheet_name='Sayfa1')
ip=md['ip']
ip.plot()
# df= md.set_index('date')
# df.index


#difference
dif_ip=ip.diff()
dif_ip.plot()
plot_acf(ip,lags=60)
plot_pacf(ip,lags=20)



data2=ip
test_size=12
training_size=len(data2)-test_size
test_sample=data2[training_size:len(data2)]
test_sample=test_sample.reset_index()
del test_sample['index']
training_sample=data2[0:training_size]

#additive seasonality


ar = 1          # this is the maximum degree specification
ma = (1,0,0,0,0,0,0,0,0,0,0,1)  # this is the lag polynomial specification

mod=sm.tsa.statespace.SARIMAX(training_sample,trend='n',order=(ar,1,ma),enforce_stationarity=True, 
                                         enforce_invertibility=True)
results=mod.fit(disp=False)
print(results.summary())

print(results.test_serial_correlation(method='ljungbox', lags=None))
#results.plot_diagnostics()
residuals=results.resid
residuals=residuals.iloc[1:-1]
plot_acf(residuals,lags=10)



#in sample prediction
pred=results.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)
pred_pseudo1=pred.predicted_mean
pred_pseudo1=pred_pseudo1.reset_index()
del pred_pseudo1['index']
pred_pseudo1.columns = ['predicted']
ypredict1=pred_pseudo1.values
yactual=test_sample.values
mae1=abs(yactual-ypredict1).mean()
mape1=100*(abs(yactual-ypredict1)/yactual).mean()



#out of sample prediction
# Getting 12 quarters for forecasts
SARIMAX_forecast = round(results.forecast(steps =12 ), 2)
# Creating an index from 2022.2 to 2023.1 and a SARIMAX_forecast dataframe:
idx = pd.date_range('2022.1', '2023.1', freq='    M')
SARIMAX_forecast = pd.DataFrame(list(zip(list(idx),list(SARIMAX_forecast))), 
                                columns=['Date','ForecastValue']).set_index('Date')





#mutliplicative seasonality
# arma(1,0) with seasonal AR

mod2=sm.tsa.statespace.SARIMAX(training_sample,trend='n',order=(1,1,0),seasonal_order=(1,1,0,12),enforce_stationarity=True, 
                                         enforce_invertibility=True)


results2=mod2.fit(disp=False)
print(results2.summary())

print(results2.test_serial_correlation(method='ljungbox', lags=None))
#results.plot_diagnostics()
residuals2=results2.resid
residuals2=residuals2.iloc[1:-1]
plot_acf(residuals2,lags=10)



#in sample prediction
pred2=results2.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)
pred_pseudo2=pred2.predicted_mean
pred_pseudo2=pred_pseudo2.reset_index()
del pred_pseudo2['index']
pred_pseudo2.columns = ['predicted']
ypredict2=pred_pseudo2.values
yactual=test_sample.values
mae2=abs(yactual-ypredict2).mean()
mape2=100*(abs(yactual-ypredict2)/yactual).mean()

#out of sample prediction
# Getting 12 quarters for forecasts
SARIMAX_forecast2 = round(results2.forecast(steps =12 ), 2)

SARIMAX_forecast2 = pd.DataFrame(list(zip(list(idx),list(SARIMAX_forecast2))), 
                                columns=['Date','ForecastValue']).set_index('Date')




