{"cells": [{"cell_type": "code", "execution_count": null, "id": "5ceef63f", "metadata": {}, "outputs": [], "source": ["import warnings\n", "import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "from pathlib import Path\n", "\n", "from statsmodels.tsa.api import VAR\n", "from statsmodels.tsa.api import VECM\n", "\n", "data_folder = Path(\"D:/TimeSeriesandForecasting/DataandCodes\")\n", "\n", "md = pd.read_excel(data_folder /'Money.xlsx', sheet_name='Sayfa1')\n", "df=md\n", "ip=df[\"ip\"]\n", "Real_Money=df[\"Real_Money\"]\n", "interest_rate=df[\"interest_rate\"]\n", "\n", "#creation of VAR dataset\n", "tempdata= df[[\"ip\",'Real_Money','interest_rate']]\n", "vardata=tempdata.diff().dropna()    \n", "train_var=vardata.iloc[0:120]\n", "test_var=vardata.iloc[-12:]\n", "\n", "## level data for later use \n", "train_level=tempdata.iloc[0:121]\n", "test_level=tempdata.iloc[-12:]\n", "\n", "# VAR Model\n", "model_var = VAR(train_var)\n", "result_var=model_var.fit( maxlags=15, ic='aic', trend='c')\n", "stable1=result_var.is_stable(verbose=False) \n", "print(stable1) \n", " \n", "result_var.summary()\n", "result_var.plot()\n", "result_var.plot_acorr()\n", "nobs=result_var.nobs\n", "lag_order = result_var.k_ar\n", "print(lag_order)\n", "nobs_train_var=len(train_var)\n", "\n", "## Forecasting\n", "pseudo_for=result_var.forecast(train_var.values[-lag_order:],steps=12)\n", "pseudo_for=pd.DataFrame(pseudo_for)\n", "pseudo_for.rename(columns={0:\"ip\",1:\"Real_Money\", 2:\"interest_rate\"}, inplace=True)\n", "\n", "\n", "# Input data for forecasting\n", "input_data = vardata.values[-lag_order:]\n", "print(input_data)\n", "# forecasting\n", "pred = result_var.forecast(y=input_data, steps=12)\n", "pred = (pd.DataFrame(pred, index=test_var.index, columns= test_var.columns + '_pred'))\n", "print(pred)\n", "\n", "\n", "#### Getting Levels\n", "#   #    first option:\n", "basedate=train_level.iloc[-1:] #last observation in the train_level set\n", "merge=pd.concat([basedate,pseudo_for],axis=0, ignore_index=True)\n", "test_forecastlevels=merge.cumsum()\n", "test_forecastlevels=test_forecastlevels.drop([0],axis=0)\n", "\n", "\n", "#   #    second option: with function\n", "\n", "def invert_transformation(df_train, df_forecast):\n", "    \"\"\"Revert back the differencing to get the forecast to original scale.\"\"\"\n", "    df_fc = df_forecast.copy()\n", "    columns = df_train.columns\n", "    for col in columns:\n", "        df_fc[str(col)+'_pred'] = df_train[col].iloc[-1] + df_fc[str(col)].cumsum()\n", "    df_fc=df_fc.iloc[:,3:]\n", "    return df_fc\n", "\n", "output = invert_transformation(train_level, pseudo_for) \n", "\n", "\n", "predictions_var_diff =result_var.fittedvalues\n", "\n", "#comparing test values with the forecasted values\n", "test_level=test_level.reset_index(drop=True) #index and column names have to be same.\n", "output=output.reset_index(drop=True)\n", "output.columns=[\"ip\",\"Real_Money\",\"interest_rate\"]\n", "mae1=abs(test_level-output).mean()\n", "mape1=100*(abs(test_level-output)/test_level).mean()\n", "\n", "\n", "\n", "##  impulse-response functions\n", "\n", "irf = result_var.irf(12)\n", "irf.plot(orth=True)\n", "\n", "irf.plot(impulse='ip') #just for the RGDP\n", "irf.plot(impulse='Real_Money') \n", "irf.plot(impulse='interest_rate') \n", "irf.plot_cum_effects(orth=True)\n", "\n", "## Forecast <PERSON><PERSON><PERSON>om<PERSON> (FEVD)\n", "fevd = result_var.fevd(12)\n", "fevd.summary()\n", "result_var.fevd(12).plot()\n", "\n", "\n", "## GRANGER CAUSALITY CHECK\n", "print(result_var.test_causality('ip', [\"Real_Money\",\"interest_rate\"], kind='f'))\n", "\n", "from statsmodels.tsa.stattools import grangercausalitytests\n", "re=grangercausalitytests(np.column_stack((train_var['ip'],train_var['Real_Money'])),maxlag=10)\n", "\n", "\n", "maxlag=10\n", "test_granger = 'ssr_chi2test'\n", "def grangers_causation_matrix(data, variables, test_granger = 'ssr_chi2test', verbose=False):    \n", "    \"\"\"Check Granger Causality of all possible combinations of the Time series.\n", "    The rows are the response variable, columns are predictors. The values in the table \n", "    are the P-Values. P-Values lesser than the significance level (0.05), implies \n", "    the Null Hypothesis that the coefficients of the corresponding past values is \n", "    zero, that is, the X does not cause Y can be rejected.\n", "\n", "    data      : pandas dataframe containing the time series variables\n", "    variables : list containing names of the time series variables.\n", "    \"\"\"\n", "    df = pd.DataFrame(np.zeros((len(variables), len(variables))), columns=variables, index=variables)\n", "    for c in df.columns:\n", "        for r in df.index:\n", "            test_result = grangercausalitytests(data[[r, c]], maxlag=maxlag, verbose=False)\n", "            p_values = [round(test_result[i+1][0][test_granger][1],4) for i in range(maxlag)]\n", "            if verbose: print(f'Y = {r}, X = {c}, P Values = {p_values}')\n", "            min_p_value = np.min(p_values)\n", "            df.loc[r, c] = min_p_value\n", "    df.columns = [var + '_x' for var in variables]\n", "    df.index = [var + '_y' for var in variables]\n", "    return df\n", "\n", "grangers_causation_matrix(train_var, variables = train_var.columns) "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}