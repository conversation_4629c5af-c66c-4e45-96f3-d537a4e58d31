# -*- coding: utf-8 -*-
"""
Created on Sat Jul  2 12:51:29 2022

@author: ITU
"""

import numpy as np
import pandas as pd
import statsmodels.api as sm
import statsmodels.formula.api as smf
import matplotlib.pyplot as plt

from pathlib import Path

data_folder = Path("C:/Users/<USER>/Desktop/TimeSeriesandForecasting/DataandCodes")

data = pd.read_excel(data_folder /'ihracat.xlsx', sheet_name='Sayfa1')

df2=data[['GDP','HouseholdConsumption','GoodServiceExport','GoodServiceImport']]

Y=df2[['GDP']]
x=df2[['HouseholdConsumption', 'GoodServiceExport', 'GoodServiceImport']]
X=sm.add_constant(x)
test_size=5
train_size=len(Y)-test_size
test_sample=Y[train_size:len(Y)]
test_sample=test_sample.reset_index()
lf=len(Y)-train_size
matpredall=np.zeros((lf,1))

matrix = np.zeros((1,1)) # Pre-allocate matrix


testx=X[train_size:len(X)]
testy=Y[train_size:len(X)]
testx=testx.reset_index()
del testx['index']
testy=testy.reset_index()
del testy['index']
MAPE_ALL=list()
def range_with_floats(start, stop, step):
    while stop > start:
        yield start
        start += step
       
for qs in  range_with_floats(0.5, 0.95, 0.05):
      for j in range(lf):
                X_train=X[0+j:train_size+j]
                y_train=Y[0+j:train_size+j]
                X_test=testx[0+j:1+j]
                y_test=testy[0+j:1+j]
                df3=pd.concat([y_train,X_train],axis=1)
                m=0
                model = smf.quantreg('GDP ~ HouseholdConsumption+GoodServiceExport+GoodServiceImport',data=df3)
                fitted = model.fit(q=qs)
                fitted.summary()
                fitted = model.fit(q=qs).predict()
                y_pred = model.fit(q=qs).predict(X_test)
                matrix[:,m] = y_pred
                m=m+1
                print(j)   
                matpredall[j,0]=matrix
                matytraintest=Y[train_size:len(Y)]  
                matytraintest=np.array(matytraintest)
                lenmatytraintest=len( matytraintest)
                dfmatytraintest=pd.DataFrame(matytraintest)
                dfmatpredict=pd.DataFrame(matpredall)
                fark=dfmatytraintest.values- dfmatpredict.values
                Mat_error=abs(fark) 
                Mat_MAE=Mat_error.mean(0)
                Mat_MAE=Mat_MAE.tolist()
                Mat_errorrate=(Mat_error/dfmatytraintest.values)*100
                Mat_MAPE=Mat_errorrate.mean(0)
                Mat_MAPE=Mat_MAPE.tolist()
                pseudo_predicted_price=pd.DataFrame(matpredall)
                pseudo_predicted_price.columns=['Predicted_GDP']
      MAPE_ALL.append(Mat_MAPE)
      
index_min = np.argmin(MAPE_ALL)
qs_1=np.arange(0.5,0.95,0.05)
qs=qs_1[index_min]
#real out of sample prediction
#verinin son satır alınıyor
testx2=X[len(X)-1:len(X)]
real_pred = model.fit(q=qs).predict(testx2)
real_pred=real_pred.reset_index()
del real_pred['index']
real_pred.columns=['Real_Predicted_GDP']
print(real_pred)