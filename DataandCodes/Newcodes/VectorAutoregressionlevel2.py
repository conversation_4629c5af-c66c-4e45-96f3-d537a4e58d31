# -*- coding: utf-8 -*-
"""
Created on Thu Jun 23 16:57:23 2022

@author: ITU
"""

import warnings
import pandas as pd
import numpy as np
import statsmodels.api as sm
from pathlib import Path

from statsmodels.tsa.api import VAR
from statsmodels.tsa.api import VECM

data_folder = Path("D:/TimeSeriesandForecasting/DataandCodes")

md = pd.read_excel(data_folder /'Money.xlsx', sheet_name='Sayfa1')
df=md
ip=df["ip"]
Real_Money=df["Real_Money"]
interest_rate=df["interest_rate"]

#creation of VAR dataset
tempdata= df[["ip",'Real_Money','interest_rate']]
vardata=tempdata.diff().dropna()    
train_var=vardata.iloc[0:120]
test_var=vardata.iloc[-12:]

## level data for later use 
train_level=tempdata.iloc[0:121]
test_level=tempdata.iloc[-12:]

# VAR Model
model_var = VAR(train_var)
result_var=model_var.fit( maxlags=15, ic='aic', trend='c')
stable1=result_var.is_stable(verbose=False) 
print(stable1) 
 
result_var.summary()
result_var.plot()
result_var.plot_acorr()
nobs=result_var.nobs
lag_order = result_var.k_ar
print(lag_order)
nobs_train_var=len(train_var)

## Forecasting
pseudo_for=result_var.forecast(train_var.values[-lag_order:],steps=12)
pseudo_for=pd.DataFrame(pseudo_for)
pseudo_for.rename(columns={0:"ip",1:"Real_Money", 2:"interest_rate"}, inplace=True)


# Input data for forecasting
input_data = vardata.values[-lag_order:]
print(input_data)
# forecasting
pred = result_var.forecast(y=input_data, steps=12)
pred = (pd.DataFrame(pred, index=test_var.index, columns= test_var.columns + '_pred'))
print(pred)


#### Getting Levels
#   #    first option:
basedate=train_level.iloc[-1:] #last observation in the train_level set
merge=pd.concat([basedate,pseudo_for],axis=0, ignore_index=True)
test_forecastlevels=merge.cumsum()
test_forecastlevels=test_forecastlevels.drop([0],axis=0)


#   #    second option: with function

def invert_transformation(df_train, df_forecast):
    """Revert back the differencing to get the forecast to original scale."""
    df_fc = df_forecast.copy()
    columns = df_train.columns
    for col in columns:
        df_fc[str(col)+'_pred'] = df_train[col].iloc[-1] + df_fc[str(col)].cumsum()
    df_fc=df_fc.iloc[:,3:]
    return df_fc

output = invert_transformation(train_level, pseudo_for) 


predictions_var_diff =result_var.fittedvalues

#comparing test values with the forecasted values
test_level=test_level.reset_index(drop=True) #index and column names have to be same.
output=output.reset_index(drop=True)
output.columns=["ip","Real_Money","interest_rate"]
mae1=abs(test_level-output).mean()
mape1=100*(abs(test_level-output)/test_level).mean()



##  impulse-response functions

irf = result_var.irf(12)
irf.plot(orth=True)

irf.plot(impulse='ip') #just for the RGDP
irf.plot(impulse='Real_Money') 
irf.plot(impulse='interest_rate') 
irf.plot_cum_effects(orth=True)

## Forecast Error Variance Decomposition (FEVD)
fevd = result_var.fevd(12)
fevd.summary()
result_var.fevd(12).plot()


## GRANGER CAUSALITY CHECK
print(result_var.test_causality('ip', ["Real_Money","interest_rate"], kind='f'))

from statsmodels.tsa.stattools import grangercausalitytests
re=grangercausalitytests(np.column_stack((train_var['ip'],train_var['Real_Money'])),maxlag=10)


maxlag=10
test_granger = 'ssr_chi2test'
def grangers_causation_matrix(data, variables, test_granger = 'ssr_chi2test', verbose=False):    
    """Check Granger Causality of all possible combinations of the Time series.
    The rows are the response variable, columns are predictors. The values in the table 
    are the P-Values. P-Values lesser than the significance level (0.05), implies 
    the Null Hypothesis that the coefficients of the corresponding past values is 
    zero, that is, the X does not cause Y can be rejected.

    data      : pandas dataframe containing the time series variables
    variables : list containing names of the time series variables.
    """
    df = pd.DataFrame(np.zeros((len(variables), len(variables))), columns=variables, index=variables)
    for c in df.columns:
        for r in df.index:
            test_result = grangercausalitytests(data[[r, c]], maxlag=maxlag, verbose=False)
            p_values = [round(test_result[i+1][0][test_granger][1],4) for i in range(maxlag)]
            if verbose: print(f'Y = {r}, X = {c}, P Values = {p_values}')
            min_p_value = np.min(p_values)
            df.loc[r, c] = min_p_value
    df.columns = [var + '_x' for var in variables]
    df.index = [var + '_y' for var in variables]
    return df

grangers_causation_matrix(train_var, variables = train_var.columns)  # result:
"""
                  ip_x  Real_Money_x  interest_rate_x
ip_y             1.000        0.0010           0.1481
Real_Money_y     0.000        1.0000           0.0001
interest_rate_y  0.468        0.0171           1.0000

"""
#If a given p-value is < significance level (0.05),
#then, the corresponding X series (column) causes the Y (row).
#For example, P-Value of 0.0010 at (row 1, column 2) represents
#the p-value of the Grangers Causality test for Real_Money_x  causing ip_y,
#which is less that the significance level of 0.05.
#So, you can reject the null hypothesis and conclude Real_Money_x causes ip_y.














