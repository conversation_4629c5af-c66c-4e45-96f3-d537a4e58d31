# -*- coding: utf-8 -*-
"""
Created on Thu Jun 30 18:52:51 2022

@author: ITU
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
from pathlib import Path


def var_lags(var,nlags):
   name=var.name
   str1=str(name)
   var1=var
   df=pd.DataFrame()
   for i in range(nlags):
        df[str1+str(i+1)]=var1.shift(i+1)
        df2=df
   return df2

data_folder = Path("D:/TimeSeriesandForecasting/DataandCodes")
data = pd.read_excel(data_folder /'ihracat.xlsx', sheet_name='Sayfa1')
y=data['GDP']
l_y=var_lags(y,2) #lag1 and lag2

x1=data['HouseholdConsumption']
l_x1=var_lags(x1,2) #lag1 and lag2

x2=data['GoodServiceExport']
l_x2=var_lags(x2,2) #lag1 and lag2

x3=data['GoodServiceImport']
l_x3=var_lags(x3,2) #lag1 and lag2

df=pd.concat([y,x1,x2,x3,l_y,l_x1,l_x2,l_x3],axis=1)
df=df.dropna()



Y=df[['GDP']]
x=df[['HouseholdConsumption1', 'GoodServiceExport1', 'GoodServiceImport1']]
X=sm.add_constant(x)
test_size=5
train_size=len(Y)-test_size
test_sample=Y[train_size:len(Y)]
test_sample=test_sample.reset_index()
lf=len(Y)-train_size
matpredall=np.zeros((lf,1))
matrix = np.zeros((1,1)) # Pre-allocate matrix

testx=X[train_size:len(X)]
testy=Y[train_size:len(X)]
testx=testx.reset_index()
del testx['index']
testy=testy.reset_index()
del testy['index']

for j in range(lf):
    X_train=X[0+j:train_size+j]
    y_train=Y[0+j:train_size+j]
    X_test=testx[0+j:1+j]
    y_test=testy[0+j:1+j]
    m=0
    results = sm.OLS(endog=y_train, exog=X_train).fit()
    print(results.summary())
    y_pred_OLS = results.predict(X_test)
    matrix[:,m] = y_pred_OLS
    m=m+1
    print(j)   
    matpredall[j,0]=matrix
           
matytraintest=Y[train_size:len(Y)]  
matytraintest=np.array(matytraintest)
lenmatytraintest=len( matytraintest)
dfmatytraintest=pd.DataFrame(matytraintest)
dfmatpredict=pd.DataFrame(matpredall)
 
fark=dfmatytraintest.values- dfmatpredict.values
Mat_error=abs(fark) 
Mat_MAE=Mat_error.mean(0)
Mat_MAE=Mat_MAE.tolist()
  

Mat_errorrate=(Mat_error/dfmatytraintest.values)*100
Mat_MAPE=Mat_errorrate.mean(0)
Mat_MAPE=Mat_MAPE.tolist()
pseudo_predicted_price=pd.DataFrame(matpredall)
pseudo_predicted_price.columns=['Predicted_Prices']

#real out of sample prediction
#verinin son satır alınıyor
testx2=X[len(X)-1:len(X)]
real_pred = results.predict(testx2)
real_pred=real_pred.reset_index()
del real_pred['index']
real_pred.columns=['Real_Predicted_GDP']

