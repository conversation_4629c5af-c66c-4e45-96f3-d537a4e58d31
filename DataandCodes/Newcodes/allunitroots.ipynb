{"cells": [{"cell_type": "code", "execution_count": 3, "id": "2ef5b09a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Augmented <PERSON>-Fuller Results   \n", "=====================================\n", "Test Statistic                 -8.617\n", "P-value                         0.000\n", "Lags                                0\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -4.03 (1%), -3.44 (5%), -3.15 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.365\n", "Model:                            OLS   Adj. R-squared:                  0.356\n", "Method:                 Least Squares   F-statistic:                     37.14\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):           1.82e-13\n", "Time:                        11:46:46   Log-Likelihood:                -488.03\n", "No. Observations:                 132   AIC:                             982.1\n", "Df Residuals:                     129   BIC:                             990.7\n", "Df Model:                           2                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.7302      0.085     -8.617      0.000      -0.898      -0.563\n", "const         56.3498      6.689      8.425      0.000      43.116      69.584\n", "trend          0.2978      0.042      7.153      0.000       0.215       0.380\n", "==============================================================================\n", "Omnibus:                       19.821   <PERSON><PERSON><PERSON>-Watson:                   2.041\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):               34.342\n", "Skew:                          -0.694   Prob(JB):                     3.49e-08\n", "Kurtosis:                       5.078   Cond. No.                         998.\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -8.616991425672934\n", "ADF_critical values: {'1%': -4.02904396268122, '5%': -3.444288929202493, '10%': -3.1468728733617164}\n", "   Augmented <PERSON>-Fuller Results   \n", "=====================================\n", "Test Statistic                 -8.964\n", "P-value                         0.000\n", "Lags                               10\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -4.04 (1%), -3.45 (5%), -3.15 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.837\n", "Model:                            OLS   Adj. R-squared:                  0.819\n", "Method:                 Least Squares   F-statistic:                     46.26\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):           5.73e-37\n", "Time:                        11:46:46   Log-Likelihood:                -430.43\n", "No. Observations:                 121   AIC:                             886.9\n", "Df Residuals:                     108   BIC:                             923.2\n", "Df Model:                          12                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -7.3346      0.818     -8.964      0.000      -8.956      -5.713\n", "Diff.L1        5.5606      0.784      7.095      0.000       4.007       7.114\n", "Diff.L2        4.9449      0.731      6.762      0.000       3.495       6.394\n", "Diff.L3        4.3433      0.667      6.512      0.000       3.021       5.665\n", "Diff.L4        3.7035      0.599      6.185      0.000       2.517       4.890\n", "Diff.L5        3.1169      0.520      5.997      0.000       2.087       4.147\n", "Diff.L6        2.6660      0.428      6.225      0.000       1.817       3.515\n", "Diff.L7        2.1189      0.339      6.249      0.000       1.447       2.791\n", "Diff.L8        1.5457      0.254      6.080      0.000       1.042       2.050\n", "Diff.L9        1.0952      0.171      6.413      0.000       0.757       1.434\n", "Diff.L10       0.5462      0.089      6.141      0.000       0.370       0.723\n", "const          1.5437      1.660      0.930      0.355      -1.747       4.835\n", "trend          0.0301      0.024      1.272      0.206      -0.017       0.077\n", "==============================================================================\n", "Omnibus:                       46.395   <PERSON><PERSON><PERSON>-<PERSON>:                   1.670\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):              172.168\n", "Skew:                          -1.304   Prob(JB):                     4.11e-38\n", "Kurtosis:                       8.229   Cond. No.                         158.\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -8.964213209239828\n", "ADF_critical values: {'1%': -4.02904396268122, '5%': -3.444288929202493, '10%': -3.1468728733617164}\n", "   Augmented <PERSON>-Fuller Results   \n", "=====================================\n", "Test Statistic                 -2.280\n", "P-value                         0.445\n", "Lags                                2\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -4.03 (1%), -3.44 (5%), -3.15 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.115\n", "Model:                            OLS   Adj. R-squared:                  0.087\n", "Method:                 Least Squares   F-statistic:                     4.061\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):            0.00395\n", "Time:                        11:46:46   Log-Likelihood:                -853.75\n", "No. Observations:                 130   AIC:                             1718.\n", "Df Residuals:                     125   BIC:                             1732.\n", "Df Model:                           4                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.1122      0.049     -2.280      0.024      -0.210      -0.015\n", "Diff.L1        0.0688      0.098      0.701      0.485      -0.126       0.263\n", "Diff.L2       -0.2363      0.117     -2.022      0.045      -0.468      -0.005\n", "const        351.4206    152.502      2.304      0.023      49.600     653.241\n", "trend          3.5633      1.433      2.486      0.014       0.727       6.400\n", "==============================================================================\n", "Omnibus:                       58.829   <PERSON><PERSON><PERSON>-Watson:                   1.995\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):             1070.711\n", "Skew:                           0.966   Prob(JB):                    3.15e-233\n", "Kurtosis:                      16.926   Cond. No.                     5.00e+04\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large,  5e+04. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "test statistic -2.2797050995047354\n", "ADF_critical values: {'1%': -4.030152423759672, '5%': -3.444817634956759, '10%': -3.1471816659080565}\n", "   Augmented <PERSON>-Fuller Results   \n", "=====================================\n", "Test Statistic                 -7.823\n", "P-value                         0.000\n", "Lags                                2\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -4.03 (1%), -3.45 (5%), -3.15 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.497\n", "Model:                            OLS   Adj. R-squared:                  0.480\n", "Method:                 Least Squares   F-statistic:                     30.57\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):           1.06e-17\n", "Time:                        11:46:46   Log-Likelihood:                -849.13\n", "No. Observations:                 129   AIC:                             1708.\n", "Df Residuals:                     124   BIC:                             1723.\n", "Df Model:                           4                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -1.5438      0.197     -7.823      0.000      -1.934      -1.153\n", "Diff.L1        0.5215      0.158      3.301      0.001       0.209       0.834\n", "Diff.L2        0.1954      0.129      1.512      0.133      -0.060       0.451\n", "const         12.2370     31.606      0.387      0.699     -50.319      74.793\n", "trend          0.5154      0.436      1.183      0.239      -0.347       1.378\n", "==============================================================================\n", "Omnibus:                       41.450   <PERSON><PERSON><PERSON>-Watson:                   1.985\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):              741.382\n", "Skew:                           0.374   Prob(JB):                    1.03e-161\n", "Kurtosis:                      14.721   Cond. No.                         600.\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -7.823493775723805\n", "ADF_critical values: {'1%': -4.030152423759672, '5%': -3.444817634956759, '10%': -3.1471816659080565}\n", "   Augmented <PERSON>-Fuller Results   \n", "=====================================\n", "Test Statistic                 -2.317\n", "P-value                         0.166\n", "Lags                                1\n", "-------------------------------------\n", "\n", "Trend: Constant\n", "Critical Values: -3.48 (1%), -2.88 (5%), -2.58 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.308\n", "Model:                            OLS   Adj. R-squared:                  0.298\n", "Method:                 Least Squares   F-statistic:                     28.54\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):           5.62e-11\n", "Time:                        11:46:46   Log-Likelihood:                -165.44\n", "No. Observations:                 131   AIC:                             336.9\n", "Df Residuals:                     128   BIC:                             345.5\n", "Df Model:                           2                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.0352      0.015     -2.317      0.022      -0.065      -0.005\n", "Diff.L1        0.5460      0.074      7.368      0.000       0.399       0.693\n", "const          0.4458      0.197      2.265      0.025       0.056       0.835\n", "==============================================================================\n", "Omnibus:                       14.983   <PERSON><PERSON><PERSON>-<PERSON>:                   2.025\n", "Prob(Omnibus):                  0.001   Jarque-Bera (JB):               20.160\n", "Skew:                           0.634   Prob(JB):                     4.19e-05\n", "Kurtosis:                       4.444   Cond. No.                         34.0\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -2.3173468908542603\n", "ADF_critical values: {'1%': -3.481281802271349, '5%': -2.883867891664528, '10%': -2.5786771965503177}\n", "   Augmented <PERSON>-Fuller Results   \n", "=====================================\n", "Test Statistic                 -6.253\n", "P-value                         0.000\n", "Lags                                0\n", "-------------------------------------\n", "\n", "Trend: Constant\n", "Critical Values: -3.48 (1%), -2.88 (5%), -2.58 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                      y   R-squared:                       0.233\n", "Model:                            OLS   Adj. R-squared:                  0.227\n", "Method:                 Least Squares   F-statistic:                     39.10\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):           5.44e-09\n", "Time:                        11:46:46   Log-Likelihood:                -168.13\n", "No. Observations:                 131   AIC:                             340.3\n", "Df Residuals:                     129   BIC:                             346.0\n", "Df Model:                           1                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.4693      0.075     -6.253      0.000      -0.618      -0.321\n", "const          0.0248      0.077      0.322      0.748      -0.128       0.177\n", "==============================================================================\n", "Omnibus:                       12.100   Du<PERSON><PERSON>-Watson:                   1.981\n", "Prob(Omnibus):                  0.002   Jarque<PERSON><PERSON><PERSON> (JB):               17.043\n", "Skew:                           0.496   Prob(JB):                     0.000199\n", "Kurtosis:                       4.462   Cond. No.                         1.07\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_14423/2148448267.py:36: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:48: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:61: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:74: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:86: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:98: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["test statistic -6.253208484318464\n", "ADF_critical values: {'1%': -3.481281802271349, '5%': -2.883867891664528, '10%': -2.5786771965503177}\n", "    KPSS Stationarity Test Results   \n", "=====================================\n", "Test Statistic                  0.083\n", "P-value                         0.246\n", "Lags                                4\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: 0.22 (1%), 0.15 (5%), 0.12 (10%)\n", "Null Hypothesis: The process is weakly stationary.\n", "Alternative Hypothesis: The process contains a unit root.\n", "KPSS_critical values: {'1%': 0.2175, '5%': 0.1479, '10%': 0.1193}\n", "    KPSS Stationarity Test Results   \n", "=====================================\n", "Test Statistic                  0.108\n", "P-value                         0.131\n", "Lags                               23\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: 0.22 (1%), 0.15 (5%), 0.12 (10%)\n", "Null Hypothesis: The process is weakly stationary.\n", "Alternative Hypothesis: The process contains a unit root.\n", "KPSS_critical values: {'1%': 0.2175, '5%': 0.1479, '10%': 0.1193}\n", "    KPSS Stationarity Test Results   \n", "=====================================\n", "Test Statistic                  0.310\n", "P-value                         0.001\n", "Lags                                6\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: 0.22 (1%), 0.15 (5%), 0.12 (10%)\n", "Null Hypothesis: The process is weakly stationary.\n", "Alternative Hypothesis: The process contains a unit root.\n", "KPSS_critical values: {'1%': 0.2175, '5%': 0.1479, '10%': 0.1193}\n", "    KPSS Stationarity Test Results   \n", "=====================================\n", "Test Statistic                  0.040\n", "P-value                         0.728\n", "Lags                                6\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: 0.22 (1%), 0.15 (5%), 0.12 (10%)\n", "Null Hypothesis: The process is weakly stationary.\n", "Alternative Hypothesis: The process contains a unit root.\n", "KPSS_critical values: {'1%': 0.2175, '5%': 0.1479, '10%': 0.1193}\n", "    KPSS Stationarity Test Results   \n", "=====================================\n", "Test Statistic                  1.075\n", "P-value                         0.002\n", "Lags                                6\n", "-------------------------------------\n", "\n", "Trend: Constant\n", "Critical Values: 0.74 (1%), 0.46 (5%), 0.35 (10%)\n", "Null Hypothesis: The process is weakly stationary.\n", "Alternative Hypothesis: The process contains a unit root.\n", "KPSS_critical values: {'1%': 0.7428, '5%': 0.4614, '10%': 0.3475}\n", "    KPSS Stationarity Test Results   \n", "=====================================\n", "Test Statistic                  0.048\n", "P-value                         0.886\n", "Lags                                5\n", "-------------------------------------\n", "\n", "Trend: Constant\n", "Critical Values: 0.74 (1%), 0.46 (5%), 0.35 (10%)\n", "Null Hypothesis: The process is weakly stationary.\n", "Alternative Hypothesis: The process contains a unit root.\n", "KPSS_critical values: {'1%': 0.7428, '5%': 0.4614, '10%': 0.3475}\n", "     Phillips-<PERSON>ron Test (Z-tau)    \n", "=====================================\n", "Test Statistic                 -8.916\n", "P-value                         0.000\n", "Lags                               13\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -4.03 (1%), -3.44 (5%), -3.15 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "PhillipsPerron_critical values: {'1%': -4.02904396268122, '5%': -3.444288929202493, '10%': -3.1468728733617164}\n", "     Phillips-<PERSON>ron Test (Z-tau)    \n", "=====================================\n", "Test Statistic                -31.038\n", "P-value                         0.000\n", "Lags                               13\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -4.03 (1%), -3.44 (5%), -3.15 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "PhillipsPerron_critical values: {'1%': -4.02904396268122, '5%': -3.444288929202493, '10%': -3.1468728733617164}\n", "     Phillips-<PERSON>ron Test (Z-tau)    \n", "=====================================\n", "Test Statistic                 -2.928\n", "P-value                         0.153\n", "Lags                               13\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -4.03 (1%), -3.44 (5%), -3.15 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "PhillipsPerron_critical values: {'1%': -4.02904396268122, '5%': -3.444288929202493, '10%': -3.1468728733617164}\n", "     Phillips-<PERSON>ron Test (Z-tau)    \n", "=====================================\n", "Test Statistic                -10.430\n", "P-value                         0.000\n", "Lags                               13\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -4.03 (1%), -3.44 (5%), -3.15 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "PhillipsPerron_critical values: {'1%': -4.02904396268122, '5%': -3.444288929202493, '10%': -3.1468728733617164}\n", "     Phillips-<PERSON>ron Test (Z-tau)    \n", "=====================================\n", "Test Statistic                 -2.087\n", "P-value                         0.250\n", "Lags                               13\n", "-------------------------------------\n", "\n", "Trend: Constant\n", "Critical Values: -3.48 (1%), -2.88 (5%), -2.58 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "PhillipsPerron_critical values: {'1%': -3.4808880719210005, '5%': -2.8836966192225284, '10%': -2.5785857598714417}\n", "     Phillips-<PERSON>ron Test (Z-tau)    \n", "=====================================\n", "Test Statistic                 -6.248\n", "P-value                         0.000\n", "Lags                               13\n", "-------------------------------------\n", "\n", "Trend: Constant\n", "Critical Values: -3.48 (1%), -2.88 (5%), -2.58 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "PhillipsPerron_critical values: {'1%': -3.4808880719210005, '5%': -2.8836966192225284, '10%': -2.5785857598714417}\n", "      <PERSON><PERSON>-Fuller GLS Results      \n", "=====================================\n", "Test Statistic                 -8.360\n", "P-value                         0.000\n", "Lags                                0\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -3.57 (1%), -2.99 (5%), -2.70 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                                 OLS Regression Results                                \n", "=======================================================================================\n", "Dep. Variable:                      y   R-squared (uncentered):                   0.348\n", "Model:                            OLS   Adj. R-squared (uncentered):              0.343\n", "Method:                 Least Squares   F-statistic:                              69.89\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):                    8.01e-14\n", "Time:                        11:46:46   Log-Likelihood:                         -489.83\n", "No. Observations:                 132   AIC:                                      981.7\n", "Df Residuals:                     131   BIC:                                      984.5\n", "Df Model:                           1                                                  \n", "Covariance Type:            nonrobust                                                  \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.6968      0.083     -8.360      0.000      -0.862      -0.532\n", "==============================================================================\n", "Omnibus:                       25.243   <PERSON><PERSON><PERSON>-<PERSON>:                   2.057\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):               42.252\n", "Skew:                          -0.898   Prob(JB):                     6.69e-10\n", "Kurtosis:                       5.111   Cond. No.                         1.00\n", "==============================================================================\n", "\n", "Notes:\n", "[1] R² is computed without centering (uncentered) since the model does not contain a constant.\n", "[2] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -8.359871399853779\n", "DFGLS_critical values: {'1%': -3.5700735767156067, '5%': -2.9914293822849753, '10%': -2.6997524236169506}\n", "      <PERSON><PERSON>-Fuller GLS Results      \n", "=====================================\n", "Test Statistic                 -1.199\n", "P-value                         0.747\n", "Lags                               10\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -3.58 (1%), -3.00 (5%), -2.71 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                                 OLS Regression Results                                \n", "=======================================================================================\n", "Dep. Variable:                      y   R-squared (uncentered):                   0.719\n", "Model:                            OLS   Adj. R-squared (uncentered):              0.691\n", "Method:                 Least Squares   F-statistic:                              25.61\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):                    1.73e-25\n", "Time:                        11:46:46   Log-Likelihood:                         -463.41\n", "No. Observations:                 121   AIC:                                      948.8\n", "Df Residuals:                     110   BIC:                                      979.6\n", "Df Model:                          11                                                  \n", "Covariance Type:            nonrobust                                                  \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.3045      0.254     -1.199      0.233      -0.808       0.199\n", "Diff.L1       -1.1288      0.269     -4.201      0.000      -1.661      -0.596\n", "Diff.L2       -1.2079      0.294     -4.102      0.000      -1.791      -0.624\n", "Diff.L3       -1.1523      0.316     -3.646      0.000      -1.779      -0.526\n", "Diff.L4       -1.0928      0.331     -3.304      0.001      -1.748      -0.437\n", "Diff.L5       -0.8756      0.335     -2.610      0.010      -1.540      -0.211\n", "Diff.L6       -0.4363      0.320     -1.364      0.175      -1.070       0.198\n", "Diff.L7       -0.1765      0.284     -0.621      0.536      -0.739       0.386\n", "Diff.L8       -0.0221      0.237     -0.093      0.926      -0.492       0.448\n", "Diff.L9        0.1925      0.178      1.081      0.282      -0.161       0.546\n", "Diff.L10       0.1802      0.102      1.758      0.081      -0.023       0.383\n", "==============================================================================\n", "Omnibus:                        7.617   <PERSON><PERSON><PERSON>-Watson:                   1.739\n", "Prob(Omnibus):                  0.022   Jar<PERSON><PERSON><PERSON><PERSON> (JB):                9.286\n", "Skew:                          -0.366   Prob(JB):                      0.00963\n", "Kurtosis:                       4.142   Cond. No.                         23.4\n", "==============================================================================\n", "\n", "Notes:\n", "[1] R² is computed without centering (uncentered) since the model does not contain a constant.\n", "[2] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -1.1985279416209964\n", "DFGLS_critical values: {'1%': -3.5700735767156067, '5%': -2.9914293822849753, '10%': -2.6997524236169506}\n", "      <PERSON><PERSON>-Fuller GLS Results      \n", "=====================================\n", "Test Statistic                 -1.994\n", "P-value                         0.304\n", "Lags                                2\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -3.57 (1%), -2.99 (5%), -2.70 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                                 OLS Regression Results                                \n", "=======================================================================================\n", "Dep. Variable:                      y   R-squared (uncentered):                   0.098\n", "Model:                            OLS   Adj. R-squared (uncentered):              0.077\n", "Method:                 Least Squares   F-statistic:                              4.609\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):                     0.00427\n", "Time:                        11:46:46   Log-Likelihood:                         -854.98\n", "No. Observations:                 130   AIC:                                      1716.\n", "Df Residuals:                     127   BIC:                                      1725.\n", "Df Model:                           3                                                  \n", "Covariance Type:            nonrobust                                                  \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.0915      0.046     -1.994      0.048      -0.182      -0.001\n", "Diff.L1        0.0693      0.097      0.714      0.477      -0.123       0.261\n", "Diff.L2       -0.2283      0.114     -2.008      0.047      -0.453      -0.003\n", "==============================================================================\n", "Omnibus:                       64.986   <PERSON><PERSON><PERSON>-Watson:                   1.993\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):             1126.737\n", "Skew:                           1.173   Prob(JB):                    2.15e-245\n", "Kurtosis:                      17.231   Cond. No.                         3.11\n", "==============================================================================\n", "\n", "Notes:\n", "[1] R² is computed without centering (uncentered) since the model does not contain a constant.\n", "[2] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -1.9940758903946147\n", "DFGLS_critical values: {'1%': -3.572570811654538, '5%': -2.993589830055535, '10%': -2.7018262972325306}\n", "      <PERSON><PERSON>-Fuller GLS Results      \n", "=====================================\n", "Test Statistic                 -6.433\n", "P-value                         0.000\n", "Lags                                1\n", "-------------------------------------\n", "\n", "Trend: Constant and Linear Time Trend\n", "Critical Values: -3.57 (1%), -2.99 (5%), -2.70 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                                 OLS Regression Results                                \n", "=======================================================================================\n", "Dep. Variable:                      y   R-squared (uncentered):                   0.373\n", "Model:                            OLS   Adj. R-squared (uncentered):              0.363\n", "Method:                 Least Squares   F-statistic:                              38.02\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):                    1.10e-13\n", "Time:                        11:46:46   Log-Likelihood:                         -869.55\n", "No. Observations:                 130   AIC:                                      1743.\n", "Df Residuals:                     128   BIC:                                      1749.\n", "Df Model:                           2                                                  \n", "Covariance Type:            nonrobust                                                  \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.7926      0.123     -6.433      0.000      -1.036      -0.549\n", "Diff.L1        0.0153      0.097      0.158      0.875      -0.176       0.207\n", "==============================================================================\n", "Omnibus:                       43.816   <PERSON><PERSON><PERSON>-Watson:                   1.967\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):              923.519\n", "Skew:                          -0.371   Prob(JB):                    2.89e-201\n", "Kurtosis:                      16.036   Cond. No.                         2.43\n", "==============================================================================\n", "\n", "Notes:\n", "[1] R² is computed without centering (uncentered) since the model does not contain a constant.\n", "[2] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -6.432500228083545\n", "DFGLS_critical values: {'1%': -3.572570811654538, '5%': -2.993589830055535, '10%': -2.7018262972325306}\n", "      <PERSON><PERSON>-Fuller GLS Results      \n", "=====================================\n", "Test Statistic                 -1.658\n", "P-value                         0.095\n", "Lags                                1\n", "-------------------------------------\n", "\n", "Trend: Constant\n", "Critical Values: -2.71 (1%), -2.10 (5%), -1.78 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                                 OLS Regression Results                                \n", "=======================================================================================\n", "Dep. Variable:                      y   R-squared (uncentered):                   0.296\n", "Model:                            OLS   Adj. R-squared (uncentered):              0.285\n", "Method:                 Least Squares   F-statistic:                              27.16\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):                    1.43e-10\n", "Time:                        11:46:46   Log-Likelihood:                         -166.80\n", "No. Observations:                 131   AIC:                                      337.6\n", "Df Residuals:                     129   BIC:                                      343.4\n", "Df Model:                           2                                                  \n", "Covariance Type:            nonrobust                                                  \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.0204      0.012     -1.658      0.100      -0.045       0.004\n", "Diff.L1        0.5459      0.075      7.320      0.000       0.398       0.693\n", "==============================================================================\n", "Omnibus:                       13.831   <PERSON><PERSON><PERSON>-<PERSON>:                   2.012\n", "Prob(Omnibus):                  0.001   Jarque-Bera (JB):               18.881\n", "Skew:                           0.581   Prob(JB):                     7.94e-05\n", "Kurtosis:                       4.452   Cond. No.                         6.11\n", "==============================================================================\n", "\n", "Notes:\n", "[1] R² is computed without centering (uncentered) since the model does not contain a constant.\n", "[2] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_14423/2148448267.py:161: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:174: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:185: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:197: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:208: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n", "/tmp/ipykernel_14423/2148448267.py:221: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print('test statistic',(reg_res.tvalues[0]))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["test statistic -1.658456916927442\n", "DFGLS_critical values: {'1%': -2.71488905459291, '5%': -2.095301547530038, '10%': -1.780929064050392}\n", "      <PERSON><PERSON>-Fuller GLS Results      \n", "=====================================\n", "Test Statistic                 -6.154\n", "P-value                         0.000\n", "Lags                                0\n", "-------------------------------------\n", "\n", "Trend: Constant\n", "Critical Values: -2.71 (1%), -2.10 (5%), -1.78 (10%)\n", "Null Hypothesis: The process contains a unit root.\n", "Alternative Hypothesis: The process is weakly stationary.\n", "                                 OLS Regression Results                                \n", "=======================================================================================\n", "Dep. Variable:                      y   R-squared (uncentered):                   0.226\n", "Model:                            OLS   Adj. R-squared (uncentered):              0.220\n", "Method:                 Least Squares   F-statistic:                              37.87\n", "Date:                Wed, 09 Jul 2025   Prob (F-statistic):                    8.69e-09\n", "Time:                        11:46:46   Log-Likelihood:                         -168.73\n", "No. Observations:                 131   AIC:                                      339.5\n", "Df Residuals:                     130   BIC:                                      342.3\n", "Df Model:                           1                                                  \n", "Covariance Type:            nonrobust                                                  \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "Level.L1      -0.4540      0.074     -6.154      0.000      -0.600      -0.308\n", "==============================================================================\n", "Omnibus:                       11.736   <PERSON><PERSON><PERSON>-Watson:                   1.994\n", "Prob(Omnibus):                  0.003   Jar<PERSON><PERSON><PERSON><PERSON> (JB):               16.276\n", "Skew:                           0.488   Prob(JB):                     0.000292\n", "Kurtosis:                       4.425   Cond. No.                         1.00\n", "==============================================================================\n", "\n", "Notes:\n", "[1] R² is computed without centering (uncentered) since the model does not contain a constant.\n", "[2] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "test statistic -6.154207047677716\n", "DFGLS_critical values: {'1%': -2.71488905459291, '5%': -2.095301547530038, '10%': -1.780929064050392}\n"]}, {"data": {"image/png": "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*********************************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import warnings\n", "import pandas as pd\n", "from pathlib import Path\n", "import statsmodels.api as sm\n", "import  matplotlib.pylab as plt\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "from statsmodels.graphics.tsaplots import plot_pacf\n", "plt.style.use('fivethirtyeight')\n", "import arch\n", "from arch.unitroot import ADF\n", "from arch.unitroot import DFGLS\n", "from arch.unitroot import PhillipsPerron\n", "from arch.unitroot import KPSS\n", "from pathlib import Path\n", "from statsmodels.tsa.stattools import adfuller\n", "\n", "data_folder = Path(\"D:/TimeSeriesandForecasting/DataandCodes\")\n", "\n", "# data_folder = Path(\"C:/data/\")\n", "# warnings.filterwarnings(\"ignore\")\n", "md = pd.read_excel( '../Money.xlsx', sheet_name='Sayfa1')\n", "df=md\n", "ip=df[\"ip\"]\n", "Real_Money=df[\"Real_Money\"]\n", "interest_rate=df[\"interest_rate\"]\n", "\n", "#Augmented <PERSON><PERSON> Test for ip\n", "ip.plot()\n", "\n", "adf_ip = ADF(ip, trend='ct', max_lags=10, method='aic') \n", "print(adf_ip.summary().as_text())\n", "reg_res = adf_ip.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_ip.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "dif_ip=ip.diff()\n", "dif_ip=dif_ip.dropna()\n", "dif_adf_ip = ADF(dif_ip, trend='ct', max_lags=10, method='aic') \n", "print(dif_adf_ip.summary().as_text())\n", "reg_res = dif_adf_ip.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_ip.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "\n", "#Augmented <PERSON><PERSON> Test for Real_Money\n", "Real_Money.plot()\n", "adf_Real_Money = ADF(Real_Money, trend='ct', max_lags=10, method='aic') \n", "print(adf_Real_Money.summary().as_text())\n", "reg_res = adf_Real_Money.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_Real_Money.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "\n", "dif_Real_Money=Real_Money.diff()\n", "dif_Real_Money=dif_Real_Money.dropna()\n", "dif_adf_Real_Money = ADF(dif_Real_Money, trend='ct', max_lags=10, method='aic') \n", "print(dif_adf_Real_Money.summary().as_text())\n", "reg_res = dif_adf_Real_Money.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_Real_Money.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "#Augmented <PERSON><PERSON> Test for interest rate\n", "interest_rate.plot()\n", "adf_interest_rate = ADF(interest_rate, trend='c', max_lags=10, method='aic') \n", "print(adf_interest_rate.summary().as_text())\n", "reg_res = adf_interest_rate.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_interest_rate.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "dif_interest_rate=interest_rate.diff()\n", "dif_interest_rate=dif_interest_rate.dropna()\n", "dif_adf_interest_rate = ADF(dif_interest_rate, trend='c', max_lags=10, method='aic') \n", "print(dif_adf_interest_rate.summary().as_text())\n", "reg_res = dif_adf_interest_rate.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_interest_rate.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "#KPSS test for ip\n", "kpss_ip = KPSS(ip, trend='ct') \n", "print(kpss_ip.summary().as_text())\n", "print('KPSS_critical values:',kpss_ip._critical_values)\n", "\n", "dif_kpss_ip=KPSS(dif_ip, trend='ct') \n", "print(dif_kpss_ip.summary().as_text())\n", "print('KPSS_critical values:',kpss_ip._critical_values)\n", "\n", "#KPSS test for Real_Money\n", "kpss_Real_Money = KPSS(Real_Money, trend='ct') \n", "print(kpss_Real_Money.summary().as_text())\n", "print('KPSS_critical values:',kpss_Real_Money._critical_values)\n", "dif_kpss_Real_Money=KPSS(dif_Real_Money, trend='ct') \n", "print(dif_kpss_Real_Money.summary().as_text())\n", "print('KPSS_critical values:',kpss_Real_Money._critical_values)\n", "\n", "#KPSS test for interest_rate\n", "kpss_interest_rate = KPSS(interest_rate, trend='c') \n", "print(kpss_interest_rate.summary().as_text())\n", "print('KPSS_critical values:',kpss_interest_rate._critical_values)\n", "dif_kpss_interest_rate=KPSS(dif_interest_rate, trend='c') \n", "print(dif_kpss_interest_rate.summary().as_text())\n", "print('KPSS_critical values:',kpss_interest_rate._critical_values)\n", "\n", "#PHILLIPSPERRON test for ip\n", "PhillipsPerron_ip = PhillipsPerron (ip, trend='ct') \n", "print(PhillipsPerron_ip.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_ip._critical_values)\n", "\n", "dif_PhillipsPerron_ip= PhillipsPerron (dif_ip, trend='ct') \n", "print(dif_PhillipsPerron_ip.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_ip._critical_values)\n", "\n", "#PHILLIPSPERRON test for Real_Money\n", "PhillipsPerron_Real_Money = PhillipsPerron (Real_Money, trend='ct') \n", "print(PhillipsPerron_Real_Money.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_Real_Money._critical_values)\n", "\n", "dif_PhillipsPerron_Real_Money= PhillipsPerron (dif_Real_Money, trend='ct') \n", "print(dif_PhillipsPerron_Real_Money.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_Real_Money._critical_values)\n", "\n", "#PHİLLİPSPERRON test for interest_rate\n", "PhillipsPerron_interest_rate = PhillipsPerron (interest_rate, trend='c') \n", "print(PhillipsPerron_interest_rate.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_interest_rate._critical_values)\n", "\n", "dif_PhillipsPerron_interest_rate= PhillipsPerron (dif_interest_rate, trend='c') \n", "print(dif_PhillipsPerron_interest_rate.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_interest_rate._critical_values)\n", "\n", "#DFGLS Test for ip\n", "DFGLS_ip = DFGLS(ip, trend='ct', max_lags=10, method='aic') \n", "print(DFGLS_ip.summary())\n", "reg_res = DFGLS_ip.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_ip.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "\n", "dif_ip=ip.diff()\n", "dif_ip=dif_ip.dropna()\n", "dif_DFGLS_ip = DFGLS(dif_ip, trend='ct', max_lags=10, method='aic') \n", "print(dif_DFGLS_ip.summary().as_text())\n", "reg_res = dif_DFGLS_ip.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_ip.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "#DFGLS Test for Real_Money\n", "DFGLS_Real_Money = DFGLS(Real_Money, trend='ct', max_lags=10, method='aic') \n", "print(DFGLS_Real_Money.summary().as_text())\n", "reg_res = DFGLS_Real_Money.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_Real_Money.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "dif_Real_Money=Real_Money.diff()\n", "dif_Real_Money=dif_Real_Money.dropna()\n", "dif_DFGLS_Real_Money = DFGLS(dif_Real_Money, trend='ct', max_lags=10, method='aic') \n", "print(dif_DFGLS_Real_Money.summary().as_text())\n", "reg_res = dif_DFGLS_Real_Money.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_Real_Money.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "#DFGLS Test for interest_rate\n", "DFGLS_interest_rate = DFGLS(interest_rate, trend='c', max_lags=10, method='aic') \n", "print(DFGLS_interest_rate.summary().as_text())\n", "reg_res = DFGLS_interest_rate.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_interest_rate.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "\n", "dif_interest_rate=interest_rate.diff()\n", "dif_interest_rate=dif_interest_rate.dropna()\n", "dif_DFGLS_interest_rate = DFGLS(dif_interest_rate, trend='c', max_lags=10, method='aic') #default\n", "print(dif_DFGLS_interest_rate.summary().as_text())\n", "reg_res = dif_DFGLS_interest_rate.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_interest_rate.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "62310d5c-3edb-4f1b-9562-19560f06f519", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}