{"cells": [{"cell_type": "code", "execution_count": null, "id": "2ef5b09a", "metadata": {}, "outputs": [], "source": ["import warnings\n", "import pandas as pd\n", "from pathlib import Path\n", "import statsmodels.api as sm\n", "import  matplotlib.pylab as plt\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "from statsmodels.graphics.tsaplots import plot_pacf\n", "plt.style.use('fivethirtyeight')\n", "import arch\n", "from arch.unitroot import ADF\n", "from arch.unitroot import DFGLS\n", "from arch.unitroot import PhillipsPerron\n", "from arch.unitroot import KPSS\n", "from pathlib import Path\n", "from statsmodels.tsa.stattools import adfuller\n", "\n", "data_folder = Path(\"D:/TimeSeriesandForecasting/DataandCodes\")\n", "\n", "# data_folder = Path(\"C:/data/\")\n", "# warnings.filterwarnings(\"ignore\")\n", "md = pd.read_excel(data_folder /'Money.xlsx', sheet_name='Sayfa1')\n", "df=md\n", "ip=df[\"ip\"]\n", "Real_Money=df[\"Real_Money\"]\n", "interest_rate=df[\"interest_rate\"]\n", "\n", "#Augmented <PERSON><PERSON> Test for ip\n", "ip.plot()\n", "\n", "adf_ip = ADF(ip, trend='ct', max_lags=10, method='aic') \n", "print(adf_ip.summary().as_text())\n", "reg_res = adf_ip.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_ip.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "dif_ip=ip.diff()\n", "dif_ip=dif_ip.dropna()\n", "dif_adf_ip = ADF(dif_ip, trend='ct', max_lags=10, method='aic') \n", "print(dif_adf_ip.summary().as_text())\n", "reg_res = dif_adf_ip.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_ip.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "\n", "#Augmented <PERSON><PERSON> Test for Real_Money\n", "Real_Money.plot()\n", "adf_Real_Money = ADF(Real_Money, trend='ct', max_lags=10, method='aic') \n", "print(adf_Real_Money.summary().as_text())\n", "reg_res = adf_Real_Money.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_Real_Money.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "\n", "dif_Real_Money=Real_Money.diff()\n", "dif_Real_Money=dif_Real_Money.dropna()\n", "dif_adf_Real_Money = ADF(dif_Real_Money, trend='ct', max_lags=10, method='aic') \n", "print(dif_adf_Real_Money.summary().as_text())\n", "reg_res = dif_adf_Real_Money.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_Real_Money.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "#Augmented <PERSON><PERSON> Test for interest rate\n", "interest_rate.plot()\n", "adf_interest_rate = ADF(interest_rate, trend='c', max_lags=10, method='aic') \n", "print(adf_interest_rate.summary().as_text())\n", "reg_res = adf_interest_rate.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_interest_rate.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "dif_interest_rate=interest_rate.diff()\n", "dif_interest_rate=dif_interest_rate.dropna()\n", "dif_adf_interest_rate = ADF(dif_interest_rate, trend='c', max_lags=10, method='aic') \n", "print(dif_adf_interest_rate.summary().as_text())\n", "reg_res = dif_adf_interest_rate.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "adf_cv=adf_interest_rate.critical_values\n", "print('ADF_critical values:',adf_cv)\n", "\n", "#KPSS test for ip\n", "kpss_ip = KPSS(ip, trend='ct') \n", "print(kpss_ip.summary().as_text())\n", "print('KPSS_critical values:',kpss_ip._critical_values)\n", "\n", "dif_kpss_ip=KPSS(dif_ip, trend='ct') \n", "print(dif_kpss_ip.summary().as_text())\n", "print('KPSS_critical values:',kpss_ip._critical_values)\n", "\n", "#KPSS test for Real_Money\n", "kpss_Real_Money = KPSS(Real_Money, trend='ct') \n", "print(kpss_Real_Money.summary().as_text())\n", "print('KPSS_critical values:',kpss_Real_Money._critical_values)\n", "dif_kpss_Real_Money=KPSS(dif_Real_Money, trend='ct') \n", "print(dif_kpss_Real_Money.summary().as_text())\n", "print('KPSS_critical values:',kpss_Real_Money._critical_values)\n", "\n", "#KPSS test for interest_rate\n", "kpss_interest_rate = KPSS(interest_rate, trend='c') \n", "print(kpss_interest_rate.summary().as_text())\n", "print('KPSS_critical values:',kpss_interest_rate._critical_values)\n", "dif_kpss_interest_rate=KPSS(dif_interest_rate, trend='c') \n", "print(dif_kpss_interest_rate.summary().as_text())\n", "print('KPSS_critical values:',kpss_interest_rate._critical_values)\n", "\n", "#PHILLIPSPERRON test for ip\n", "PhillipsPerron_ip = PhillipsPerron (ip, trend='ct') \n", "print(PhillipsPerron_ip.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_ip._critical_values)\n", "\n", "dif_PhillipsPerron_ip= PhillipsPerron (dif_ip, trend='ct') \n", "print(dif_PhillipsPerron_ip.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_ip._critical_values)\n", "\n", "#PHILLIPSPERRON test for Real_Money\n", "PhillipsPerron_Real_Money = PhillipsPerron (Real_Money, trend='ct') \n", "print(PhillipsPerron_Real_Money.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_Real_Money._critical_values)\n", "\n", "dif_PhillipsPerron_Real_Money= PhillipsPerron (dif_Real_Money, trend='ct') \n", "print(dif_PhillipsPerron_Real_Money.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_Real_Money._critical_values)\n", "\n", "#PHİLLİPSPERRON test for interest_rate\n", "PhillipsPerron_interest_rate = PhillipsPerron (interest_rate, trend='c') \n", "print(PhillipsPerron_interest_rate.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_interest_rate._critical_values)\n", "\n", "dif_PhillipsPerron_interest_rate= PhillipsPerron (dif_interest_rate, trend='c') \n", "print(dif_PhillipsPerron_interest_rate.summary().as_text())\n", "print('PhillipsPerron_critical values:',PhillipsPerron_interest_rate._critical_values)\n", "\n", "#DFGLS Test for ip\n", "DFGLS_ip = DFGLS(ip, trend='ct', max_lags=10, method='aic') \n", "print(DFGLS_ip.summary())\n", "reg_res = DFGLS_ip.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_ip.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "\n", "dif_ip=ip.diff()\n", "dif_ip=dif_ip.dropna()\n", "dif_DFGLS_ip = DFGLS(dif_ip, trend='ct', max_lags=10, method='aic') \n", "print(dif_DFGLS_ip.summary().as_text())\n", "reg_res = dif_DFGLS_ip.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_ip.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "#DFGLS Test for Real_Money\n", "DFGLS_Real_Money = DFGLS(Real_Money, trend='ct', max_lags=10, method='aic') \n", "print(DFGLS_Real_Money.summary().as_text())\n", "reg_res = DFGLS_Real_Money.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_Real_Money.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "dif_Real_Money=Real_Money.diff()\n", "dif_Real_Money=dif_Real_Money.dropna()\n", "dif_DFGLS_Real_Money = DFGLS(dif_Real_Money, trend='ct', max_lags=10, method='aic') \n", "print(dif_DFGLS_Real_Money.summary().as_text())\n", "reg_res = dif_DFGLS_Real_Money.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_Real_Money.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "#DFGLS Test for interest_rate\n", "DFGLS_interest_rate = DFGLS(interest_rate, trend='c', max_lags=10, method='aic') \n", "print(DFGLS_interest_rate.summary().as_text())\n", "reg_res = DFGLS_interest_rate.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_interest_rate.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n", "\n", "dif_interest_rate=interest_rate.diff()\n", "dif_interest_rate=dif_interest_rate.dropna()\n", "dif_DFGLS_interest_rate = DFGLS(dif_interest_rate, trend='c', max_lags=10, method='aic') #default\n", "print(dif_DFGLS_interest_rate.summary().as_text())\n", "reg_res = dif_DFGLS_interest_rate.regression\n", "residuals=reg_res.resid\n", "print(reg_res.summary().as_text())\n", "plot_acf(residuals,lags=60)\n", "print('test statistic',(reg_res.tvalues[0]))\n", "DFGLS_cv=DFGLS_interest_rate.critical_values\n", "print('DFGLS_critical values:',DFGLS_cv)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}