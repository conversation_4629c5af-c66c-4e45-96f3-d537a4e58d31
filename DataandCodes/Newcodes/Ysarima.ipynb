{"cells": [{"cell_type": "code", "execution_count": null, "id": "8ae7d48d", "metadata": {}, "outputs": [], "source": ["import warnings\n", "import pandas as pd\n", "#from pandas import ExcelWriter\n", "#from pandas import ExcelFile\n", "import numpy as np\n", "import statsmodels.api as sm\n", "import  matplotlib.pylab as plt\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "from statsmodels.graphics.tsaplots import plot_pacf\n", "plt.style.use('fivethirtyeight')\n", "from pathlib import Path\n", "data_folder = Path(\"D:/TimeSeriesandForecasting/DataandCodes\")\n", "\n", "# data_folder = Path(\"C:/data/\")\n", "# warnings.filterwarnings(\"ignore\")\n", "md = pd.read_excel(data_folder /'Money.xlsx', sheet_name='Sayfa1')\n", "\n", "df= md.set_index('date')\n", "df.index\n", "ip=df['ip']\n", "\n", "\n", "#difference\n", "dif_ip=ip.diff()\n", "dif_ip.plot()\n", "plot_acf(ip,lags=60)\n", "plot_pacf(ip,lags=20)\n", "\n", "\n", "\n", "\n", "ip=md['ip']\n", "data2=ip\n", "test_size=12\n", "training_size=len(data2)-test_size\n", "test_sample=data2[training_size:len(data2)]\n", "test_sample=test_sample.reset_index()\n", "del test_sample['index']\n", "training_sample=data2[0:training_size]\n", "\n", "#additive seasonality\n", "\n", "\n", "ar = 1          # this is the maximum degree specification\n", "ma = (1,0,0,0,0,0,0,0,0,0,0,1)  # this is the lag polynomial specification\n", "\n", "mod=sm.tsa.statespace.SARIMAX(training_sample,trend='n',order=(ar,1,ma),enforce_stationarity=True, \n", "                                         enforce_invertibility=True)\n", "results=mod.fit(disp=False)\n", "print(results.summary())\n", "\n", "print(results.test_serial_correlation(method='ljungbox', lags=None))\n", "#results.plot_diagnostics()\n", "residuals=results.resid\n", "residuals=residuals.iloc[1:-1]\n", "plot_acf(residuals,lags=10)\n", "\n", "\n", "\n", "#in sample prediction\n", "pred=results.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "pred_pseudo1=pred.predicted_mean\n", "pred_pseudo1=pred_pseudo1.reset_index()\n", "del pred_pseudo1['index']\n", "pred_pseudo1.columns = ['predicted']\n", "ypredict1=pred_pseudo1.values\n", "yactual=test_sample.values\n", "mae1=abs(yactual-ypredict1).mean()\n", "mape1=100*(abs(yactual-ypredict1)/yactual).mean()\n", "\n", "\n", "\n", "#out of sample prediction\n", "# Getting 12 quarters for forecasts\n", "SARIMAX_forecast = round(results.forecast(steps =12 ), 2)\n", "# Creating an index from 2022.2 to 2023.1 and a SARIMAX_forecast dataframe:\n", "idx = pd.date_range('2022.1', '2023.1', freq='    M')\n", "SARIMAX_forecast = pd.DataFrame(list(zip(list(idx),list(SARIMAX_forecast))), \n", "                                columns=['Date','ForecastValue']).set_index('Date')\n", "\n", "\n", "\n", "\n", "\n", "#mutliplicative seasonality\n", "# arma(1,0) with seasonal AR\n", "\n", "mod2=sm.tsa.statespace.SARIMAX(training_sample,trend='n',order=(1,1,0),seasonal_order=(1,1,0,12),enforce_stationarity=True, \n", "                                         enforce_invertibility=True)\n", "\n", "\n", "results2=mod2.fit(disp=False)\n", "print(results2.summary())\n", "\n", "print(results2.test_serial_correlation(method='ljungbox', lags=None))\n", "#results.plot_diagnostics()\n", "residuals2=results2.resid\n", "residuals2=residuals2.iloc[1:-1]\n", "plot_acf(residuals2,lags=10)\n", "\n", "\n", "\n", "#in sample prediction\n", "pred2=results2.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "pred_pseudo2=pred2.predicted_mean\n", "pred_pseudo2=pred_pseudo2.reset_index()\n", "del pred_pseudo2['index']\n", "pred_pseudo2.columns = ['predicted']\n", "ypredict2=pred_pseudo2.values\n", "yactual=test_sample.values\n", "mae2=abs(yactual-ypredict2).mean()\n", "mape2=100*(abs(yactual-ypredict2)/yactual).mean()\n", "\n", "#out of sample prediction\n", "# Getting 12 quarters for forecasts\n", "SARIMAX_forecast2 = round(results2.forecast(steps =12 ), 2)\n", "\n", "SARIMAX_forecast2 = pd.DataFrame(list(zip(list(idx),list(SARIMAX_forecast2))), \n", "                                columns=['Date','ForecastValue']).set_index('Date')\n", "\n", "print(mape1,mape2)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}