# -*- coding: utf-8 -*-
"""
Created on Thu Feb 28 13:51:19 2019

@author: pc
"""

#https://medium.com/mlreview/understanding-lstm-and-its-diagrams-37e2f46f1714
import pandas as pd
import warnings
import numpy as np
from math import sqrt
from numpy import concatenate
from matplotlib import pyplot
from pandas import read_csv
from pandas import DataFrame
from pandas import concat
from sklearn.preprocessing import MinMaxScaler
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import mean_squared_error
from keras.models import Sequential
from keras.layers import Dense
from keras.layers import LSTM
from pandas import read_csv
from datetime import datetime
from pathlib import Path
from pandas import read_csv
from matplotlib import pyplot

# load data

# save to file

data_folder = Path("D:/TimeSeriesandForecasting/DataandCodes")
data = pd.read_excel(data_folder /'ihracat.xlsx', sheet_name='Sayfa1')
data=data[['GDP','HouseholdConsumption','GoodServiceExport','GoodServiceImport']]

df2=data
dataval=df2.values
groups = [0, 1, 2,3]
i = 1
# plot each column
pyplot.figure()

for group in groups:
	pyplot.subplot(len(groups), 1, i)
	pyplot.plot(dataval[:, group])
	pyplot.title(data.columns[group], y=0.5, loc='right')
	i += 1
pyplot.show()




vardata=df2.diff().dropna()

# load dataset
values = vardata.values




# convert series to supervised learning
def series_to_supervised(vardata, n_in=1, n_out=1, dropnan=True):
	n_vars = 1 if type(vardata) is list else vardata.shape[1]
	df = DataFrame(vardata)
	cols, names = list(), list()
	# input sequence (t-n, ... t-1)
	for i in range(n_in, 0, -1):
		cols.append(df.shift(i))
		names += [('var%d(t-%d)' % (j+1, i)) for j in range(n_vars)]
	# forecast sequence (t, t+1, ... t+n)
	for i in range(0, n_out):
		cols.append(df.shift(-i))
		if i == 0:
			names += [('var%d(t)' % (j+1)) for j in range(n_vars)]
		else:
			names += [('var%d(t+%d)' % (j+1, i)) for j in range(n_vars)]
	# put it all together
	agg = concat(cols, axis=1)
	agg.columns = names
	# drop rows with NaN values
	if dropnan:
		agg.dropna(inplace=True)
	return agg
 


# integer encode direction
#encoder = LabelEncoder()
#values[:,4] = encoder.fit_transform(values[:,4])
# ensure all data is float
#values = values.astype('float32')
# normalize features
scaler = MinMaxScaler(feature_range=(0, 1))
scaled = scaler.fit_transform(values)
# frame as supervised learning
reframed = series_to_supervised(scaled, 1, 1)

print(reframed.head())

# split into train and test sets
values = reframed.values
# drop columns we don't want to predict
reframed.drop(reframed.columns[[5,6,7]], axis=1, inplace=True)
values = reframed.values
n_obs=len(values)
h=5 #forecast horizon
train_sample=(n_obs-h)
train=values[0:train_sample,:]
test = values[train_sample:, :]


# split into input and outputs
train_X, train_y = train[:, :-1], train[:, -1]
test_X, test_y = test[:, :-1], test[:, -1]
# reshape input to be 3D [samples, timesteps, features]
train_X = train_X.reshape((train_X.shape[0], 1, train_X.shape[1]))
test_X = test_X.reshape((test_X.shape[0], 1, test_X.shape[1]))
print(train_X.shape, train_y.shape, test_X.shape, test_y.shape)
 
# design network
model = Sequential()
model.add(LSTM(50, input_shape=(train_X.shape[1], train_X.shape[2])))
model.add(Dense(1))
model.compile(loss='mae', optimizer='adam')
# fit network
history = model.fit(train_X, train_y, epochs=50, batch_size=100, validation_data=(test_X, test_y), verbose=2, shuffle=False)
# plot history
pyplot.plot(history.history['loss'], label='train')
pyplot.plot(history.history['val_loss'], label='test')
pyplot.legend()
pyplot.show()
 
# make a prediction
yhat = model.predict(test_X)
#reducing to 2D
test_X = test_X.reshape((test_X.shape[0], test_X.shape[2]))
# invert scaling for forecast
inv_yhat = concatenate((yhat, test_X[:, 1:]), axis=1)
inv_yhat = scaler.inverse_transform(inv_yhat)
inv_yhat = inv_yhat[:,0]


# invert scaling for actual
test_y = test_y.reshape((len(test_y), 1))
inv_y = concatenate((test_y, test_X[:, 1:]), axis=1)
inv_y = scaler.inverse_transform(inv_y)
inv_y = inv_y[:,0]


y_GDPF=inv_yhat


size1=len(data)
yGDPF=pd.DataFrame(y_GDPF)

yGDPF.rename(columns={0:'GDP'}, inplace=True)

basedate=data.iloc[size1-h-1:size1-h,:]
GDP_basedate=basedate['GDP']
forecast2=yGDPF[['GDP' ]]
GDP_basedate=GDP_basedate.reset_index()
# del GDP_basedate['date']

merge2=pd.concat([GDP_basedate,forecast2],axis=0,ignore_index=True)
GDP_forecastlevels=merge2.cumsum()
GDP_forecastlevels=GDP_forecastlevels.iloc[1:len(GDP_forecastlevels),:]
GDP_forecastlevels=GDP_forecastlevels.reset_index()
del GDP_forecastlevels['index']
test_level=data.iloc[size1-h:size1,:]
GDP_test=test_level['GDP']

# calculate RMSE
GDP_test=GDP_test.reset_index()
del GDP_test['index']
del GDP_forecastlevels['level_0']
rmse = sqrt(mean_squared_error(GDP_test, GDP_forecastlevels))
print('RMSE: %.3f' % rmse)

mape=100*(abs(GDP_test-GDP_forecastlevels)/GDP_test).mean()
print('MAPE: %.3f' % mape)

#out of sample real prediction
test_r1=test_X[-1,0:]
test_r1 = test_r1.reshape(1,1, test_r1.shape[0])

real_prd_GDP=model.predict(test_r1)
test_r1=test_r1.reshape(1,4)
inv_real_prd_GDP = concatenate((real_prd_GDP, test_r1[:, 1:]), axis=1)
inv_real_prd_GDP = scaler.inverse_transform(inv_real_prd_GDP)
inv_real_prd_GDP = inv_real_prd_GDP[:,0]

pred_real_GDP=pd.DataFrame(inv_real_prd_GDP)
pred_real_GDP.rename(columns={0:'GDP'}, inplace=True)
#conversion of oopr to level

baseoopr=data[-1:]
GDP_baseoopr=baseoopr['GDP']
GDP_baseoopr=GDP_baseoopr.reset_index()


merge3=pd.concat([GDP_baseoopr,pred_real_GDP ],axis=0,ignore_index=True)
GDP_ooprlevels=merge3.cumsum()
GDP_ooprlevels=GDP_ooprlevels.iloc[1:len(GDP_ooprlevels),:]
GDP_ooprlevels=GDP_ooprlevels.reset_index()
del GDP_ooprlevels['index']
del GDP_ooprlevels['level_0']
##############################################
#LSTM for 2nd Equation
values = vardata.values
scaler = MinMaxScaler(feature_range=(0, 1))
scaled = scaler.fit_transform(values)
# frame as supervised learning
reframed = series_to_supervised(scaled, 1, 1)

print(reframed.head())

# split into train and test sets
values = reframed.values
# drop columns we don't want to predict 
#to change for each equation
reframed.drop(reframed.columns[[4,6,7]], axis=1, inplace=True)
values = reframed.values
train_sample=(n_obs-h)
train=values[0:train_sample,:]
test = values[train_sample:, :]


# split into input and outputs
train_X, train_y = train[:, :-1], train[:, -1]
test_X, test_y = test[:, :-1], test[:, -1]
# reshape input to be 3D [samples, timesteps, features]
train_X = train_X.reshape((train_X.shape[0], 1, train_X.shape[1]))
test_X = test_X.reshape((test_X.shape[0], 1, test_X.shape[1]))
print(train_X.shape, train_y.shape, test_X.shape, test_y.shape)
# design network
model2 = Sequential()
model2.add(LSTM(50, input_shape=(train_X.shape[1], train_X.shape[2])))
model2.add(Dense(1))
model2.compile(loss='mae', optimizer='adam')
# fit network
history = model2.fit(train_X, train_y, epochs=50, batch_size=100, validation_data=(test_X, test_y), verbose=2, shuffle=False)
# plot history
pyplot.plot(history.history['loss'], label='train')
pyplot.plot(history.history['val_loss'], label='test')
pyplot.legend()
pyplot.show()
# make a prediction
yhat = model2.predict(test_X)
#reducing to 2D
test_X = test_X.reshape((test_X.shape[0], test_X.shape[2]))
# invert scaling for forecast
inv_yhat = concatenate((yhat, test_X[:, 1:]), axis=1)
inv_yhat = scaler.inverse_transform(inv_yhat)
inv_yhat = inv_yhat[:,0]


# invert scaling for actual
test_y = test_y.reshape((len(test_y), 1))
inv_y = concatenate((test_y, test_X[:, 1:]), axis=1)
inv_y = scaler.inverse_transform(inv_y)
inv_y = inv_y[:,0]


y_HouseholdConsumptionF=inv_yhat


size1=len(data)
yHouseholdConsumptionF=pd.DataFrame(y_HouseholdConsumptionF)

yHouseholdConsumptionF.rename(columns={0:'HouseholdConsumption'}, inplace=True)

basedate=data.iloc[size1-h-1:size1-h,:]
HouseholdConsumption_basedate=basedate['HouseholdConsumption']
forecast2=yHouseholdConsumptionF[['HouseholdConsumption' ]]
HouseholdConsumption_basedate=HouseholdConsumption_basedate.reset_index()


merge2=pd.concat([HouseholdConsumption_basedate,forecast2],axis=0,ignore_index=True)
HouseholdConsumption_forecastlevels=merge2.cumsum()
HouseholdConsumption_forecastlevels=HouseholdConsumption_forecastlevels.iloc[1:len(HouseholdConsumption_forecastlevels),:]
HouseholdConsumption_forecastlevels=HouseholdConsumption_forecastlevels.reset_index()
del HouseholdConsumption_forecastlevels['index']
test_level=data.iloc[size1-h:size1,:]
HouseholdConsumption_test=test_level['HouseholdConsumption']

# calculate RMSE
HouseholdConsumption_test=HouseholdConsumption_test.reset_index()
del HouseholdConsumption_test['index']
del HouseholdConsumption_forecastlevels['level_0']
rmse = sqrt(mean_squared_error(HouseholdConsumption_test, HouseholdConsumption_forecastlevels))
print('RMSE: %.3f' % rmse)
mape=100*(abs(HouseholdConsumption_test-HouseholdConsumption_forecastlevels)/HouseholdConsumption_test).mean()
print('MAPE: %.3f' % mape)

#out of sample real prediction
test_r1=test_X[-1,0:]
test_r1 = test_r1.reshape(1,1, test_r1.shape[0])

real_prd_HouseholdConsumption=model.predict(test_r1)
test_r1=test_r1.reshape(1,4)
inv_real_prd_HouseholdConsumption= concatenate((real_prd_HouseholdConsumption, test_r1[:, 1:]), axis=1)
inv_real_prd_HouseholdConsumption = scaler.inverse_transform(inv_real_prd_HouseholdConsumption)
inv_real_prd_HouseholdConsumption = inv_real_prd_HouseholdConsumption[:,0]

pred_real_HouseholdConsumption=pd.DataFrame(inv_real_prd_HouseholdConsumption)
pred_real_HouseholdConsumption.rename(columns={0:'HouseholdConsumption'}, inplace=True)
#conversion of oopr to level

baseoopr=data[-1:]
HouseholdConsumption_baseoopr=baseoopr['HouseholdConsumption']
HouseholdConsumption_baseoopr=HouseholdConsumption_baseoopr.reset_index()


merge3=pd.concat([HouseholdConsumption_baseoopr,pred_real_HouseholdConsumption],axis=0,ignore_index=True)
HouseholdConsumption_ooprlevels=merge3.cumsum()
HouseholdConsumption_ooprlevels=HouseholdConsumption_ooprlevels.iloc[1:len(HouseholdConsumption_ooprlevels),:]
HouseholdConsumption_ooprlevels=HouseholdConsumption_ooprlevels.reset_index()
del HouseholdConsumption_ooprlevels['index']



##############################################
#LSTM for 3nd Equation
values = vardata.values
scaler = MinMaxScaler(feature_range=(0, 1))
scaled = scaler.fit_transform(values)
# frame as supervised learning
reframed = series_to_supervised(scaled, 1, 1)

print(reframed.head())

# split into train and test sets
values = reframed.values
# drop columns we don't want to predict 
#to change for each equation
reframed.drop(reframed.columns[[4,5,7]], axis=1, inplace=True)


values = reframed.values
values = reframed.values
train_sample=(n_obs-h)
train=values[0:train_sample,:]
test = values[train_sample:, :]


# split into input and outputs
train_X, train_y = train[:, :-1], train[:, -1]
test_X, test_y = test[:, :-1], test[:, -1]
# reshape input to be 3D [samples, timesteps, features]
train_X = train_X.reshape((train_X.shape[0], 1, train_X.shape[1]))
test_X = test_X.reshape((test_X.shape[0], 1, test_X.shape[1]))
print(train_X.shape, train_y.shape, test_X.shape, test_y.shape)
# design network
model3 = Sequential()
model3.add(LSTM(50, input_shape=(train_X.shape[1], train_X.shape[2])))
model3.add(Dense(1))
model3.compile(loss='mae', optimizer='adam')
# fit network
history = model3.fit(train_X, train_y, epochs=50, batch_size=100, validation_data=(test_X, test_y), verbose=2, shuffle=False)
# plot history
pyplot.plot(history.history['loss'], label='train')
pyplot.plot(history.history['val_loss'], label='test')
pyplot.legend()
pyplot.show()
# make a prediction
yhat = model3.predict(test_X)
#reducing to 2D
test_X = test_X.reshape((test_X.shape[0], test_X.shape[2]))
# invert scaling for forecast
inv_yhat = concatenate((yhat, test_X[:, 1:]), axis=1)
inv_yhat = scaler.inverse_transform(inv_yhat)
inv_yhat = inv_yhat[:,0]


# invert scaling for actual
test_y = test_y.reshape((len(test_y), 1))
inv_y = concatenate((test_y, test_X[:, 1:]), axis=1)
inv_y = scaler.inverse_transform(inv_y)
inv_y = inv_y[:,0]


y_GoodServiceExportF=inv_yhat


size1=len(data)
yGoodServiceExportF=pd.DataFrame(y_GoodServiceExportF)

yGoodServiceExportF.rename(columns={0:'GoodServiceExport'}, inplace=True)

basedate=data.iloc[size1-h-1:size1-h,:]
GoodServiceExport_basedate=basedate['GoodServiceExport']
forecast2=yGoodServiceExportF[['GoodServiceExport' ]]
GoodServiceExport_basedate=GoodServiceExport_basedate.reset_index()


merge3=pd.concat([GoodServiceExport_basedate,forecast2],axis=0,ignore_index=True)
GoodServiceExport_forecastlevels=merge3.cumsum()
GoodServiceExport_forecastlevels=GoodServiceExport_forecastlevels.iloc[1:len(GoodServiceExport_forecastlevels),:]
GoodServiceExport_forecastlevels=GoodServiceExport_forecastlevels.reset_index()
del GoodServiceExport_forecastlevels['index']
test_level=data.iloc[size1-h:size1,:]
GoodServiceExport_test=test_level['GoodServiceExport']

# calculate RMSE
GoodServiceExport_test=GoodServiceExport_test.reset_index()
del GoodServiceExport_test['index']
del GoodServiceExport_forecastlevels['level_0']
rmse = sqrt(mean_squared_error(GoodServiceExport_test, GoodServiceExport_forecastlevels))
print('RMSE: %.3f' % rmse)
mape=100*(abs(GoodServiceExport_test-GoodServiceExport_forecastlevels)/GoodServiceExport_test).mean()
print('MAPE: %.3f' % mape)

#out of sample real prediction
test_r1=test_X[-1,0:]
test_r1 = test_r1.reshape(1,1, test_r1.shape[0])

real_prd_GoodServiceExport=model3.predict(test_r1)
test_r1=test_r1.reshape(1,4)
inv_real_prd_GoodServiceExport = concatenate((real_prd_GoodServiceExport, test_r1[:, 1:]), axis=1)
inv_real_prd_GoodServiceExport = scaler.inverse_transform(inv_real_prd_GoodServiceExport)
inv_real_prd_GoodServiceExport = inv_real_prd_GoodServiceExport[:,0]

pred_real_GoodServiceExport=pd.DataFrame(inv_real_prd_GoodServiceExport)
pred_real_GoodServiceExport.rename(columns={0:'GoodServiceExport'}, inplace=True)
#conversion of oopr to level

baseoopr=data[-1:]
GoodServiceExport_baseoopr=baseoopr['GoodServiceExport']
GoodServiceExport_baseoopr=GoodServiceExport_baseoopr.reset_index()


merge3=pd.concat([GoodServiceExport_baseoopr,pred_real_GoodServiceExport ],axis=0,ignore_index=True)
GoodServiceExport_ooprlevels=merge3.cumsum()
GoodServiceExport_ooprlevels=GoodServiceExport_ooprlevels.iloc[1:len(GoodServiceExport_ooprlevels),:]
GoodServiceExport_ooprlevels=GoodServiceExport_ooprlevels.reset_index()
del GoodServiceExport_ooprlevels['index']

#LSTM for4nd Equation
values = vardata.values
scaler = MinMaxScaler(feature_range=(0, 1))
scaled = scaler.fit_transform(values)
# frame as supervised learning
reframed = series_to_supervised(scaled, 1, 1)

print(reframed.head())

# split into train and test sets
values = reframed.values
# drop columns we don't want to predict 
#to change for each equation
reframed.drop(reframed.columns[[4,5,6]], axis=1, inplace=True)
print(reframed.head())

# split into train and test sets
values = reframed.values
# drop columns we don't want to predict 
#to change for each equation
values = reframed.values
values = reframed.values
train_sample=(n_obs-h)
train=values[0:train_sample,:]
test = values[train_sample:, :]


# split into input and outputs
train_X, train_y = train[:, :-1], train[:, -1]
test_X, test_y = test[:, :-1], test[:, -1]
# reshape input to be 3D [samples, timesteps, features]
train_X = train_X.reshape((train_X.shape[0], 1, train_X.shape[1]))
test_X = test_X.reshape((test_X.shape[0], 1, test_X.shape[1]))
print(train_X.shape, train_y.shape, test_X.shape, test_y.shape)
# design network
model4 = Sequential()
model4.add(LSTM(50, input_shape=(train_X.shape[1], train_X.shape[2])))
model4.add(Dense(1))
model4.compile(loss='mae', optimizer='adam')
# fit network
history = model4.fit(train_X, train_y, epochs=50, batch_size=100, validation_data=(test_X, test_y), verbose=2, shuffle=False)
# plot history
pyplot.plot(history.history['loss'], label='train')
pyplot.plot(history.history['val_loss'], label='test')
pyplot.legend()
pyplot.show()
# make a prediction
yhat = model4.predict(test_X)
#reducing to 2D
test_X = test_X.reshape((test_X.shape[0], test_X.shape[2]))
# invert scaling for forecast
inv_yhat = concatenate((yhat, test_X[:, 1:]), axis=1)
inv_yhat = scaler.inverse_transform(inv_yhat)
inv_yhat = inv_yhat[:,0]


# invert scaling for actual
test_y = test_y.reshape((len(test_y), 1))
inv_y = concatenate((test_y, test_X[:, 1:]), axis=1)
inv_y = scaler.inverse_transform(inv_y)
inv_y = inv_y[:,0]


y_GoodServiceImportF=inv_yhat


size1=len(data)
yGoodServiceImportF=pd.DataFrame(y_GoodServiceImportF)

yGoodServiceImportF.rename(columns={0:'GoodServiceImport'}, inplace=True)

basedate=data.iloc[size1-h-1:size1-h,:]
GoodServiceImport_basedate=basedate['GoodServiceImport']
forecast2=yGoodServiceImportF[['GoodServiceImport' ]]
GoodServiceImport_basedate=GoodServiceImport_basedate.reset_index()


merge4=pd.concat([GoodServiceImport_basedate,forecast2],axis=0,ignore_index=True)
GoodServiceImport_forecastlevels=merge4.cumsum()
GoodServiceImport_forecastlevels=GoodServiceImport_forecastlevels.iloc[1:len(GoodServiceImport_forecastlevels),:]
GoodServicImport_forecastlevels=GoodServiceImport_forecastlevels.reset_index()
del GoodServiceImport_forecastlevels['index']
test_level=data.iloc[size1-h:size1,:]
GoodServiceImport_test=test_level['GoodServiceImport']

# calculate RMSE
GoodServiceImport_test=GoodServiceImport_test.reset_index()
del GoodServiceImport_test['index']
rmse = sqrt(mean_squared_error(GoodServiceImport_test, GoodServiceImport_forecastlevels))
print('RMSE: %.3f' % rmse)
mape=100*(abs(GoodServiceImport_test-GoodServiceImport_forecastlevels)/GoodServiceImport_test).mean()
print('MAPE: %.3f' % mape)

#out of sample real prediction
test_r1=test_X[-1,0:]
test_r1 = test_r1.reshape(1,1, test_r1.shape[0])

real_prd_GoodServiceImport=model4.predict(test_r1)
test_r1=test_r1.reshape(1,4)
inv_real_prd_GoodServiceImport = concatenate((real_prd_GoodServiceImport, test_r1[:, 1:]), axis=1)
inv_real_prd_GoodServiceImport = scaler.inverse_transform(inv_real_prd_GoodServiceImport)
inv_real_prd_GoodServiceImport = inv_real_prd_GoodServiceImport[:,0]

pred_real_GoodServiceImport=pd.DataFrame(inv_real_prd_GoodServiceImport)
pred_real_GoodServiceImport.rename(columns={0:'GoodServiceImport'}, inplace=True)
#conversion of oopr to level

baseoopr=data[-1:]
GoodServiceImport_baseoopr=baseoopr['GoodServiceImport']
GoodServiceImport_baseoopr=GoodServiceImport_baseoopr.reset_index()


merge4=pd.concat([GoodServiceImport_baseoopr,pred_real_GoodServiceImport ],axis=0,ignore_index=True)
GoodServiceImport_ooprlevels=merge4.cumsum()
GoodServiceImport_ooprlevels=GoodServiceImport_ooprlevels.iloc[1:len(GoodServiceImport_ooprlevels),:]
GoodServiceImport_ooprlevels=GoodServiceImport_ooprlevels.reset_index()
del GoodServiceImport_ooprlevels['index']



#2 Step ahead prediction for GDP

inv_yd = concatenate((real_prd_GDP,real_prd_HouseholdConsumption, real_prd_GoodServiceExport,real_prd_GoodServiceImport), axis=0)
inv_yd=inv_yd.reshape(1,1,inv_yd.shape[0])
real_prd2_GDP=model.predict(inv_yd)

inv_yd=inv_yd.reshape(1,4)
inv_real_prd2_GDP = concatenate((real_prd2_GDP, inv_yd[:, 1:]), axis=1)
inv_real_prd2_GDP = scaler.inverse_transform(inv_real_prd2_GDP)
inv_real_prd2_GDP = inv_real_prd2_GDP[:,0]

#2 Step ahead prediction for HOUSEHOLDCONSUMPTION
inv_yd = concatenate((real_prd_GDP,real_prd_HouseholdConsumption, real_prd_GoodServiceExport,real_prd_GoodServiceImport), axis=0)
inv_yd=inv_yd.reshape(1,1,inv_yd.shape[0])
real_prd2_HouseholdConsumption=model2.predict(inv_yd)
inv_yd=inv_yd.reshape(1,4)
inv_real_prd2_HouseholdConsumption = concatenate((real_prd2_HouseholdConsumption, inv_yd[:, 1:]), axis=1)
inv_real_prd2_HouseholdConsumption = scaler.inverse_transform(inv_real_prd2_HouseholdConsumption)
inv_real_prd2_HouseholdConsumption = inv_real_prd2_HouseholdConsumption[:,0]

#2 Step ahead prediction for GOODSERVICEEXPORT
inv_yd = concatenate((real_prd_GDP,real_prd_HouseholdConsumption, real_prd_GoodServiceExport,real_prd_GoodServiceImport), axis=0)
inv_yd=inv_yd.reshape(1,1,inv_yd.shape[0])
real_prd2_GoodServiceExport=model3.predict(inv_yd)
inv_yd=inv_yd.reshape(1,4)
inv_real_prd2_GoodServiceExport = concatenate((real_prd2_GoodServiceExport, inv_yd[:, 1:]), axis=1)
inv_real_prd2_GoodServiceExport = scaler.inverse_transform(inv_real_prd2_GoodServiceExport)
inv_real_prd2_GoodServiceExport= inv_real_prd2_GoodServiceExport[:,0]


#2 Step ahead prediction for GOODSERVICEIMPORT
inv_yd = concatenate((real_prd_GDP,real_prd_HouseholdConsumption, real_prd_GoodServiceExport,real_prd_GoodServiceImport), axis=0)
inv_yd=inv_yd.reshape(1,1,inv_yd.shape[0])
real_prd2_GoodServiceImport=model4.predict(inv_yd)
inv_yd=inv_yd.reshape(1,4)
inv_real_prd2_GoodServiceImport = concatenate((real_prd2_GoodServiceImport, inv_yd[:, 1:]), axis=1)
inv_real_prd2_GoodServiceImport = scaler.inverse_transform(inv_real_prd2_GoodServiceImport)
inv_real_prd2_GoodServiceImport= inv_real_prd2_GoodServiceImport[:,0]
#conversion of pred2 to level

inv_real_prd2_GDP=pd.DataFrame(inv_real_prd2_GDP)
inv_real_prd2_GDP.rename(columns={0:'GDP'}, inplace=True)
inv_real_prd2_HouseholdConsumption=pd.DataFrame(inv_real_prd2_HouseholdConsumption)
inv_real_prd2_HouseholdConsumption.rename(columns={0:'HouseholdConsumption'}, inplace=True)
inv_real_prd2_GoodServiceExport=pd.DataFrame(inv_real_prd2_GoodServiceExport)
inv_real_prd2_GoodServiceExport.rename(columns={0:'GoodServiceExport'}, inplace=True)
inv_real_prd2_GoodServiceImport=pd.DataFrame(inv_real_prd2_GoodServiceImport)
inv_real_prd2_GoodServiceImport.rename(columns={0:'GoodServiceImport'}, inplace=True)



Pred1=pd.concat([GDP_ooprlevels,HouseholdConsumption_ooprlevels,GoodServiceExport_ooprlevels,GoodServiceImport_ooprlevels],axis=1)
del Pred1['level_0']

Pred2=pd.concat([inv_real_prd2_GDP,inv_real_prd2_HouseholdConsumption,inv_real_prd2_GoodServiceExport,inv_real_prd2_GoodServiceImport],axis=1)
predson=pd.concat([Pred1,Pred2],axis=0)
predson=predson.cumsum()

predson=predson.reset_index()
del predson['index']




