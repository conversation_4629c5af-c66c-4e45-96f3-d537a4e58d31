{"cells": [{"cell_type": "code", "execution_count": null, "id": "665ac2e1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import statsmodels.api as sm\n", "from pathlib import Path\n", "\n", "\n", "def var_lags(var,nlags):\n", "   name=var.name\n", "   str1=str(name)\n", "   var1=var\n", "   df=pd.DataFrame()\n", "   for i in range(nlags):\n", "        df[str1+str(i+1)]=var1.shift(i+1)\n", "        df2=df\n", "   return df2\n", "\n", "data_folder = Path(\"D:/TimeSeriesandForecasting/DataandCodes\")\n", "data = pd.read_excel(data_folder /'ihracat.xlsx', sheet_name='Sayfa1')\n", "y=data['GDP']\n", "l_y=var_lags(y,2) #lag1 and lag2\n", "\n", "x1=data['HouseholdConsumption']\n", "l_x1=var_lags(x1,2) #lag1 and lag2\n", "\n", "x2=data['GoodServiceExport']\n", "l_x2=var_lags(x2,2) #lag1 and lag2\n", "\n", "x3=data['GoodServiceImport']\n", "l_x3=var_lags(x3,2) #lag1 and lag2\n", "\n", "df=pd.concat([y,x1,x2,x3,l_y,l_x1,l_x2,l_x3],axis=1)\n", "df=df.dropna()\n", "\n", "\n", "\n", "Y=df[['GDP']]\n", "x=df[['HouseholdConsumption1', 'GoodServiceExport1', 'GoodServiceImport1']]\n", "X=sm.add_constant(x)\n", "test_size=5\n", "train_size=len(Y)-test_size\n", "test_sample=Y[train_size:len(Y)]\n", "test_sample=test_sample.reset_index()\n", "lf=len(Y)-train_size\n", "matpredall=np.zeros((lf,1))\n", "matrix = np.zeros((1,1)) # Pre-allocate matrix\n", "\n", "testx=X[train_size:len(X)]\n", "testy=Y[train_size:len(X)]\n", "testx=testx.reset_index()\n", "del testx['index']\n", "testy=testy.reset_index()\n", "del testy['index']\n", "\n", "for j in range(lf):\n", "    X_train=X[0+j:train_size+j]\n", "    y_train=Y[0+j:train_size+j]\n", "    X_test=testx[0+j:1+j]\n", "    y_test=testy[0+j:1+j]\n", "    m=0\n", "    results = sm.OLS(endog=y_train, exog=X_train).fit()\n", "    print(results.summary())\n", "    y_pred_OLS = results.predict(X_test)\n", "    matrix[:,m] = y_pred_OLS\n", "    m=m+1\n", "    print(j)   \n", "    mat<PERSON><PERSON>all[j,0]=matrix\n", "           \n", "matytraintest=Y[train_size:len(Y)]  \n", "matytraintest=np.array(matytraintest)\n", "lenmatytraintest=len( matytraintest)\n", "dfmatytraintest=pd.DataFrame(matytraintest)\n", "dfmatpredict=pd.Data<PERSON>rame(matpredall)\n", " \n", "fark=dfmatytraintest.values- dfmatpredict.values\n", "Mat_error=abs(fark) \n", "Mat_MAE=Mat_error.mean(0)\n", "Mat_MAE=Mat_MAE.tolist()\n", "  \n", "\n", "Mat_errorrate=(Mat_error/dfmatytraintest.values)*100\n", "Mat_MAPE=Mat_errorrate.mean(0)\n", "Mat_MAPE=Mat_MAPE.tolist()\n", "pseudo_predicted_price=pd.DataFrame(matpredall)\n", "pseudo_predicted_price.columns=['Predicted_Prices']\n", "\n", "#real out of sample prediction\n", "#verinin son satır <PERSON><PERSON>\n", "testx2=X[len(X)-1:len(X)]\n", "real_pred = results.predict(testx2)\n", "real_pred=real_pred.reset_index()\n", "del real_pred['index']\n", "real_pred.columns=['Real_Predicted_GDP']"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}