#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
# Created on Sun Oct  4 14:01:13 2020

@author: bulent
https://www.statsmodels.org/dev/examples/notebooks/generated/regression_diagnostics.html#Influence-tests
"""
import pandas as pd
import numpy as np
import statsmodels.api as sm
from pathlib import Path

def var_lags(data,nlags):
    alldata=data
    str1='var1'
    df=alldata['Date']
    df['var1']=df.iloc[:,1]
    for i in range(nlags):
        df[str1+str(i+1)]=df['var1'].shift(i+1)
        df2=df
    return df2

def dgpprice(alldata,lags):


    
    
   
    Price=alldata.iloc[:,1]
    Price=pd.DataFrame(Price)
    dfyeni=var_lags(alldata,1)
    dfyeni=dfyeni.set_index("Date")
    alldatason=pd.concat([dfyeni,df3],axis=1)
    alldatason2=alldatason.dropna()
    alldatason3=alldatason2.reset_index()
    return alldatason3
data_folder = Path("D:/TimeSeriesandForecasting/DataandCodes")

data = md = pd.read_excel(data_folder /'ihracat.xlsx', sheet_name='Sayfa1')



# Engle-granger cointegration test-single approach
x=df[['GDP',]]
y=df[['HouseholdConsumption', 'GoodServiceExport', 'GoodServiceImport']]
# data with one time lag 
alldata2=dgpprice(data,1)
writer = pd.ExcelWriter(data_folder/'akbnk_adjusted.xlsx', engine='xlsxwriter')
alldata2.to_excel(writer, sheet_name='akbank',index = False)
writer.save()
alldata2=pd.read_excel(data_folder/'akbnk_adjusted.xlsx', sheet_name='akbank')
column_names = list(alldata2.columns)
print(column_names)
len_data=len(column_names)
X=alldata2[['price1','Volume','SP500','USDTRY','BIST100','Fiyat-Kazanç Oranı', 'Piyasa değeri/Defter Değeri', '2 yıllık tahvil faizi', 'enflasyon', 'TTM ROA', 'TTM ROE','rvol']]
corr1=X.corr()
X=sm.add_constant(X)

Y=alldata2.iloc[:,1:2]  
Y=pd.DataFrame(Y)

test_size=21
train_size=len(Y)-test_size
test_sample=Y[train_size:len(Y)]
test_sample=test_sample.reset_index()
lf=len(Y)-train_size
matpredall=np.zeros((lf,1))
matrix = np.zeros((1,1)) # Pre-allocate matrix

testx=X[train_size:len(X)]
testy=Y[train_size:len(X)]
testx=testx.reset_index()
del testx['index']
testy=testy.reset_index()
del testy['index']

for j in range(lf):
    X_train=X[0+j:train_size+j]
    y_train=Y[0+j:train_size+j]
    X_test=testx[0+j:1+j]
    y_test=testy[0+j:1+j]
    m=0
    results = sm.OLS(endog=y_train, exog=X_train).fit()
    print(results.summary())
    y_pred_OLS = results.predict(X_test)
    matrix[:,m] = y_pred_OLS
    m=m+1
    print(j)   
    matpredall[j,0]=matrix
           
matytraintest=Y[train_size:len(Y)]  
matytraintest=np.array(matytraintest)
lenmatytraintest=len( matytraintest)
dfmatytraintest=pd.DataFrame(matytraintest)
dfmatpredict=pd.DataFrame(matpredall)
 
fark=dfmatytraintest.values- dfmatpredict.values
Mat_error=abs(fark) 
Mat_MAE=Mat_error.mean(0)
Mat_MAE=Mat_MAE.tolist()
  

Mat_errorrate=(Mat_error/dfmatytraintest.values)*100
Mat_MAPE=Mat_errorrate.mean(0)
Mat_MAPE=Mat_MAPE.tolist()
pseudo_predicted_price=pd.DataFrame(matpredall)
pseudo_predicted_price.columns=['Predicted_Prices']

#real out of sample prediction
#verinin son satır alınıyor
testx2=X[len(X)-1:len(X)]
real_pred = results.predict(testx2)
real_pred=real_pred.reset_index()
del real_pred['index']
real_pred.columns=['Real_Predicted_Prices']