#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sun Oct  4 14:01:13 2020

@author: bulent
https://www.statsmodels.org/dev/examples/notebooks/generated/regression_diagnostics.html#Influence-tests
"""
import pandas as pd
import numpy as np
import statsmodels.api as sm
from pathlib import Path





data_folder = Path("D:/FinansveRiskuygulamalar/")
PORTFOY = pd.read_excel(data_folder/'PORTFOY2022.xlsx', sheet_name='Sheet1')


DAX=PORTFOY['DAX']
Y=DAX
Y=pd.DataFrame(Y)
test_size=21
train_size=len(Y)-test_size
test_sample=Y[train_size:len(Y)]
test_sample=test_sample.reset_index()
lf=len(Y)-train_size
matpredall=np.zeros((lf,1))
matrix = np.zeros((1,1)) # Pre-allocate matrix

testy=Y[train_size:len(Y)]
testy=testy.reset_index()
del testy['index']

for j in range(lf):
    print(j)
    y_train=Y[0+j:train_size+j]
    y_test=testy[0+j:1+j]
    m=0
    mod=sm.tsa.statespace.SARIMAX(y_train,trend='c',order=(1,1,1))
    results=mod.fit(disp=False)
    y_pred_ARIMA =results.get_prediction(start=train_size,end=train_size, dynamic=True)
    pred_pseudo1=y_pred_ARIMA.predicted_mean
    pred_pseudo1=pred_pseudo1.reset_index()
    del pred_pseudo1['index']
    pred_pseudo1.columns = ['predicted']
    ypredict1=pred_pseudo1.values

    matrix[:,m] = ypredict1 
    m=m+1
    print(j)   
    matpredall[j,0]=matrix
           
matytraintest=Y[train_size:len(Y)]  
matytraintest=np.array(matytraintest)
lenmatytraintest=len( matytraintest)
dfmatytraintest=pd.DataFrame(matytraintest)
dfmatpredict=pd.DataFrame(matpredall)
 
fark=dfmatytraintest.values- dfmatpredict.values
Mat_error=abs(fark) 
Mat_MAE=Mat_error.mean(0)
Mat_MAE=Mat_MAE.tolist()
  

Mat_errorrate=(Mat_error/dfmatytraintest.values)*100
Mat_MAPE=Mat_errorrate.mean(0)
Mat_MAPE=Mat_MAPE.tolist()
pseudo_predict_all=pd.DataFrame(matpredall)
pseudo_predict_all.columns = ['predicted']

#real out of sample predictions 
y_real_pred =results.get_prediction(start=len(Y),end=len(Y), dynamic=True)
y_real_pred1=y_real_pred.predicted_mean
y_real_pred1=y_real_pred1.reset_index()
del y_real_pred1['index']
y_real_pred1.columns = ['real_predicted']
