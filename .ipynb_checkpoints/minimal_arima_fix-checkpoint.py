"""
Minimal ARIMA Fix Example
This shows the exact fixes needed for your original code
"""

# Import statements (add these to your notebook)
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.arima.model import ARIMA

# Load your data (same as original)
df = pd.read_csv('../Dataset/MaunaLoaDailyTemps.csv', index_col='DATE', parse_dates=True)
df = df.dropna()

print('Shape of data:', df.shape)
print('Columns:', df.columns.tolist())

# Split data (same as original)
train = df.iloc[:-30]
test = df.iloc[-30:]
print('Train shape:', train.shape, 'Test shape:', test.shape)

# Fit model (same as original)
model = ARIMA(train['AvgTemp'], order=(1,0,5))
fitted_model = model.fit()
print('Model fitted successfully!')

# FIX 1: Make predictions properly
start = len(train)
end = len(train) + len(test) - 1

# Make predictions
predictions = fitted_model.predict(start=start, end=end, typ='levels')

# FIX 2: CRITICAL - Assign proper date index to predictions
predictions.index = test.index

print('Predictions shape:', predictions.shape)
print('Test data shape:', test['AvgTemp'].shape)
print('Index match:', predictions.index.equals(test.index))

# FIX 3: Plot with proper syntax
plt.figure(figsize=(15, 8))

# Plot training data
plt.plot(train.index, train['AvgTemp'], label='Training Data', color='blue', alpha=0.7)

# Plot test data
plt.plot(test.index, test['AvgTemp'], label='Actual Test Data', color='green', linewidth=2)

# Plot predictions (now with proper index)
plt.plot(predictions.index, predictions, label='ARIMA Predictions', color='red', linewidth=2, linestyle='--')

plt.title('ARIMA Model: Training, Test, and Predictions', fontsize=16)
plt.xlabel('Date', fontsize=12)
plt.ylabel('Average Temperature (°F)', fontsize=12)
plt.legend(fontsize=12)  # FIX: removed quotes around legend
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# FIX 4: Calculate performance metrics
from sklearn.metrics import mean_absolute_error, mean_squared_error
import numpy as np

mae = mean_absolute_error(test['AvgTemp'], predictions)
rmse = np.sqrt(mean_squared_error(test['AvgTemp'], predictions))

print(f"MAE: {mae:.2f}°F")
print(f"RMSE: {rmse:.2f}°F")

# FIX 5: Show comparison
comparison = pd.DataFrame({
    'Actual': test['AvgTemp'],
    'Predicted': predictions,
    'Difference': test['AvgTemp'] - predictions
})

print("\nFirst 10 predictions vs actual:")
print(comparison.head(10))

# FIX 6: Residuals plot
residuals = test['AvgTemp'] - predictions

plt.figure(figsize=(12, 4))
plt.subplot(1, 2, 1)
plt.plot(residuals.index, residuals, color='purple')
plt.title('Residuals Over Time')
plt.axhline(y=0, color='red', linestyle='--')
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
plt.hist(residuals, bins=15, color='skyblue', edgecolor='black', alpha=0.7)
plt.title('Residuals Distribution')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("✅ All fixes applied successfully!")
print("Key fixes:")
print("1. Removed .rename() from predictions")
print("2. Added: predictions.index = test.index")
print("3. Fixed legend syntax: plt.legend() instead of plt.legend('string')")
print("4. Added proper error metrics and residual analysis") 