# ARIMA Model Issues and Fixes

## Problems Identified in Your Original Code

### 1. **Model.predict() Issues**
The main problems with your `model.predict()` calls were:

#### Issue 1: Incorrect Parameter Usage
```python
# ❌ Original problematic code
pred = model.predict(start=start, end=end, typ='levels').rename('ARIMA predictions')
```

**Problems:**
- The `rename()` method was being called on the prediction result, which might not work as expected
- The predictions didn't have proper date indexing

#### Issue 2: Index Mismatch
```python
# ❌ Original code
start = len(train)
end = len(train) + len(test) - 1
pred = model.predict(start=start, end=end, typ='levels')
```

**Problems:**
- Predictions were returned with integer indices instead of date indices
- This caused plotting issues because the x-axis didn't align properly

### 2. **Empty Plots Issues**
The plots were empty because:
- Predictions and actual data had different indices
- Legend parameters were incorrect
- Data wasn't properly aligned for plotting

## Fixed Solutions

### 1. **Proper Prediction Code**
```python
# ✅ Fixed prediction code
start = len(train)
end = len(train) + len(test) - 1

# Make predictions
predictions = fitted_model.predict(start=start, end=end, typ='levels')

# Create proper date index for predictions
prediction_index = test.index
predictions.index = prediction_index

# Now predictions have the same date index as test data
```

### 2. **Fixed Plotting Code**
```python
# ✅ Fixed plotting code
plt.figure(figsize=(15, 8))

# Plot training data
plt.plot(train.index, train['AvgTemp'], label='Training Data', color='blue', alpha=0.7)

# Plot test data
plt.plot(test.index, test['AvgTemp'], label='Actual Test Data', color='green', linewidth=2)

# Plot predictions (now with proper index)
plt.plot(predictions.index, predictions, label='ARIMA Predictions', color='red', linewidth=2, linestyle='--')

plt.title('ARIMA Model: Training, Test, and Predictions', fontsize=16)
plt.xlabel('Date', fontsize=12)
plt.ylabel('Average Temperature (°F)', fontsize=12)
plt.legend(fontsize=12)  # Fixed: removed quotes around legend
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()
```

### 3. **Complete Fixed Workflow**

```python
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.arima.model import ARIMA
from pmdarima import auto_arima

# 1. Load data
df = pd.read_csv('../Dataset/MaunaLoaDailyTemps.csv', index_col='DATE', parse_dates=True)
df = df.dropna()

# 2. Split data
train = df.iloc[:-30]
test = df.iloc[-30:]

# 3. Find optimal parameters
stepwise_fit = auto_arima(df['AvgTemp'], suppress_warnings=True)
optimal_order = stepwise_fit.order

# 4. Fit model
model = ARIMA(train['AvgTemp'], order=optimal_order)
fitted_model = model.fit()

# 5. Make predictions (FIXED)
start = len(train)
end = len(train) + len(test) - 1
predictions = fitted_model.predict(start=start, end=end, typ='levels')

# 6. Fix prediction index (CRITICAL FIX)
predictions.index = test.index

# 7. Plot results (FIXED)
plt.figure(figsize=(15, 8))
plt.plot(train.index, train['AvgTemp'], label='Training Data', color='blue', alpha=0.7)
plt.plot(test.index, test['AvgTemp'], label='Actual Test Data', color='green', linewidth=2)
plt.plot(predictions.index, predictions, label='ARIMA Predictions', color='red', linewidth=2, linestyle='--')
plt.title('ARIMA Model: Training, Test, and Predictions')
plt.xlabel('Date')
plt.ylabel('Average Temperature (°F)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()
```

## Key Fixes Summary

1. **Index Alignment**: Ensure predictions have the same date index as test data
2. **Proper Plotting**: Use correct legend syntax and ensure data alignment
3. **Error Handling**: Add proper error checking and debugging output
4. **Model Validation**: Include performance metrics and residual analysis

## Additional Improvements

### Performance Metrics
```python
from sklearn.metrics import mean_absolute_error, mean_squared_error
import numpy as np

mae = mean_absolute_error(test['AvgTemp'], predictions)
rmse = np.sqrt(mean_squared_error(test['AvgTemp'], predictions))

print(f"MAE: {mae:.2f}°F")
print(f"RMSE: {rmse:.2f}°F")
```

### Residual Analysis
```python
residuals = test['AvgTemp'] - predictions

plt.figure(figsize=(12, 4))
plt.subplot(1, 2, 1)
plt.plot(residuals.index, residuals)
plt.title('Residuals Over Time')
plt.axhline(y=0, color='red', linestyle='--')

plt.subplot(1, 2, 2)
plt.hist(residuals, bins=15)
plt.title('Residuals Distribution')
plt.show()
```

## Common Issues to Avoid

1. **Don't use `.rename()` on prediction results** - it can cause issues
2. **Always check data shapes and indices** before plotting
3. **Use proper legend syntax** - `plt.legend()` not `plt.legend('string')`
4. **Ensure date index consistency** between predictions and actual data
5. **Handle missing values** before model fitting

## Testing Your Fixes

To test if your fixes work:

1. Check that `predictions.shape == test['AvgTemp'].shape`
2. Verify that `predictions.index.equals(test.index)`
3. Ensure plots show all three lines (training, actual, predictions)
4. Confirm that performance metrics are reasonable

The main issue was that predictions weren't properly indexed with dates, causing the plotting to fail. The fixed code ensures proper index alignment and correct plotting syntax. 