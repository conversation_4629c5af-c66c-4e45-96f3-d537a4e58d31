# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.stattools import adfuller, acf, pacf
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_squared_error, mean_absolute_error
import itertools
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_theme()

# Load and prepare data
df = pd.read_csv('../Dataset/data_01.csv')
df['datetime'] = pd.to_datetime(df['datetime'])
df.set_index('datetime', inplace=True)

print("Data Info:")
print(f"Shape: {df.shape}")
print(f"Date range: {df.index.min()} to {df.index.max()}")
print(f"Total days: {(df.index.max() - df.index.min()).days}")
print(f"Years of data: {len(df) / 365.25:.1f}")

# Plot the original time series
plt.figure(figsize=(15, 6))
df['tempmax'].plot()
plt.title('Istanbul Maximum Temperature Time Series')
plt.xlabel('Date')
plt.ylabel('Temperature (°C)')
plt.grid(True)
plt.show()

# 1. STATIONARITY ANALYSIS
print("\n=== STATIONARITY ANALYSIS ===")

def check_stationarity(timeseries):
    """Perform Augmented Dickey-Fuller test for stationarity"""
    result = adfuller(timeseries.dropna())
    print(f'ADF Statistic: {result[0]:.4f}')
    print(f'p-value: {result[1]:.4f}')
    print('Critical values:')
    for key, value in result[4].items():
        print(f'\t{key}: {value:.4f}')
    
    if result[1] <= 0.05:
        print("Series is stationary (reject null hypothesis)")
        return True
    else:
        print("Series is non-stationary (fail to reject null hypothesis)")
        return False

# Check original series stationarity
print("Original series stationarity:")
is_stationary = check_stationarity(df['tempmax'])

# If not stationary, difference the data
if not is_stationary:
    print("\nDifferencing the series...")
    df_diff = df['tempmax'].diff().dropna()
    print("First difference stationarity:")
    check_stationarity(df_diff)
    
    # Check seasonal differencing if needed
    seasonal_diff = df['tempmax'].diff(365).dropna()  # Annual seasonality
    print("\nSeasonal difference (365 days) stationarity:")
    check_stationarity(seasonal_diff)

# 2. SEASONALITY ANALYSIS
print("\n=== SEASONALITY ANALYSIS ===")

# Monthly box plot to identify seasonal patterns
df['month'] = df.index.month
df['year'] = df.index.year

plt.figure(figsize=(15, 6))
plt.subplot(1, 2, 1)
df.boxplot(column='tempmax', by='month', ax=plt.gca())
plt.title('Temperature by Month')
plt.suptitle('')

# Yearly trend
plt.subplot(1, 2, 2)
yearly_avg = df.groupby('year')['tempmax'].mean()
yearly_avg.plot(kind='line', marker='o')
plt.title('Yearly Average Temperature')
plt.xlabel('Year')
plt.ylabel('Average Temperature (°C)')
plt.grid(True)
plt.tight_layout()
plt.show()

# 3. ACF AND PACF ANALYSIS
print("\n=== ACF AND PACF ANALYSIS ===")

# Use differenced data if original is non-stationary
if not is_stationary:
    analysis_data = df_diff
else:
    analysis_data = df['tempmax']

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 5))
plot_acf(analysis_data, ax=ax1, lags=50)
plot_pacf(analysis_data, ax=ax2, lags=50, method='ywm')
ax1.set_title('Autocorrelation Function')
ax2.set_title('Partial Autocorrelation Function')
plt.tight_layout()
plt.show()

# 4. OPTIMIZED PARAMETER SELECTION
print("\n=== OPTIMIZED PARAMETER SELECTION ===")

def find_best_sarima_params(data, max_p=3, max_d=2, max_q=3, max_P=2, max_D=1, max_Q=2, seasonal_period=365):
    """Find optimal SARIMA parameters using grid search with AIC"""
    best_aic = float('inf')
    best_params = None
    best_seasonal_params = None
    
    # Define parameter ranges
    p_range = range(0, max_p + 1)
    d_range = range(0, max_d + 1)
    q_range = range(0, max_q + 1)
    P_range = range(0, max_P + 1)
    D_range = range(0, max_D + 1)
    Q_range = range(0, max_Q + 1)
    
    total_combinations = len(p_range) * len(d_range) * len(q_range) * len(P_range) * len(D_range) * len(Q_range)
    print(f"Testing {total_combinations} parameter combinations...")
    
    count = 0
    for p, d, q, P, D, Q in itertools.product(p_range, d_range, q_range, P_range, D_range, Q_range):
        count += 1
        if count % 100 == 0:
            print(f"Progress: {count}/{total_combinations} combinations tested")
        
        try:
            model = SARIMAX(data, order=(p, d, q), seasonal_order=(P, D, Q, seasonal_period))
            fitted_model = model.fit(disp=0)
            
            if fitted_model.aic < best_aic:
                best_aic = fitted_model.aic
                best_params = (p, d, q)
                best_seasonal_params = (P, D, Q, seasonal_period)
                print(f"New best AIC: {best_aic:.2f} with params SARIMA{best_params}{best_seasonal_params}")
                
        except:
            continue
    
    return best_params, best_seasonal_params, best_aic

# Use a smaller sample for parameter selection to speed up the process
sample_size = min(1000, len(df))
sample_data = df['tempmax'].iloc[-sample_size:]

print("Finding optimal SARIMA parameters...")
best_order, best_seasonal_order, best_aic = find_best_sarima_params(
    sample_data, 
    max_p=1, max_d=1, max_q=1, 
    max_P=1, max_D=1, max_Q=1, 
    seasonal_period=365
)

print(f"\nBest SARIMA parameters:")
print(f"Order: {best_order}")
print(f"Seasonal Order: {best_seasonal_order}")
print(f"AIC: {best_aic:.2f}")

# 5. MODEL FITTING AND VALIDATION
print("\n=== MODEL FITTING AND VALIDATION ===")

# Split data for validation
train_size = int(0.85 * len(df))
train = df['tempmax'].iloc[:train_size]
test = df['tempmax'].iloc[train_size:]
steps = len(test)

print(f"Train size: {len(train)}")
print(f"Test size: {len(test)}")

# Fit the optimized model
model = SARIMAX(train, order=best_order, seasonal_order=best_seasonal_order)
fitted_model = model.fit(disp=0)

print(f"Model AIC: {fitted_model.aic:.2f}")
print(f"Model BIC: {fitted_model.bic:.2f}")

# Make predictions
forecast = fitted_model.get_forecast(steps=steps)
forecast_mean = forecast.predicted_mean
forecast_conf = forecast.conf_int()

# Calculate metrics
mse = mean_squared_error(test, forecast_mean)
mae = mean_absolute_error(test, forecast_mean)
rmse = np.sqrt(mse)
mape = np.mean(np.abs((test - forecast_mean) / test)) * 100

print(f"\nForecast Performance Metrics:")
print(f"MSE: {mse:.4f}")
print(f"MAE: {mae:.4f}")
print(f"RMSE: {rmse:.4f}")
print(f"MAPE: {mape:.2f}%")

# 6. VISUALIZATION OF RESULTS
print("\n=== VISUALIZATION ===")

# Plot training, test, and predictions
plt.figure(figsize=(15, 8))

# Plot historical data
plt.plot(train.index, train, label='Training Data', color='blue', alpha=0.7)
plt.plot(test.index, test, label='Actual Test Data', color='green', alpha=0.7)

# Plot predictions
plt.plot(test.index, forecast_mean, label='Predictions', color='red', linewidth=2)

# Plot confidence intervals
plt.fill_between(test.index, 
                 forecast_conf.iloc[:, 0], 
                 forecast_conf.iloc[:, 1], 
                 color='red', alpha=0.1, label='95% Confidence Interval')

plt.title(f'SARIMA{best_order}{best_seasonal_order} Forecast vs Actual')
plt.xlabel('Date')
plt.ylabel('Temperature (°C)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# 7. RESIDUAL ANALYSIS
print("\n=== RESIDUAL ANALYSIS ===")

residuals = test - forecast_mean

# Plot residuals
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Residuals over time
axes[0, 0].plot(test.index, residuals)
axes[0, 0].set_title('Residuals Over Time')
axes[0, 0].set_xlabel('Date')
axes[0, 0].set_ylabel('Residuals')
axes[0, 0].grid(True, alpha=0.3)

# Residuals histogram
axes[0, 1].hist(residuals, bins=30, alpha=0.7, edgecolor='black')
axes[0, 1].set_title('Residuals Distribution')
axes[0, 1].set_xlabel('Residuals')
axes[0, 1].set_ylabel('Frequency')
axes[0, 1].grid(True, alpha=0.3)

# Q-Q plot
from scipy import stats
stats.probplot(residuals, dist="norm", plot=axes[1, 0])
axes[1, 0].set_title('Q-Q Plot of Residuals')
axes[1, 0].grid(True, alpha=0.3)

# Residuals vs fitted
axes[1, 1].scatter(forecast_mean, residuals, alpha=0.6)
axes[1, 1].axhline(y=0, color='red', linestyle='--')
axes[1, 1].set_title('Residuals vs Fitted Values')
axes[1, 1].set_xlabel('Fitted Values')
axes[1, 1].set_ylabel('Residuals')
axes[1, 1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Ljung-Box test for residual autocorrelation
lb_test = acorr_ljungbox(residuals, lags=10, return_df=True)
print(f"Ljung-Box test p-value: {lb_test['lb_pvalue'].iloc[-1]:.4f}")

if lb_test['lb_pvalue'].iloc[-1] > 0.05:
    print("Residuals are not autocorrelated (good)")
else:
    print("Residuals show autocorrelation (model may need improvement)")

# 8. FUTURE FORECAST
print("\n=== FUTURE FORECAST (90 DAYS) ===")

# Fit model on full dataset
full_model = SARIMAX(df['tempmax'], order=best_order, seasonal_order=best_seasonal_order)
full_fitted_model = full_model.fit(disp=0)

# Forecast next 90 days
future_forecast = full_fitted_model.get_forecast(steps=90)
future_mean = future_forecast.predicted_mean
future_conf = future_forecast.conf_int()

# Create future dates
future_dates = pd.date_range(start=df.index[-1] + pd.Timedelta(days=1), periods=90, freq='D')

# Plot historical data and future forecast
plt.figure(figsize=(15, 8))

# Plot historical data
plt.plot(df.index, df['tempmax'], label='Historical Data', color='blue', alpha=0.7)

# Plot future forecast
plt.plot(future_dates, future_mean, label='90-Day Forecast', color='red', linewidth=2)

# Plot confidence intervals
plt.fill_between(future_dates, 
                 future_conf.iloc[:, 0], 
                 future_conf.iloc[:, 1], 
                 color='red', alpha=0.1, label='95% Confidence Interval')

plt.title(f'SARIMA{best_order}{best_seasonal_order} - 90-Day Temperature Forecast')
plt.xlabel('Date')
plt.ylabel('Temperature (°C)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Print forecast statistics
print(f"\nForecast Statistics (Next 90 Days):")
print(f"Mean Forecast: {future_mean.mean():.2f}°C")
print(f"Min Forecast: {future_mean.min():.2f}°C")
print(f"Max Forecast: {future_mean.max():.2f}°C")
print(f"Standard Deviation: {future_mean.std():.2f}°C")

# Save forecasts to DataFrame
forecast_df = pd.DataFrame({
    'date': future_dates,
    'forecast': future_mean.values,
    'lower_ci': future_conf.iloc[:, 0].values,
    'upper_ci': future_conf.iloc[:, 1].values
})

print(f"\nFirst 10 days of forecast:")
print(forecast_df.head(10))

# Save to CSV
forecast_df.to_csv('temperature_forecast_90days.csv', index=False)
print(f"\nForecast saved to 'temperature_forecast_90days.csv'")

print(f"\n=== OPTIMIZATION SUMMARY ===")
print(f"Original parameters: SARIMA(22, 0, 15)(0, 0, 1, 52)")
print(f"Optimized parameters: SARIMA{best_order}{best_seasonal_order}")
print(f"Model AIC: {full_fitted_model.aic:.2f}")
print(f"Test RMSE: {rmse:.4f}")
print(f"Test MAPE: {mape:.2f}%") 