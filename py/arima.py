# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.stattools import acf, pacf
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_squared_error, mean_absolute_error
import itertools
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_theme()

df = pd.read_csv('Dataset/data_01.csv')
df['datetime'] = pd.to_datetime(df['datetime'])
df.set_index('datetime', inplace=True)

df['tempmax'].plot(figsize=(12,5))



# # Difference the data
# df["tempmax"] = df["tempmax"].diff()
# df.dropna(inplace=True)

# # Plot acf and pacf
# fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16,5), dpi=80)
# plot_acf(df['tempmax'])
# plot_pacf(df['tempmax'], method='ywm')
# ax1.tick_params(axis='both', labelsize=12)
# ax2.tick_params(axis='both', labelsize=12)
# plt.show()


# Splitting data
# train, test = train_test_split(df['tempmax'], test_size=0.2, random_state=0)
train, test= np.split(df['tempmax'], [int(.90 *len(df['tempmax']))])
steps= test.size
print("Train Set:\n", train)
print("\nTest Set:\n", test)
# steps

print(train.shape,test.shape)

#fit the model on the train set
best_order=(22, 0, 15)
best_seasonal_order=(0, 0, 1, 52)
model = SARIMAX(train, order=best_order, seasonal_order=best_seasonal_order)
sarima_final_model = model.fit(disp=0)


y_pred_sar = sarima_final_model.get_forecast(steps=steps)
# y_pred_sar
y_pred_sarima = y_pred_sar.predicted_mean
y_pred_sarima_series = pd.Series(y_pred_sarima, index=test.index)


y_pred_sarima_series = pd.Series(y_pred_sarima, index=test.index)

# Plot all the series
plt.figure(figsize=(12, 6))

# Plot train data
plt.plot(train.index, train, label="TRAIN", color='blue')

# Plot test data
plt.plot(test.index, test, label="TEST", color='orange')

# Plot predictions
plt.plot(y_pred_sarima_series.index, y_pred_sarima_series, label="PREDICTION", color='red')

plt.xlabel('Date')
plt.ylabel('Value')
plt.title('Train, Test, and Predicted Test')
plt.legend()

plt.show()

#fit the model on the entire data set 
# best_order=(22, 0, 7)
# best_seasonal_order=(0, 1, 1, 67)
model_2 = SARIMAX(df['tempmax'], order=best_order, seasonal_order=best_seasonal_order)
sarima_final_model_2 = model.fit(disp=0)


#foreacst for next 90 days

forecast = sarima_final_model_2.forecast(steps=90)
forecast_index = pd.date_range(start=df.index[-1], periods=91)[1:]  # Create future dates
forecast_series = pd.Series(forecast, index=forecast_index)

# Get confidence intervals
pred_conf = sarima_final_model_2.get_forecast(steps=90).conf_int()

# Plot the forecasts
plt.figure(figsize=(15, 6))

# Plot original data
plt.plot(df.index, df['tempmax'], label='Historical Data')


# Plot forecast
plt.plot(forecast_series.index, forecast_series, color='red', label='Forecast')

# Plot confidence intervals
plt.fill_between(pred_conf.index,
                 pred_conf.iloc[:, 0],
                 pred_conf.iloc[:, 1],
                 color='red', alpha=0.1)

plt.title('ARIMA Forecast for Istanbul Rainfall (Next 3 Months)')
plt.xlabel('Date')
plt.ylabel('tempmaxerature (mm)')
plt.legend()
plt.grid(True)
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Print forecast statistics
print("\nForecast Statistics:")
print("-------------------")
print(f"Mean Forecast: {forecast.mean():.2f} mm")
print(f"Min Forecast: {forecast.min():.2f} mm")
print(f"Max Forecast: {forecast.max():.2f} mm")
print(f"Standard Deviation: {forecast.std():.2f} mm")

# Save forecasts to DataFrame
forecast_df = pd.DataFrame({
    'date': forecast_series.index,
    'forecast': forecast_series.values,
    'lower_ci': pred_conf.iloc[:, 0].values,
    'upper_ci': pred_conf.iloc[:, 1].values
})

print("\nFirst few rows of forecasts with confidence intervals:")
print(forecast_df.head())