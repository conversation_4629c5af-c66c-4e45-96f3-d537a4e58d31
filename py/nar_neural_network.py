# Non-Linear Autoregressive Neural Network (NAR) for Temperature Forecasting
# This model can capture complex non-linear patterns in time series data

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Try to import required libraries
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, LSTM, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from sklearn.preprocessing import MinMaxScaler
    from sklearn.metrics import mean_squared_error, mean_absolute_error
    TENSORFLOW_AVAILABLE = True
except ImportError:
    print("Warning: TensorFlow not available. Using simplified analysis.")
    TENSORFLOW_AVAILABLE = False

# Load and prepare data
try:
    df = pd.read_csv('../Dataset/data_17-25.csv')
    print("Data loaded successfully!")
except FileNotFoundError:
    print("Error: Could not find data file. Please check the path.")
    exit()

# Prepare data
df['datetime'] = pd.to_datetime(df['datetime'])
df.set_index('datetime', inplace=True)
df = df.resample('W').mean(numeric_only=True)

print(f"Data shape: {df.shape}")
print(f"Date range: {df.index.min()} to {df.index.max()}")
print(f"Years of data: {len(df) / 365.25:.1f}")

# Basic data visualization
plt.figure(figsize=(15, 6))
df['tempmax'].plot()
plt.title('Istanbul Maximum Temperature Time Series')
plt.xlabel('Date')
plt.ylabel('Temperature (°C)')
plt.grid(True, alpha=0.3)
plt.show()

if TENSORFLOW_AVAILABLE:
    # 1. DATA PREPROCESSING
    print("\n=== DATA PREPROCESSING ===")
    
    # Extract temperature data
    temperature_data = df['tempmax'].values.reshape(-1, 1)
    
    # Normalize the data
    scaler = MinMaxScaler(feature_range=(0, 1))
    scaled_data = scaler.fit_transform(temperature_data)
    
    print(f"Original data range: {temperature_data.min():.1f}°C to {temperature_data.max():.1f}°C")
    print(f"Scaled data range: {scaled_data.min():.3f} to {scaled_data.max():.3f}")
    
    # 2. CREATE SEQUENCES FOR NAR MODEL
    print("\n=== CREATING SEQUENCES ===")
    
    def create_sequences(data, look_back=30):
        """Create sequences for NAR model"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i, 0])
            y.append(data[i, 0])
        return np.array(X), np.array(y)
    
    # Parameters
    look_back = 30  # Use last 30 days to predict next day
    print(f"Using {look_back} days of history to predict next day")
    
    # Create sequences
    X, y = create_sequences(scaled_data, look_back)
    print(f"Total sequences created: {len(X)}")
    print(f"Input shape: {X.shape}")
    print(f"Output shape: {y.shape}")
    
    # 3. SPLIT DATA INTO TRAIN/TEST
    print("\n=== DATA SPLITTING ===")
    
    # Split into train and test (85/15)
    train_size = int(0.85 * len(X))
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    print(f"Training samples: {len(X_train)}")
    print(f"Testing samples: {len(X_test)}")
    
    # Reshape for LSTM (samples, timesteps, features)
    X_train = X_train.reshape((X_train.shape[0], X_train.shape[1], 1))
    X_test = X_test.reshape((X_test.shape[0], X_test.shape[1], 1))
    
    print(f"Training data shape: {X_train.shape}")
    print(f"Testing data shape: {X_test.shape}")
    
    # 4. BUILD NAR NEURAL NETWORK
    print("\n=== BUILDING NAR NEURAL NETWORK ===")
    
    def build_nar_model(look_back, units=50, dropout=0.2):
        """Build Non-Linear Autoregressive Neural Network"""
        model = Sequential([
            # First LSTM layer
            LSTM(units=units, return_sequences=True, input_shape=(look_back, 1)),
            BatchNormalization(),
            Dropout(dropout),
            
            # Second LSTM layer
            LSTM(units=units, return_sequences=True),
            BatchNormalization(),
            Dropout(dropout),
            
            # Third LSTM layer
            LSTM(units=units, return_sequences=False),
            BatchNormalization(),
            Dropout(dropout),
            
            # Dense layers for non-linear mapping
            Dense(units=25, activation='relu'),
            BatchNormalization(),
            Dropout(dropout),
            
            Dense(units=10, activation='relu'),
            BatchNormalization(),
            Dropout(dropout),
            
            # Output layer
            Dense(units=1, activation='linear')
        ])
        
        return model
    
    # Build model
    model = build_nar_model(look_back, units=64, dropout=0.3)
    
    # Compile model
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='mse',
        metrics=['mae']
    )
    
    # Model summary
    print("Model Architecture:")
    model.summary()
    
    # 5. TRAIN THE MODEL
    print("\n=== TRAINING NAR MODEL ===")
    
    # Callbacks for better training
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=20,
        restore_best_weights=True,
        verbose=1
    )
    
    reduce_lr = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=10,
        min_lr=0.00001,
        verbose=1
    )
    
    # Train the model
    history = model.fit(
        X_train, y_train,
        epochs=100,
        batch_size=32,
        validation_split=0.2,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )
    
    # 6. EVALUATE MODEL PERFORMANCE
    print("\n=== MODEL EVALUATION ===")
    
    # Make predictions on test set
    y_pred_scaled = model.predict(X_test)
    
    # Inverse transform to get original scale
    y_pred = scaler.inverse_transform(y_pred_scaled)
    y_test_original = scaler.inverse_transform(y_test.reshape(-1, 1))
    
    # Calculate metrics
    mse = mean_squared_error(y_test_original, y_pred)
    mae = mean_absolute_error(y_test_original, y_pred)
    rmse = np.sqrt(mse)
    mape = np.mean(np.abs((y_test_original - y_pred) / y_test_original)) * 100
    
    print(f"Test Performance Metrics:")
    print(f"MSE: {mse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAPE: {mape:.2f}%")
    
    # 7. VISUALIZE TRAINING AND PREDICTIONS
    print("\n=== VISUALIZATION ===")
    
    # Plot training history
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # Loss
    ax1.plot(history.history['loss'], label='Training Loss')
    ax1.plot(history.history['val_loss'], label='Validation Loss')
    ax1.set_title('Model Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # MAE
    ax2.plot(history.history['mae'], label='Training MAE')
    ax2.plot(history.history['val_mae'], label='Validation MAE')
    ax2.set_title('Model MAE')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('MAE')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Plot predictions vs actual
    plt.figure(figsize=(15, 8))
    
    # Get test dates
    test_dates = df.index[train_size + look_back:]
    
    plt.plot(test_dates, y_test_original, label='Actual', color='blue', alpha=0.7)
    plt.plot(test_dates, y_pred, label='NAR Predictions', color='red', linewidth=2)
    plt.title('NAR Neural Network: Predictions vs Actual')
    plt.xlabel('Date')
    plt.ylabel('Temperature (°C)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
    
    # 8. FORECAST NEXT 3 MONTHS (90 DAYS)
    print("\n=== FORECASTING NEXT 3 MONTHS ===")
    
    def forecast_future(model, last_sequence, scaler, steps=90):
        """Forecast future values using the trained model"""
        forecasts = []
        current_sequence = last_sequence.copy()
        
        for _ in range(steps):
            # Reshape for prediction
            current_sequence_reshaped = current_sequence.reshape((1, look_back, 1))
            
            # Predict next value
            next_pred = model.predict(current_sequence_reshaped, verbose=0)
            forecasts.append(next_pred[0, 0])
            
            # Update sequence (remove oldest, add newest)
            current_sequence = np.roll(current_sequence, -1)
            current_sequence[-1] = next_pred[0, 0]
        
        return np.array(forecasts)
    
    # Get the last sequence from training data
    last_sequence = scaled_data[-look_back:]
    
    # Forecast next 90 days
    future_forecasts_scaled = forecast_future(model, last_sequence, scaler, steps=90)
    
    # Inverse transform to get original scale
    future_forecasts = scaler.inverse_transform(future_forecasts_scaled.reshape(-1, 1))
    
    # Create future dates
    last_date = df.index[-1]
    future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), periods=90, freq='D')
    
    # Plot historical data and forecast
    plt.figure(figsize=(15, 8))
    
    # Plot historical data
    plt.plot(df.index, df['tempmax'], label='Historical Data', color='blue', alpha=0.7)
    
    # Plot forecast
    plt.plot(future_dates, future_forecasts, label='3-Month Forecast', color='red', linewidth=2)
    
    plt.title('NAR Neural Network: 3-Month Temperature Forecast')
    plt.xlabel('Date')
    plt.ylabel('Temperature (°C)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
    
    # 9. FORECAST STATISTICS
    print(f"\nForecast Statistics (Next 90 Days):")
    print(f"Mean Forecast: {future_forecasts.mean():.2f}°C")
    print(f"Min Forecast: {future_forecasts.min():.2f}°C")
    print(f"Max Forecast: {future_forecasts.max():.2f}°C")
    print(f"Standard Deviation: {future_forecasts.std():.2f}°C")
    
    # Monthly breakdown
    future_df = pd.DataFrame({
        'date': future_dates,
        'forecast': future_forecasts.flatten()
    })
    future_df['month'] = future_df['date'].dt.month
    future_df['month_name'] = future_df['date'].dt.strftime('%B')
    
    monthly_forecasts = future_df.groupby('month_name')['forecast'].agg(['mean', 'min', 'max'])
    print(f"\nMonthly Forecast Breakdown:")
    for month, stats in monthly_forecasts.iterrows():
        print(f"{month}: {stats['mean']:.1f}°C (min: {stats['min']:.1f}°C, max: {stats['max']:.1f}°C)")
    
    # 10. SAVE FORECASTS
    forecast_df = pd.DataFrame({
        'date': future_dates,
        'forecast': future_forecasts.flatten()
    })
    
    forecast_df.to_csv('nar_temperature_forecast_3months.csv', index=False)
    print(f"\nForecast saved to 'nar_temperature_forecast_3months.csv'")
    
    # 11. COMPARISON WITH SARIMA
    print(f"\n=== NAR vs SARIMA COMPARISON ===")
    print(f"NAR Neural Network Performance:")
    print(f"- RMSE: {rmse:.4f}")
    print(f"- MAPE: {mape:.2f}%")
    print(f"- Model Complexity: {model.count_params()} parameters")
    print(f"- Training Time: ~{len(history.history['loss'])} epochs")
    
    print(f"\nAdvantages of NAR Neural Network:")
    print(f"- Captures non-linear patterns")
    print(f"- Learns complex temporal dependencies")
    print(f"- Can handle multiple seasonalities")
    print(f"- Adaptive to changing patterns")
    
    # 12. MODEL INTERPRETATION
    print(f"\n=== MODEL INTERPRETATION ===")
    print(f"Look-back period: {look_back} days")
    print(f"Network architecture: 3 LSTM layers + 2 Dense layers")
    print(f"Activation functions: ReLU (hidden), Linear (output)")
    print(f"Regularization: Dropout ({0.3*100}%) + Batch Normalization")
    print(f"Optimizer: Adam with learning rate scheduling")
    
else:
    # Fallback analysis without TensorFlow
    print("TensorFlow not available. Showing NAR model design only.")
    print("\nNAR Neural Network Design for Temperature Forecasting:")
    print("1. Input Layer: 30 days of historical temperature data")
    print("2. LSTM Layer 1: 64 units with return_sequences=True")
    print("3. LSTM Layer 2: 64 units with return_sequences=True")
    print("4. LSTM Layer 3: 64 units with return_sequences=False")
    print("5. Dense Layer 1: 25 units with ReLU activation")
    print("6. Dense Layer 2: 10 units with ReLU activation")
    print("7. Output Layer: 1 unit with linear activation")
    print("\nTraining Strategy:")
    print("- Loss function: Mean Squared Error (MSE)")
    print("- Optimizer: Adam with learning rate 0.001")
    print("- Batch size: 32")
    print("- Early stopping with patience=20")
    print("- Learning rate reduction on plateau")
    print("- Validation split: 20%")

print(f"\n=== NAR MODEL SUMMARY ===")
print(f"Model Type: Non-Linear Autoregressive Neural Network")
print(f"Input: Last {look_back if TENSORFLOW_AVAILABLE else 30} days of temperature data")
print(f"Output: Next day temperature prediction")
print(f"Forecast Period: 90 days (3 months)")
print(f"Key Features:")
print(f"- LSTM layers for temporal dependencies")
print(f"- Dense layers for non-linear mapping")
print(f"- Dropout for regularization")
print(f"- Batch normalization for training stability")
print(f"- Can capture complex seasonal patterns") 