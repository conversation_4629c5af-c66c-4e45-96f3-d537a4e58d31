{"cells": [{"cell_type": "code", "execution_count": 1, "id": "538e2c85-945f-49d7-b039-795c391fbb43", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from statsmodels.tsa.stattools import acf, pacf\n", "from statsmodels.tsa.arima.model import ARIMA\n", "from statsmodels.tsa.statespace.sarimax import SARIMAX\n", "from statsmodels.stats.diagnostic import acorr_ljungbox\n", "from statsmodels.graphics.tsaplots import plot_acf, plot_pacf\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error\n", "import itertools\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 2, "id": "89cd07c1-2e78-4a1c-abf6-abff1580e9c9", "metadata": {}, "outputs": [], "source": ["# Set style for better visualizations\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_theme()\n"]}, {"cell_type": "code", "execution_count": 60, "id": "5d007599-411f-41fa-8ca5-fb923d6286ec", "metadata": {}, "outputs": [], "source": ["# Read and prepare the data\n", "df = pd.read_csv('../Dataset/data_01.csv')\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "df.set_index('datetime', inplace=True)\n", "\n", "# Fill any missing values with interpolation\n", "# df['tempmax'] = df['tempmax'].interpolate(method='linear')\n"]}, {"cell_type": "code", "execution_count": 64, "id": "d15fca17-92e4-4c61-aeee-daaa934a5843", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>tempmax</th>\n", "      <th>tempmin</th>\n", "      <th>temp</th>\n", "      <th>feelslikemax</th>\n", "      <th>feelslikemin</th>\n", "      <th>feelslike</th>\n", "      <th>humidity</th>\n", "      <th>precip</th>\n", "      <th>precipprob</th>\n", "      <th>...</th>\n", "      <th>sealevelpressure</th>\n", "      <th>cloudcover</th>\n", "      <th>visibility</th>\n", "      <th>solarradiation</th>\n", "      <th>uvindex</th>\n", "      <th>sunrise</th>\n", "      <th>sunset</th>\n", "      <th>moonphase</th>\n", "      <th>conditions</th>\n", "      <th>source</th>\n", "    </tr>\n", "    <tr>\n", "      <th>datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-08-01</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>29.8</td>\n", "      <td>23.6</td>\n", "      <td>26.5</td>\n", "      <td>29.5</td>\n", "      <td>23.6</td>\n", "      <td>26.9</td>\n", "      <td>62.9</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1009.4</td>\n", "      <td>29.9</td>\n", "      <td>12.4</td>\n", "      <td>163.6</td>\n", "      <td>8</td>\n", "      <td>2022-08-01T05:59:38</td>\n", "      <td>2022-08-01T20:20:37</td>\n", "      <td>0.13</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-02</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>28.8</td>\n", "      <td>21.2</td>\n", "      <td>25.3</td>\n", "      <td>30.4</td>\n", "      <td>21.2</td>\n", "      <td>25.8</td>\n", "      <td>61.7</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1011.3</td>\n", "      <td>23.0</td>\n", "      <td>12.6</td>\n", "      <td>270.5</td>\n", "      <td>8</td>\n", "      <td>2022-08-02T06:00:36</td>\n", "      <td>2022-08-02T20:19:31</td>\n", "      <td>0.16</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-03</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>28.3</td>\n", "      <td>22.6</td>\n", "      <td>25.4</td>\n", "      <td>30.5</td>\n", "      <td>22.6</td>\n", "      <td>26.1</td>\n", "      <td>71.4</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1012.9</td>\n", "      <td>45.3</td>\n", "      <td>12.9</td>\n", "      <td>260.9</td>\n", "      <td>8</td>\n", "      <td>2022-08-03T06:01:34</td>\n", "      <td>2022-08-03T20:18:23</td>\n", "      <td>0.20</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-04</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>27.9</td>\n", "      <td>23.9</td>\n", "      <td>25.7</td>\n", "      <td>29.7</td>\n", "      <td>23.9</td>\n", "      <td>26.1</td>\n", "      <td>70.6</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1013.2</td>\n", "      <td>39.6</td>\n", "      <td>12.8</td>\n", "      <td>182.1</td>\n", "      <td>9</td>\n", "      <td>2022-08-04T06:02:33</td>\n", "      <td>2022-08-04T20:17:13</td>\n", "      <td>0.23</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-05</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>28.5</td>\n", "      <td>23.1</td>\n", "      <td>25.9</td>\n", "      <td>30.8</td>\n", "      <td>23.1</td>\n", "      <td>26.7</td>\n", "      <td>72.0</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1012.4</td>\n", "      <td>37.1</td>\n", "      <td>12.8</td>\n", "      <td>275.0</td>\n", "      <td>8</td>\n", "      <td>2022-08-05T06:03:31</td>\n", "      <td>2022-08-05T20:16:03</td>\n", "      <td>0.25</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-06</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>28.4</td>\n", "      <td>23.7</td>\n", "      <td>25.9</td>\n", "      <td>30.8</td>\n", "      <td>23.7</td>\n", "      <td>26.8</td>\n", "      <td>72.4</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1009.6</td>\n", "      <td>35.4</td>\n", "      <td>12.8</td>\n", "      <td>179.8</td>\n", "      <td>8</td>\n", "      <td>2022-08-06T06:04:30</td>\n", "      <td>2022-08-06T20:14:50</td>\n", "      <td>0.30</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-07</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>28.7</td>\n", "      <td>24.0</td>\n", "      <td>26.4</td>\n", "      <td>31.3</td>\n", "      <td>24.0</td>\n", "      <td>27.5</td>\n", "      <td>72.8</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1008.3</td>\n", "      <td>50.8</td>\n", "      <td>12.8</td>\n", "      <td>200.0</td>\n", "      <td>9</td>\n", "      <td>2022-08-07T06:05:29</td>\n", "      <td>2022-08-07T20:13:37</td>\n", "      <td>0.34</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-08</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>28.9</td>\n", "      <td>24.1</td>\n", "      <td>26.8</td>\n", "      <td>32.0</td>\n", "      <td>24.1</td>\n", "      <td>28.2</td>\n", "      <td>74.5</td>\n", "      <td>4.144</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1010.1</td>\n", "      <td>37.4</td>\n", "      <td>12.8</td>\n", "      <td>169.3</td>\n", "      <td>8</td>\n", "      <td>2022-08-08T06:06:28</td>\n", "      <td>2022-08-08T20:12:22</td>\n", "      <td>0.37</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-09</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>29.5</td>\n", "      <td>24.9</td>\n", "      <td>27.3</td>\n", "      <td>32.6</td>\n", "      <td>24.9</td>\n", "      <td>28.6</td>\n", "      <td>68.9</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>1010.2</td>\n", "      <td>33.2</td>\n", "      <td>12.8</td>\n", "      <td>158.5</td>\n", "      <td>7</td>\n", "      <td>2022-08-09T06:07:28</td>\n", "      <td>2022-08-09T20:11:06</td>\n", "      <td>0.41</td>\n", "      <td>Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-10</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>28.4</td>\n", "      <td>23.9</td>\n", "      <td>26.0</td>\n", "      <td>31.5</td>\n", "      <td>23.9</td>\n", "      <td>27.0</td>\n", "      <td>75.6</td>\n", "      <td>0.119</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1010.2</td>\n", "      <td>55.1</td>\n", "      <td>12.3</td>\n", "      <td>135.5</td>\n", "      <td>5</td>\n", "      <td>2022-08-10T06:08:27</td>\n", "      <td>2022-08-10T20:09:49</td>\n", "      <td>0.44</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 29 columns</p>\n", "</div>"], "text/plain": ["                         name  tempmax  tempmin  temp  feelslikemax  \\\n", "datetime                                                              \n", "2022-08-01  İstanbul, Türkiye     29.8     23.6  26.5          29.5   \n", "2022-08-02  İstanbul, Türkiye     28.8     21.2  25.3          30.4   \n", "2022-08-03  İstanbul, Türkiye     28.3     22.6  25.4          30.5   \n", "2022-08-04  İstanbul, Türkiye     27.9     23.9  25.7          29.7   \n", "2022-08-05  İstanbul, Türkiye     28.5     23.1  25.9          30.8   \n", "2022-08-06  İstanbul, Türkiye     28.4     23.7  25.9          30.8   \n", "2022-08-07  İstanbul, Türkiye     28.7     24.0  26.4          31.3   \n", "2022-08-08  İstanbul, Türkiye     28.9     24.1  26.8          32.0   \n", "2022-08-09  İstanbul, Türkiye     29.5     24.9  27.3          32.6   \n", "2022-08-10  İstanbul, Türkiye     28.4     23.9  26.0          31.5   \n", "\n", "            feelslikemin  feelslike  humidity  precip  precipprob  ...  \\\n", "datetime                                                           ...   \n", "2022-08-01          23.6       26.9      62.9   0.000           0  ...   \n", "2022-08-02          21.2       25.8      61.7   0.000           0  ...   \n", "2022-08-03          22.6       26.1      71.4   0.000           0  ...   \n", "2022-08-04          23.9       26.1      70.6   0.000           0  ...   \n", "2022-08-05          23.1       26.7      72.0   0.000           0  ...   \n", "2022-08-06          23.7       26.8      72.4   0.000           0  ...   \n", "2022-08-07          24.0       27.5      72.8   0.000           0  ...   \n", "2022-08-08          24.1       28.2      74.5   4.144         100  ...   \n", "2022-08-09          24.9       28.6      68.9   0.000           0  ...   \n", "2022-08-10          23.9       27.0      75.6   0.119         100  ...   \n", "\n", "            sealevelpressure cloudcover  visibility  solarradiation  uvindex  \\\n", "datetime                                                                       \n", "2022-08-01            1009.4       29.9        12.4           163.6        8   \n", "2022-08-02            1011.3       23.0        12.6           270.5        8   \n", "2022-08-03            1012.9       45.3        12.9           260.9        8   \n", "2022-08-04            1013.2       39.6        12.8           182.1        9   \n", "2022-08-05            1012.4       37.1        12.8           275.0        8   \n", "2022-08-06            1009.6       35.4        12.8           179.8        8   \n", "2022-08-07            1008.3       50.8        12.8           200.0        9   \n", "2022-08-08            1010.1       37.4        12.8           169.3        8   \n", "2022-08-09            1010.2       33.2        12.8           158.5        7   \n", "2022-08-10            1010.2       55.1        12.3           135.5        5   \n", "\n", "                        sunrise               sunset  moonphase  \\\n", "datetime                                                          \n", "2022-08-01  2022-08-01T05:59:38  2022-08-01T20:20:37       0.13   \n", "2022-08-02  2022-08-02T06:00:36  2022-08-02T20:19:31       0.16   \n", "2022-08-03  2022-08-03T06:01:34  2022-08-03T20:18:23       0.20   \n", "2022-08-04  2022-08-04T06:02:33  2022-08-04T20:17:13       0.23   \n", "2022-08-05  2022-08-05T06:03:31  2022-08-05T20:16:03       0.25   \n", "2022-08-06  2022-08-06T06:04:30  2022-08-06T20:14:50       0.30   \n", "2022-08-07  2022-08-07T06:05:29  2022-08-07T20:13:37       0.34   \n", "2022-08-08  2022-08-08T06:06:28  2022-08-08T20:12:22       0.37   \n", "2022-08-09  2022-08-09T06:07:28  2022-08-09T20:11:06       0.41   \n", "2022-08-10  2022-08-10T06:08:27  2022-08-10T20:09:49       0.44   \n", "\n", "                        conditions  source  \n", "datetime                                    \n", "2022-08-01        Partially cloudy     obs  \n", "2022-08-02        Partially cloudy     obs  \n", "2022-08-03        Partially cloudy     obs  \n", "2022-08-04        Partially cloudy     obs  \n", "2022-08-05        Partially cloudy     obs  \n", "2022-08-06        Partially cloudy     obs  \n", "2022-08-07        Partially cloudy     obs  \n", "2022-08-08  Rain, Partially cloudy     obs  \n", "2022-08-09        Partially cloudy     obs  \n", "2022-08-10  Rain, Partially cloudy     obs  \n", "\n", "[10 rows x 29 columns]"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["# Select t, Ridership only \n", "# df2 = df[['datetime', 'tempmax']]\n", "\n", "# # Convert Month to datetime format \n", "# df2['datetime'] = pd.to_datetime(df2['datetime'])\n", "\n", "# df.info()\n", "df.head(10) "]}, {"cell_type": "code", "execution_count": 65, "id": "7e2ac089-6f32-4efe-b880-ae397590988a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='datetime'>"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df['tempmax'].plot(figsize=(12,5))\n"]}, {"cell_type": "code", "execution_count": null, "id": "73aa35ea-bfdf-49a3-b3b9-3c9b55a56728", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 66, "id": "510d7aff-988c-46ac-8307-60d922ab393e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 67, "id": "6ab05bff-1db0-4895-b2f5-570ca3ddf586", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train Set:\n", " datetime\n", "2022-08-01    29.8\n", "2022-08-02    28.8\n", "2022-08-03    28.3\n", "2022-08-04    27.9\n", "2022-08-05    28.5\n", "              ... \n", "2025-01-11    13.1\n", "2025-01-12     8.0\n", "2025-01-13     6.1\n", "2025-01-14     7.1\n", "2025-01-15     7.4\n", "Name: tempmax, Length: 899, dtype: float64\n", "\n", "Test Set:\n", " datetime\n", "2025-01-16     6.6\n", "2025-01-17     9.5\n", "2025-01-18     9.3\n", "2025-01-19    10.6\n", "2025-01-20     8.8\n", "              ... \n", "2025-04-21    15.8\n", "2025-04-22    15.0\n", "2025-04-23    13.5\n", "2025-04-24    11.1\n", "2025-04-25    16.3\n", "Name: tempmax, Length: 100, dtype: float64\n"]}, {"data": {"text/plain": ["100"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["#split timeseries to %80 train and %20 test\n", "\n", "# Splitting data\n", "# train, test = train_test_split(df['tempmax'], test_size=0.2, random_state=0)\n", "train, test= np.split(df['tempmax'], [int(.90 *len(df['tempmax']))])\n", "steps= test.size\n", "print(\"Train Set:\\n\", train)\n", "print(\"\\nTest Set:\\n\", test)\n", "steps"]}, {"cell_type": "code", "execution_count": null, "id": "45ec241d-250a-4339-ac4a-973cd4449309", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 68, "id": "1c4091c7-f828-4cd3-995c-c3c49835075a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(899,) (100,)\n"]}], "source": ["\n", "print(train.shape,test.shape)\n", "# print(test.iloc[0],test.iloc[-1])"]}, {"cell_type": "code", "execution_count": null, "id": "26d1cf6c-7795-431f-b41c-7e9fd83b4787", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f44cb020-37ed-4ac5-92b7-6945e10a7c38", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "79a2fe9c-5ad4-48be-840f-dc6f9b38cde8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0e774c75-f888-4e34-b24e-14f4aa7df3f4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 69, "id": "7fdce009-06a9-4875-84af-06a0890060f3", "metadata": {}, "outputs": [], "source": ["# model=ARIMA(train,order=(1,0,1))\n", "# model=model.fit()\n", "# model.summary()"]}, {"cell_type": "code", "execution_count": 70, "id": "59d4a46d-941b-4e54-99d1-8e9bffef0aeb", "metadata": {}, "outputs": [], "source": ["def arima_optimizer_aic(train, orders):\n", "    best_aic, best_params = float(\"inf\"), None\n", "    for order in orders:\n", "        arma_model_result = ARIMA(train, order=order).fit()\n", "        aic = arma_model_result.aic\n", "        # print(aic)\n", "        if aic < best_aic:\n", "            best_aic, best_params = aic, order\n", "        print('ARIMA%s AIC=%.2f' % (order, aic))\n", "      \n", "    print('Best ARIMA%s AIC=%.2f' % (best_params, best_aic))\n", "    return best_params"]}, {"cell_type": "code", "execution_count": 71, "id": "1b8b0b68-822b-41ce-9647-d5fab75d728e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ARIMA(0, 0, 0) AIC=6177.97\n", "ARIMA(0, 0, 1) AIC=5294.50\n", "ARIMA(0, 0, 2) AIC=4837.66\n", "ARIMA(0, 1, 0) AIC=4148.28\n", "ARIMA(0, 1, 1) AIC=4150.28\n", "ARIMA(0, 1, 2) AIC=4111.78\n", "ARIMA(0, 2, 0) AIC=4768.74\n", "ARIMA(0, 2, 1) AIC=4153.38\n", "ARIMA(0, 2, 2) AIC=4155.37\n", "ARIMA(1, 0, 0) AIC=4136.40\n", "ARIMA(1, 0, 1) AIC=4137.67\n", "ARIMA(1, 0, 2) AIC=4114.29\n", "ARIMA(1, 1, 0) AIC=4150.28\n", "ARIMA(1, 1, 1) AIC=4081.54\n", "ARIMA(1, 1, 2) AIC=4059.84\n", "ARIMA(1, 2, 0) AIC=4594.78\n", "ARIMA(1, 2, 1) AIC=4155.37\n", "ARIMA(1, 2, 2) AIC=4155.10\n", "ARIMA(2, 0, 0) AIC=4137.87\n", "ARIMA(2, 0, 1) AIC=4136.90\n", "ARIMA(2, 0, 2) AIC=4068.56\n", "ARIMA(2, 1, 0) AIC=4129.43\n", "ARIMA(2, 1, 1) AIC=4055.97\n", "ARIMA(2, 1, 2) AIC=4055.93\n", "ARIMA(2, 2, 0) AIC=4501.35\n", "ARIMA(2, 2, 1) AIC=4134.80\n", "ARIMA(2, 2, 2) AIC=4158.76\n", "Best ARIMA(2, 1, 2) AIC=4055.93\n"]}], "source": ["p = d = q = range(0, 3)\n", "pdq = list(itertools.product(p, d, q))\n", "best_params_aic = arima_optimizer_aic(train, pdq)"]}, {"cell_type": "code", "execution_count": 72, "id": "ad79834f-8726-434e-a612-7ef7773a39b5", "metadata": {}, "outputs": [], "source": ["# from statsmodels.tsa.arima.model import ARIMA\n", "# from sklearn.metrics import mean_absolute_error\n", "\n"]}, {"cell_type": "code", "execution_count": 73, "id": "8e689969-a68d-4cd0-ade5-b6150458996c", "metadata": {}, "outputs": [{"data": {"text/plain": ["4.242409567504924"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["arima_model = ARIMA(train, order=best_params_aic).fit()\n", "y_pred_arima = arima_model.forecast(steps=steps)\n", "mean_absolute_error(test, y_pred_arima)\n", "# arima_model.summary()\n"]}, {"cell_type": "code", "execution_count": 74, "id": "0b3d9b2f-59ed-4d99-ac4d-3c6492343cbf", "metadata": {}, "outputs": [{"data": {"text/plain": ["4.242409567504924"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred_arima = arima_model.forecast(steps=steps)\n", "mean_absolute_error(test, y_pred_arima)"]}, {"cell_type": "code", "execution_count": 77, "id": "dcf5d3f1-2bfb-48a8-95e4-48b8454c5bd6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y_pred_arima_series = pd.Series(y_pred_arima, index=test.index)\n", "\n", "# Plot all the series\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Plot train data\n", "plt.plot(train.index, train, label=\"TRAIN\", color='blue')\n", "\n", "# Plot test data\n", "plt.plot(test.index, test, label=\"TEST\", color='orange')\n", "\n", "# Plot predictions\n", "plt.plot(y_pred_arima_series.index, y_pred_arima_series, label=\"PREDICTION\", color='red')\n", "\n", "plt.xlabel('Date')\n", "plt.ylabel('Value')\n", "plt.title('Train, Test, and Predicted Test')\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 31, "id": "17ff5932-6864-4d87-abe6-83fd32347713", "metadata": {}, "outputs": [], "source": ["p = d = q = range(0, 2)\n", "pdq = list(itertools.product(p, d, q))\n", "seasonal_pdq = [(x[0], x[1], x[2], 30) for x in list(itertools.product(p, d, q))]"]}, {"cell_type": "code", "execution_count": 32, "id": "4f2567fd-3473-4c5d-920b-8ac62da01395", "metadata": {}, "outputs": [], "source": ["def sarima_optimizer_aic(train, pdq, seasonal_pdq):\n", "    best_aic, best_order, best_seasonal_order = float(\"inf\"), float(\"inf\"), None\n", "    for param in pdq:\n", "        for param_seasonal in seasonal_pdq:\n", "            try:\n", "                sarimax_model = SARIMAX(train, order=param, seasonal_order=param_seasonal)\n", "                results = sarimax_model.fit(disp=0)\n", "                aic = results.aic\n", "                if aic < best_aic:\n", "                    best_aic, best_order, best_seasonal_order = aic, param, param_seasonal\n", "                print('SARIMA{}x{} - AIC:{}'.format(param, param_seasonal, aic))\n", "            except:\n", "                continue\n", "    print('SARIMA{}x{} - AIC:{}'.format(best_order, best_seasonal_order, best_aic))\n", "    return best_order, best_seasonal_order"]}, {"cell_type": "code", "execution_count": null, "id": "13d8e480-08d6-44c8-9390-e03d98c7aef9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 33, "id": "ca10617d-84fb-469c-85a6-830b8307264c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SARIMA(0, 0, 0)x(0, 0, 0, 30) - AIC:8058.820124263004\n", "SARIMA(0, 0, 0)x(0, 0, 1, 30) - AIC:7209.578773661788\n", "SARIMA(0, 0, 0)x(0, 1, 0, 30) - AIC:5568.801020251338\n", "SARIMA(0, 0, 0)x(0, 1, 1, 30) - AIC:5570.357182835951\n", "SARIMA(0, 0, 0)x(1, 0, 0, 30) - AIC:5824.196992167548\n", "SARIMA(0, 0, 0)x(1, 0, 1, 30) - AIC:5826.134823044064\n", "SARIMA(0, 0, 0)x(1, 1, 0, 30) - AIC:5570.188023215008\n", "SARIMA(0, 0, 0)x(1, 1, 1, 30) - AIC:5571.9958536134545\n", "SARIMA(0, 0, 1)x(0, 0, 0, 30) - AIC:6952.483912323254\n", "SARIMA(0, 0, 1)x(0, 0, 1, 30) - AIC:6266.111303947909\n", "SARIMA(0, 0, 1)x(0, 1, 0, 30) - AIC:4953.341657919467\n", "SARIMA(0, 0, 1)x(0, 1, 1, 30) - AIC:4935.673146825315\n", "SARIMA(0, 0, 1)x(1, 0, 0, 30) - AIC:5170.074386132725\n", "SARIMA(0, 0, 1)x(1, 0, 1, 30) - AIC:5157.009660008718\n", "SARIMA(0, 0, 1)x(1, 1, 0, 30) - AIC:4931.242574331229\n", "SARIMA(0, 0, 1)x(1, 1, 1, 30) - AIC:4858.008205657623\n", "SARIMA(0, 1, 0)x(0, 0, 0, 30) - AIC:4148.284935651784\n", "SARIMA(0, 1, 0)x(0, 0, 1, 30) - AIC:4150.21885544239\n", "SARIMA(0, 1, 0)x(0, 1, 0, 30) - AIC:4612.83029727079\n", "SARIMA(0, 1, 0)x(0, 1, 1, 30) - AIC:4122.157730019328\n", "SARIMA(0, 1, 0)x(1, 0, 0, 30) - AIC:4150.22184660415\n", "SARIMA(0, 1, 0)x(1, 0, 1, 30) - AIC:4148.698679187107\n", "SARIMA(0, 1, 0)x(1, 1, 0, 30) - AIC:4385.782880664579\n", "SARIMA(0, 1, 0)x(1, 1, 1, 30) - AIC:4124.101058918801\n", "SARIMA(0, 1, 1)x(0, 0, 0, 30) - AIC:4150.275407117811\n", "SARIMA(0, 1, 1)x(0, 0, 1, 30) - AIC:4152.2081342740175\n", "SARIMA(0, 1, 1)x(0, 1, 0, 30) - AIC:4614.662556790561\n", "SARIMA(0, 1, 1)x(0, 1, 1, 30) - AIC:4124.157496460726\n", "SARIMA(0, 1, 1)x(1, 0, 0, 30) - AIC:4152.2111790786\n", "SARIMA(0, 1, 1)x(1, 0, 1, 30) - AIC:4150.673226786379\n", "SARIMA(0, 1, 1)x(1, 1, 0, 30) - AIC:4387.685372542846\n", "SARIMA(0, 1, 1)x(1, 1, 1, 30) - AIC:4126.1009152440665\n", "SARIMA(1, 0, 0)x(0, 0, 0, 30) - AIC:4156.361239966725\n", "SARIMA(1, 0, 0)x(0, 0, 1, 30) - AIC:4158.235770096269\n", "SARIMA(1, 0, 0)x(0, 1, 0, 30) - AIC:4544.566442808847\n", "SARIMA(1, 0, 0)x(0, 1, 1, 30) - AIC:4109.178312517937\n", "SARIMA(1, 0, 0)x(1, 0, 0, 30) - AIC:4158.240650999463\n", "SARIMA(1, 0, 0)x(1, 0, 1, 30) - AIC:4159.855393428366\n", "SARIMA(1, 0, 0)x(1, 1, 0, 30) - AIC:4345.89777642229\n", "SARIMA(1, 0, 0)x(1, 1, 1, 30) - AIC:4110.456967938429\n", "SARIMA(1, 0, 1)x(0, 0, 0, 30) - AIC:4158.360886999571\n", "SARIMA(1, 0, 1)x(0, 0, 1, 30) - AIC:4160.235590576554\n", "SARIMA(1, 0, 1)x(0, 1, 0, 30) - AIC:4540.2691422600365\n", "SARIMA(1, 0, 1)x(0, 1, 1, 30) - AIC:4110.374509345172\n", "SARIMA(1, 0, 1)x(1, 0, 0, 30) - AIC:4160.240465561542\n", "SARIMA(1, 0, 1)x(1, 0, 1, 30) - AIC:4161.853758794967\n", "SARIMA(1, 0, 1)x(1, 1, 0, 30) - AIC:4345.435759670547\n", "SARIMA(1, 0, 1)x(1, 1, 1, 30) - AIC:4111.58356804196\n", "SARIMA(1, 1, 0)x(0, 0, 0, 30) - AIC:4150.278439009256\n", "SARIMA(1, 1, 0)x(0, 0, 1, 30) - AIC:4152.211544433645\n", "SARIMA(1, 1, 0)x(0, 1, 0, 30) - AIC:4614.714204282242\n", "SARIMA(1, 1, 0)x(0, 1, 1, 30) - AIC:4124.157553057237\n", "SARIMA(1, 1, 0)x(1, 0, 0, 30) - AIC:4152.214572183365\n", "SARIMA(1, 1, 0)x(1, 0, 1, 30) - AIC:4150.681358437438\n", "SARIMA(1, 1, 0)x(1, 1, 0, 30) - AIC:4387.714968574917\n", "SARIMA(1, 1, 0)x(1, 1, 1, 30) - AIC:4126.100960320246\n", "SARIMA(1, 1, 1)x(0, 0, 0, 30) - AIC:4081.537005896828\n", "SARIMA(1, 1, 1)x(0, 0, 1, 30) - AIC:4083.3023709273625\n", "SARIMA(1, 1, 1)x(0, 1, 0, 30) - AIC:4530.599425041357\n", "SARIMA(1, 1, 1)x(0, 1, 1, 30) - AIC:4058.8276746319852\n", "SARIMA(1, 1, 1)x(1, 0, 0, 30) - AIC:4083.3075478093856\n", "SARIMA(1, 1, 1)x(1, 0, 1, 30) - AIC:4082.0776957156695\n", "SARIMA(1, 1, 1)x(1, 1, 0, 30) - AIC:4307.507318286233\n", "SARIMA(1, 1, 1)x(1, 1, 1, 30) - AIC:4060.5547377737175\n", "SARIMA(1, 1, 1)x(0, 1, 1, 30) - AIC:4058.8276746319852\n"]}], "source": ["best_order, best_seasonal_order = sarima_optimizer_aic(train, pdq, seasonal_pdq)\n"]}, {"cell_type": "code", "execution_count": 97, "id": "693910d4-0c15-4498-a065-d43d83a165ea", "metadata": {}, "outputs": [], "source": ["best_order=(3, 1, 2)\n", "best_seasonal_order=(0, 1, 1, 90)\n", "model = SARIMAX(train, order=best_order, seasonal_order=best_seasonal_order)\n", "sarima_final_model = model.fit(disp=0)\n"]}, {"cell_type": "code", "execution_count": 98, "id": "504c118f-6667-4ddf-a9a1-ce5f4883229c", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>SARIMAX Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>                <td>tempmax</td>             <th>  No. Observations:  </th>    <td>899</td>   \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>           <td>SARIMAX(3, 1, 2)x(0, 1, [1], 90)</td> <th>  Log Likelihood     </th> <td>-1931.276</td>\n", "</tr>\n", "<tr>\n", "  <th>Date:</th>                    <td>Sun, 06 Jul 2025</td>         <th>  AIC                </th> <td>3876.552</td> \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                        <td>20:18:50</td>             <th>  BIC                </th> <td>3909.414</td> \n", "</tr>\n", "<tr>\n", "  <th>Sample:</th>                     <td>08-01-2022</td>            <th>  HQIC               </th> <td>3889.170</td> \n", "</tr>\n", "<tr>\n", "  <th></th>                           <td>- 01-15-2025</td>           <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>                <td>opg</td>               <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>        <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>ar.L1</th>    <td>   -0.1834</td> <td>    0.088</td> <td>   -2.086</td> <td> 0.037</td> <td>   -0.356</td> <td>   -0.011</td>\n", "</tr>\n", "<tr>\n", "  <th>ar.L2</th>    <td>    0.5535</td> <td>    0.073</td> <td>    7.576</td> <td> 0.000</td> <td>    0.410</td> <td>    0.697</td>\n", "</tr>\n", "<tr>\n", "  <th>ar.L3</th>    <td>   -0.2046</td> <td>    0.035</td> <td>   -5.856</td> <td> 0.000</td> <td>   -0.273</td> <td>   -0.136</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L1</th>    <td>    0.1059</td> <td>    0.079</td> <td>    1.336</td> <td> 0.181</td> <td>   -0.049</td> <td>    0.261</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L2</th>    <td>   -0.8215</td> <td>    0.071</td> <td>  -11.496</td> <td> 0.000</td> <td>   -0.962</td> <td>   -0.681</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.S.L90</th> <td>   -0.9998</td> <td>   39.058</td> <td>   -0.026</td> <td> 0.980</td> <td>  -77.552</td> <td>   75.552</td>\n", "</tr>\n", "<tr>\n", "  <th>sigma2</th>   <td>    5.3980</td> <td>  210.720</td> <td>    0.026</td> <td> 0.980</td> <td> -407.606</td> <td>  418.402</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Ljung-Box (L1) (Q):</th>     <td>0.02</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>59.08</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(Q):</th>                <td>0.89</td> <th>  Prob(JB):          </th> <td>0.00</td> \n", "</tr>\n", "<tr>\n", "  <th>Heteroskedasticity (H):</th> <td>0.94</td> <th>  Skew:              </th> <td>-0.18</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(H) (two-sided):</th>    <td>0.62</td> <th>  Kurtosis:          </th> <td>4.27</td> \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/latex": ["\\begin{center}\n", "\\begin{tabular}{lclc}\n", "\\toprule\n", "\\textbf{Dep. Variable:}          &             tempmax              & \\textbf{  No. Observations:  } &    899      \\\\\n", "\\textbf{Model:}                  & SARIMAX(3, 1, 2)x(0, 1, [1], 90) & \\textbf{  Log Likelihood     } & -1931.276   \\\\\n", "\\textbf{Date:}                   &         Sun, 06 Jul 2025         & \\textbf{  AIC                } &  3876.552   \\\\\n", "\\textbf{Time:}                   &             20:18:50             & \\textbf{  BIC                } &  3909.414   \\\\\n", "\\textbf{Sample:}                 &            08-01-2022            & \\textbf{  HQIC               } &  3889.170   \\\\\n", "\\textbf{}                        &           - 01-15-2025           & \\textbf{                     } &             \\\\\n", "\\textbf{Covariance Type:}        &               opg                & \\textbf{                     } &             \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lcccccc}\n", "                  & \\textbf{coef} & \\textbf{std err} & \\textbf{z} & \\textbf{P$> |$z$|$} & \\textbf{[0.025} & \\textbf{0.975]}  \\\\\n", "\\midrule\n", "\\textbf{ar.L1}    &      -0.1834  &        0.088     &    -2.086  &         0.037        &       -0.356    &       -0.011     \\\\\n", "\\textbf{ar.L2}    &       0.5535  &        0.073     &     7.576  &         0.000        &        0.410    &        0.697     \\\\\n", "\\textbf{ar.L3}    &      -0.2046  &        0.035     &    -5.856  &         0.000        &       -0.273    &       -0.136     \\\\\n", "\\textbf{ma.L1}    &       0.1059  &        0.079     &     1.336  &         0.181        &       -0.049    &        0.261     \\\\\n", "\\textbf{ma.L2}    &      -0.8215  &        0.071     &   -11.496  &         0.000        &       -0.962    &       -0.681     \\\\\n", "\\textbf{ma.S.L90} &      -0.9998  &       39.058     &    -0.026  &         0.980        &      -77.552    &       75.552     \\\\\n", "\\textbf{sigma2}   &       5.3980  &      210.720     &     0.026  &         0.980        &     -407.606    &      418.402     \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lclc}\n", "\\textbf{Ljung-Box (L1) (Q):}     & 0.02 & \\textbf{  <PERSON><PERSON><PERSON><PERSON>ra (JB):  } & 59.08  \\\\\n", "\\textbf{Prob(Q):}                & 0.89 & \\textbf{  Prob(JB):          } &  0.00  \\\\\n", "\\textbf{Heteroskedasticity (H):} & 0.94 & \\textbf{  Skew:              } & -0.18  \\\\\n", "\\textbf{Prob(H) (two-sided):}    & 0.62 & \\textbf{  Kurtosis:          } &  4.27  \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "%\\caption{SARIMAX Results}\n", "\\end{center}\n", "\n", "Warnings: \\newline\n", " [1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                                      SARIMAX Results                                       \n", "============================================================================================\n", "Dep. Variable:                              tempmax   No. Observations:                  899\n", "Model:             SARIMAX(3, 1, 2)x(0, 1, [1], 90)   Log Likelihood               -1931.276\n", "Date:                              Sun, 06 Jul 2025   AIC                           3876.552\n", "Time:                                      20:18:50   BIC                           3909.414\n", "Sample:                                  08-01-2022   HQIC                          3889.170\n", "                                       - 01-15-2025                                         \n", "Covariance Type:                                opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "ar.L1         -0.1834      0.088     -2.086      0.037      -0.356      -0.011\n", "ar.L2          0.5535      0.073      7.576      0.000       0.410       0.697\n", "ar.L3         -0.2046      0.035     -5.856      0.000      -0.273      -0.136\n", "ma.L1          0.1059      0.079      1.336      0.181      -0.049       0.261\n", "ma.L2         -0.8215      0.071    -11.496      0.000      -0.962      -0.681\n", "ma.S.L90      -0.9998     39.058     -0.026      0.980     -77.552      75.552\n", "sigma2         5.3980    210.720      0.026      0.980    -407.606     418.402\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.02   <PERSON>ar<PERSON><PERSON><PERSON><PERSON> (JB):                59.08\n", "Prob(Q):                              0.89   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               0.94   Skew:                            -0.18\n", "Prob(H) (two-sided):                  0.62   Kurtosis:                         4.27\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "\"\"\""]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["sarima_final_model.summary()"]}, {"cell_type": "code", "execution_count": 99, "id": "1822710b-8729-4965-b83c-c01ea996ba27", "metadata": {}, "outputs": [{"data": {"text/plain": ["<statsmodels.tsa.statespace.mlemodel.PredictionResultsWrapper at 0x7048e066f8e0>"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred_sar = sarima_final_model.get_forecast(steps=steps)\n", "y_pred_sar"]}, {"cell_type": "code", "execution_count": 100, "id": "0d4418e8-9d29-4c90-b540-78f4ba522ff4", "metadata": {}, "outputs": [{"data": {"text/plain": ["2025-01-16    9.392736\n", "2025-01-17    8.635709\n", "2025-01-18    8.464290\n", "2025-01-19    8.989552\n", "2025-01-20    9.161352\n", "                ...   \n", "2025-04-21    7.031778\n", "2025-04-22    6.336914\n", "2025-04-23    6.270557\n", "2025-04-24    6.716259\n", "2025-04-25    6.470511\n", "Freq: D, Name: predicted_mean, Length: 100, dtype: float64"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred_sarima = y_pred_sar.predicted_mean\n", "# mean_absolute_error(test, y_pred_sarima)\n", "y_pred_sarima"]}, {"cell_type": "code", "execution_count": 101, "id": "3c3cd47b-7dd8-4efb-8033-1f5e0629a73f", "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime\n", "2025-01-16    9.392736\n", "2025-01-17    8.635709\n", "2025-01-18    8.464290\n", "2025-01-19    8.989552\n", "2025-01-20    9.161352\n", "                ...   \n", "2025-04-21    7.031778\n", "2025-04-22    6.336914\n", "2025-04-23    6.270557\n", "2025-04-24    6.716259\n", "2025-04-25    6.470511\n", "Name: predicted_mean, Length: 100, dtype: float64"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["y_pred_sarima_series = pd.Series(y_pred_sarima, index=test.index)\n", "y_pred_sarima_series"]}, {"cell_type": "code", "execution_count": 102, "id": "888f03cb-3531-4d09-95bd-34913f747b86", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y_pred_sarima_series = pd.Series(y_pred_sarima, index=test.index)\n", "\n", "# Plot all the series\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Plot train data\n", "plt.plot(train.index, train, label=\"TRAIN\", color='blue')\n", "\n", "# Plot test data\n", "plt.plot(test.index, test, label=\"TEST\", color='orange')\n", "\n", "# Plot predictions\n", "plt.plot(y_pred_sarima_series.index, y_pred_sarima_series, label=\"PREDICTION\", color='red')\n", "\n", "plt.xlabel('Date')\n", "plt.ylabel('Value')\n", "plt.title('Train, Test, and Predicted Test')\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a0001cf8-c9f6-4426-829e-fd9a64c193cf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}