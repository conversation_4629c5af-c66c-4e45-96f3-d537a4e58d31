{"cells": [{"cell_type": "code", "execution_count": 2, "id": "538e2c85-945f-49d7-b039-795c391fbb43", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "89cd07c1-2e78-4a1c-abf6-abff1580e9c9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "5d007599-411f-41fa-8ca5-fb923d6286ec", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>tempmax</th>\n", "      <th>tempmin</th>\n", "      <th>temp</th>\n", "      <th>feelslikemax</th>\n", "      <th>feelslikemin</th>\n", "      <th>feelslike</th>\n", "      <th>humidity</th>\n", "      <th>precip</th>\n", "      <th>precipprob</th>\n", "      <th>...</th>\n", "      <th>sealevelpressure</th>\n", "      <th>cloudcover</th>\n", "      <th>visibility</th>\n", "      <th>solarradiation</th>\n", "      <th>uvindex</th>\n", "      <th>sunrise</th>\n", "      <th>sunset</th>\n", "      <th>moonphase</th>\n", "      <th>conditions</th>\n", "      <th>source</th>\n", "    </tr>\n", "    <tr>\n", "      <th>datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2017-05-07</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>21.6</td>\n", "      <td>11.4</td>\n", "      <td>16.5</td>\n", "      <td>21.6</td>\n", "      <td>11.4</td>\n", "      <td>16.5</td>\n", "      <td>75.1</td>\n", "      <td>0.127</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1009.4</td>\n", "      <td>64.5</td>\n", "      <td>13.0</td>\n", "      <td>206.5</td>\n", "      <td>6</td>\n", "      <td>2017-05-07T05:54:37</td>\n", "      <td>2017-05-07T20:07:11</td>\n", "      <td>0.38</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-05-08</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>23.3</td>\n", "      <td>13.5</td>\n", "      <td>18.0</td>\n", "      <td>23.3</td>\n", "      <td>13.5</td>\n", "      <td>18.0</td>\n", "      <td>72.0</td>\n", "      <td>5.723</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1005.0</td>\n", "      <td>51.4</td>\n", "      <td>14.5</td>\n", "      <td>257.2</td>\n", "      <td>8</td>\n", "      <td>2017-05-08T05:53:28</td>\n", "      <td>2017-05-08T20:08:13</td>\n", "      <td>0.41</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-05-09</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>21.4</td>\n", "      <td>12.8</td>\n", "      <td>16.8</td>\n", "      <td>21.4</td>\n", "      <td>12.8</td>\n", "      <td>16.8</td>\n", "      <td>78.5</td>\n", "      <td>1.778</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1004.5</td>\n", "      <td>57.1</td>\n", "      <td>14.7</td>\n", "      <td>244.5</td>\n", "      <td>7</td>\n", "      <td>2017-05-09T05:52:21</td>\n", "      <td>2017-05-09T20:09:14</td>\n", "      <td>0.45</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-05-10</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>15.8</td>\n", "      <td>13.2</td>\n", "      <td>14.5</td>\n", "      <td>15.8</td>\n", "      <td>13.2</td>\n", "      <td>14.5</td>\n", "      <td>88.9</td>\n", "      <td>3.358</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1010.9</td>\n", "      <td>74.2</td>\n", "      <td>14.3</td>\n", "      <td>156.4</td>\n", "      <td>6</td>\n", "      <td>2017-05-10T05:51:15</td>\n", "      <td>2017-05-10T20:10:15</td>\n", "      <td>0.48</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-05-14</th>\n", "      <td>İstanbul, Türkiye</td>\n", "      <td>25.4</td>\n", "      <td>17.2</td>\n", "      <td>21.2</td>\n", "      <td>25.4</td>\n", "      <td>17.2</td>\n", "      <td>21.2</td>\n", "      <td>63.6</td>\n", "      <td>0.459</td>\n", "      <td>100</td>\n", "      <td>...</td>\n", "      <td>1013.5</td>\n", "      <td>38.6</td>\n", "      <td>16.1</td>\n", "      <td>233.1</td>\n", "      <td>8</td>\n", "      <td>2017-05-14T05:47:06</td>\n", "      <td>2017-05-14T20:14:16</td>\n", "      <td>0.62</td>\n", "      <td>Rain, Partially cloudy</td>\n", "      <td>obs</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 29 columns</p>\n", "</div>"], "text/plain": ["                         name  tempmax  tempmin  temp  feelslikemax  \\\n", "datetime                                                              \n", "2017-05-07  İstanbul, Türkiye     21.6     11.4  16.5          21.6   \n", "2017-05-08  İstanbul, Türkiye     23.3     13.5  18.0          23.3   \n", "2017-05-09  İstanbul, Türkiye     21.4     12.8  16.8          21.4   \n", "2017-05-10  İstanbul, Türkiye     15.8     13.2  14.5          15.8   \n", "2017-05-14  İstanbul, Türkiye     25.4     17.2  21.2          25.4   \n", "\n", "            feelslikemin  feelslike  humidity  precip  precipprob  ...  \\\n", "datetime                                                           ...   \n", "2017-05-07          11.4       16.5      75.1   0.127         100  ...   \n", "2017-05-08          13.5       18.0      72.0   5.723         100  ...   \n", "2017-05-09          12.8       16.8      78.5   1.778         100  ...   \n", "2017-05-10          13.2       14.5      88.9   3.358         100  ...   \n", "2017-05-14          17.2       21.2      63.6   0.459         100  ...   \n", "\n", "            sealevelpressure cloudcover  visibility  solarradiation  uvindex  \\\n", "datetime                                                                       \n", "2017-05-07            1009.4       64.5        13.0           206.5        6   \n", "2017-05-08            1005.0       51.4        14.5           257.2        8   \n", "2017-05-09            1004.5       57.1        14.7           244.5        7   \n", "2017-05-10            1010.9       74.2        14.3           156.4        6   \n", "2017-05-14            1013.5       38.6        16.1           233.1        8   \n", "\n", "                        sunrise               sunset  moonphase  \\\n", "datetime                                                          \n", "2017-05-07  2017-05-07T05:54:37  2017-05-07T20:07:11       0.38   \n", "2017-05-08  2017-05-08T05:53:28  2017-05-08T20:08:13       0.41   \n", "2017-05-09  2017-05-09T05:52:21  2017-05-09T20:09:14       0.45   \n", "2017-05-10  2017-05-10T05:51:15  2017-05-10T20:10:15       0.48   \n", "2017-05-14  2017-05-14T05:47:06  2017-05-14T20:14:16       0.62   \n", "\n", "                        conditions  source  \n", "datetime                                    \n", "2017-05-07  Rain, Partially cloudy     obs  \n", "2017-05-08  Rain, Partially cloudy     obs  \n", "2017-05-09  Rain, Partially cloudy     obs  \n", "2017-05-10  Rain, Partially cloudy     obs  \n", "2017-05-14  Rain, Partially cloudy     obs  \n", "\n", "[5 rows x 29 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "df=pd.read_csv('../Dataset/data_17-25.csv',index_col='datetime',parse_dates=True)\n", "df=df.dropna()\n", "df.head()\n", "#drop all columns except AvgTemp,tempmax,tempmin,temp,humidity,precip\n", "# df['AvgTemp']=(df['tempmax']-df['tempmin'])/2\n", "\n", "# df=df[['AvgTemp','tempmax','tempmin','temp','humidity','precip']]\n", "# print('Shape of data',df.shape)\n", "# df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "d15fca17-92e4-4c61-aeee-daaa934a5843", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "id": "7e2ac089-6f32-4efe-b880-ae397590988a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='datetime'>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df['tempmax'].plot(figsize=(12,5))\n"]}, {"cell_type": "code", "execution_count": 108, "id": "57c664cd-71ff-4ec0-a990-4a5765cf0cc3", "metadata": {}, "outputs": [], "source": ["\n", "from statsmodels.tsa.stattools import adfuller\n", "\n", "def adf_test(dataset):\n", "  dftest = adfuller(dataset, autolag = 'AIC')\n", "  print(\"1. ADF : \",dftest[0])\n", "  print(\"2. P-Value : \", dftest[1])\n", "  print(\"3. Num Of Lags : \", dftest[2])\n", "  print(\"4. Num Of Observations Used For ADF Regression and Critical Values Calculation :\", dftest[3])\n", "  print(\"5. Critical Values :\")\n", "  for key, val in dftest[4].items():\n", "      print(\"\\t\",key, \": \", val)\n", "     "]}, {"cell_type": "code", "execution_count": 109, "id": "ac486dff-8a9b-4837-ae4a-2edfba3567c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. ADF :  -6.554680125068781\n", "2. P-Value :  8.67593748019951e-09\n", "3. <PERSON><PERSON> Of Lags :  12\n", "4. Num Of Observations Used For ADF Regression and Critical Values Calculation : 1808\n", "5. Critical Values :\n", "\t 1% :  -3.433972018026501\n", "\t 5% :  -2.8631399192826676\n", "\t 10% :  -2.5676217442756872\n"]}], "source": ["adf_test(df['AvgTemp'])\n"]}, {"cell_type": "code", "execution_count": 110, "id": "ccf04057-08a6-4b4b-a0c3-0cb81f24e746", "metadata": {}, "outputs": [], "source": ["from pmdarima import auto_arima\n", "# Ignore harmless warnings\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "     "]}, {"cell_type": "code", "execution_count": 111, "id": "73aa35ea-bfdf-49a3-b3b9-3c9b55a56728", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>SARIMAX Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>           <td>y</td>        <th>  No. Observations:  </th>   <td>1821</td>   \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>           <td>SARIMAX(1, 0, 5)</td> <th>  Log Likelihood     </th> <td>-4139.901</td>\n", "</tr>\n", "<tr>\n", "  <th>Date:</th>            <td>Sat, 05 Jul 2025</td> <th>  AIC                </th> <td>8295.802</td> \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                <td>15:08:57</td>     <th>  BIC                </th> <td>8339.859</td> \n", "</tr>\n", "<tr>\n", "  <th>Sample:</th>                  <td>0</td>        <th>  HQIC               </th> <td>8312.055</td> \n", "</tr>\n", "<tr>\n", "  <th></th>                      <td> - 1821</td>     <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>        <td>opg</td>       <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>         <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>intercept</th> <td>    1.3611</td> <td>    0.397</td> <td>    3.429</td> <td> 0.001</td> <td>    0.583</td> <td>    2.139</td>\n", "</tr>\n", "<tr>\n", "  <th>ar.L1</th>     <td>    0.9707</td> <td>    0.009</td> <td>  113.485</td> <td> 0.000</td> <td>    0.954</td> <td>    0.987</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L1</th>     <td>   -0.1220</td> <td>    0.024</td> <td>   -5.088</td> <td> 0.000</td> <td>   -0.169</td> <td>   -0.075</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L2</th>     <td>   -0.2156</td> <td>    0.024</td> <td>   -8.841</td> <td> 0.000</td> <td>   -0.263</td> <td>   -0.168</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L3</th>     <td>   -0.2029</td> <td>    0.024</td> <td>   -8.430</td> <td> 0.000</td> <td>   -0.250</td> <td>   -0.156</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L4</th>     <td>   -0.1344</td> <td>    0.023</td> <td>   -5.884</td> <td> 0.000</td> <td>   -0.179</td> <td>   -0.090</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L5</th>     <td>   -0.0458</td> <td>    0.024</td> <td>   -1.874</td> <td> 0.061</td> <td>   -0.094</td> <td>    0.002</td>\n", "</tr>\n", "<tr>\n", "  <th>sigma2</th>    <td>    5.5003</td> <td>    0.172</td> <td>   31.930</td> <td> 0.000</td> <td>    5.163</td> <td>    5.838</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Ljung-Box (L1) (Q):</th>     <td>0.00</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>21.32</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(Q):</th>                <td>0.99</td> <th>  Prob(JB):          </th> <td>0.00</td> \n", "</tr>\n", "<tr>\n", "  <th>Heteroskedasticity (H):</th> <td>0.81</td> <th>  Skew:              </th> <td>-0.18</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(H) (two-sided):</th>    <td>0.01</td> <th>  Kurtosis:          </th> <td>3.40</td> \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/latex": ["\\begin{center}\n", "\\begin{tabular}{lclc}\n", "\\toprule\n", "\\textbf{Dep. Variable:}          &        y         & \\textbf{  No. Observations:  } &    1821     \\\\\n", "\\textbf{Model:}                  & SARIMAX(1, 0, 5) & \\textbf{  Log Likelihood     } & -4139.901   \\\\\n", "\\textbf{Date:}                   & Sat, 05 Jul 2025 & \\textbf{  AIC                } &  8295.802   \\\\\n", "\\textbf{Time:}                   &     15:08:57     & \\textbf{  BIC                } &  8339.859   \\\\\n", "\\textbf{Sample:}                 &        0         & \\textbf{  HQIC               } &  8312.055   \\\\\n", "\\textbf{}                        &      - 1821      & \\textbf{                     } &             \\\\\n", "\\textbf{Covariance Type:}        &       opg        & \\textbf{                     } &             \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lcccccc}\n", "                   & \\textbf{coef} & \\textbf{std err} & \\textbf{z} & \\textbf{P$> |$z$|$} & \\textbf{[0.025} & \\textbf{0.975]}  \\\\\n", "\\midrule\n", "\\textbf{intercept} &       1.3611  &        0.397     &     3.429  &         0.001        &        0.583    &        2.139     \\\\\n", "\\textbf{ar.L1}     &       0.9707  &        0.009     &   113.485  &         0.000        &        0.954    &        0.987     \\\\\n", "\\textbf{ma.L1}     &      -0.1220  &        0.024     &    -5.088  &         0.000        &       -0.169    &       -0.075     \\\\\n", "\\textbf{ma.L2}     &      -0.2156  &        0.024     &    -8.841  &         0.000        &       -0.263    &       -0.168     \\\\\n", "\\textbf{ma.L3}     &      -0.2029  &        0.024     &    -8.430  &         0.000        &       -0.250    &       -0.156     \\\\\n", "\\textbf{ma.L4}     &      -0.1344  &        0.023     &    -5.884  &         0.000        &       -0.179    &       -0.090     \\\\\n", "\\textbf{ma.L5}     &      -0.0458  &        0.024     &    -1.874  &         0.061        &       -0.094    &        0.002     \\\\\n", "\\textbf{sigma2}    &       5.5003  &        0.172     &    31.930  &         0.000        &        5.163    &        5.838     \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lclc}\n", "\\textbf{Ljung-Box (L1) (Q):}     & 0.00 & \\textbf{  <PERSON><PERSON><PERSON><PERSON>ra (JB):  } & 21.32  \\\\\n", "\\textbf{Prob(Q):}                & 0.99 & \\textbf{  Prob(JB):          } &  0.00  \\\\\n", "\\textbf{Heteroskedasticity (H):} & 0.81 & \\textbf{  Skew:              } & -0.18  \\\\\n", "\\textbf{Prob(H) (two-sided):}    & 0.01 & \\textbf{  Kurtosis:          } &  3.40  \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "%\\caption{SARIMAX Results}\n", "\\end{center}\n", "\n", "Warnings: \\newline\n", " [1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:                      y   No. Observations:                 1821\n", "Model:               SARIMAX(1, 0, 5)   Log Likelihood               -4139.901\n", "Date:                Sat, 05 Jul 2025   AIC                           8295.802\n", "Time:                        15:08:57   BIC                           8339.859\n", "Sample:                             0   HQIC                          8312.055\n", "                               - 1821                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept      1.3611      0.397      3.429      0.001       0.583       2.139\n", "ar.L1          0.9707      0.009    113.485      0.000       0.954       0.987\n", "ma.L1         -0.1220      0.024     -5.088      0.000      -0.169      -0.075\n", "ma.L2         -0.2156      0.024     -8.841      0.000      -0.263      -0.168\n", "ma.L3         -0.2029      0.024     -8.430      0.000      -0.250      -0.156\n", "ma.L4         -0.1344      0.023     -5.884      0.000      -0.179      -0.090\n", "ma.L5         -0.0458      0.024     -1.874      0.061      -0.094       0.002\n", "sigma2         5.5003      0.172     31.930      0.000       5.163       5.838\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JB):                21.32\n", "Prob(Q):                              0.99   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               0.81   Skew:                            -0.18\n", "Prob(H) (two-sided):                  0.01   Kurtosis:                         3.40\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "\"\"\""]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["stepwise_fit = auto_arima(df['AvgTemp'], \n", "                          suppress_warnings=True)           \n", "\n", "stepwise_fit.summary()"]}, {"cell_type": "code", "execution_count": 112, "id": "510d7aff-988c-46ac-8307-60d922ab393e", "metadata": {}, "outputs": [], "source": ["from statsmodels.tsa.arima.model import ARIMA\n"]}, {"cell_type": "code", "execution_count": 113, "id": "1c4091c7-f828-4cd3-995c-c3c49835075a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1821, 5)\n", "(1456, 5) (365, 5)\n", "MinTemp      36.0\n", "MaxTemp      52.0\n", "AvgTemp      44.0\n", "Sunrise     656.0\n", "Sunset     1754.0\n", "Name: 2017-12-30 00:00:00, dtype: float64 MinTemp      39.0\n", "MaxTemp      52.0\n", "AvgTemp      46.0\n", "Sunrise     656.0\n", "Sunset     1754.0\n", "Name: 2018-12-30 00:00:00, dtype: float64\n"]}], "source": ["\n", "print(df.shape)\n", "train=df.iloc[:-365]\n", "test=df.iloc[-365:]\n", "print(train.shape,test.shape)\n", "print(test.iloc[0],test.iloc[-1])"]}, {"cell_type": "code", "execution_count": 114, "id": "7fdce009-06a9-4875-84af-06a0890060f3", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>SARIMAX Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>        <td>AvgTemp</td>     <th>  No. Observations:  </th>   <td>1456</td>   \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>            <td>ARIMA(1, 0, 1)</td>  <th>  Log Likelihood     </th> <td>-3358.352</td>\n", "</tr>\n", "<tr>\n", "  <th>Date:</th>            <td>Sat, 05 Jul 2025</td> <th>  AIC                </th> <td>6724.704</td> \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                <td>15:08:57</td>     <th>  BIC                </th> <td>6745.838</td> \n", "</tr>\n", "<tr>\n", "  <th>Sample:</th>                  <td>0</td>        <th>  HQIC               </th> <td>6732.589</td> \n", "</tr>\n", "<tr>\n", "  <th></th>                      <td> - 1456</td>     <th>                     </th>     <td> </td>    \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>        <td>opg</td>       <th>                     </th>     <td> </td>    \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "     <td></td>       <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>  <td>   46.9215</td> <td>    0.309</td> <td>  151.807</td> <td> 0.000</td> <td>   46.316</td> <td>   47.527</td>\n", "</tr>\n", "<tr>\n", "  <th>ar.L1</th>  <td>    0.7673</td> <td>    0.021</td> <td>   37.239</td> <td> 0.000</td> <td>    0.727</td> <td>    0.808</td>\n", "</tr>\n", "<tr>\n", "  <th>ma.L1</th>  <td>    0.1226</td> <td>    0.031</td> <td>    3.951</td> <td> 0.000</td> <td>    0.062</td> <td>    0.183</td>\n", "</tr>\n", "<tr>\n", "  <th>sigma2</th> <td>    5.8973</td> <td>    0.191</td> <td>   30.823</td> <td> 0.000</td> <td>    5.522</td> <td>    6.272</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "  <th>Ljung-Box (L1) (Q):</th>     <td>0.02</td> <th>  <PERSON><PERSON><PERSON><PERSON><PERSON> (JB):  </th> <td>40.47</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(Q):</th>                <td>0.90</td> <th>  Prob(JB):          </th> <td>0.00</td> \n", "</tr>\n", "<tr>\n", "  <th>Heteroskedasticity (H):</th> <td>0.94</td> <th>  Skew:              </th> <td>-0.23</td>\n", "</tr>\n", "<tr>\n", "  <th>Prob(H) (two-sided):</th>    <td>0.53</td> <th>  Kurtosis:          </th> <td>3.67</td> \n", "</tr>\n", "</table><br/><br/>Warnings:<br/>[1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/latex": ["\\begin{center}\n", "\\begin{tabular}{lclc}\n", "\\toprule\n", "\\textbf{Dep. Variable:}          &     AvgTemp      & \\textbf{  No. Observations:  } &    1456     \\\\\n", "\\textbf{Model:}                  &  ARIMA(1, 0, 1)  & \\textbf{  Log Likelihood     } & -3358.352   \\\\\n", "\\textbf{Date:}                   & Sat, 05 Jul 2025 & \\textbf{  AIC                } &  6724.704   \\\\\n", "\\textbf{Time:}                   &     15:08:57     & \\textbf{  BIC                } &  6745.838   \\\\\n", "\\textbf{Sample:}                 &        0         & \\textbf{  HQIC               } &  6732.589   \\\\\n", "\\textbf{}                        &      - 1456      & \\textbf{                     } &             \\\\\n", "\\textbf{Covariance Type:}        &       opg        & \\textbf{                     } &             \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lcccccc}\n", "                & \\textbf{coef} & \\textbf{std err} & \\textbf{z} & \\textbf{P$> |$z$|$} & \\textbf{[0.025} & \\textbf{0.975]}  \\\\\n", "\\midrule\n", "\\textbf{const}  &      46.9215  &        0.309     &   151.807  &         0.000        &       46.316    &       47.527     \\\\\n", "\\textbf{ar.L1}  &       0.7673  &        0.021     &    37.239  &         0.000        &        0.727    &        0.808     \\\\\n", "\\textbf{ma.L1}  &       0.1226  &        0.031     &     3.951  &         0.000        &        0.062    &        0.183     \\\\\n", "\\textbf{sigma2} &       5.8973  &        0.191     &    30.823  &         0.000        &        5.522    &        6.272     \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "\\begin{tabular}{lclc}\n", "\\textbf{Ljung-Box (L1) (Q):}     & 0.02 & \\textbf{  <PERSON><PERSON><PERSON><PERSON>ra (JB):  } & 40.47  \\\\\n", "\\textbf{Prob(Q):}                & 0.90 & \\textbf{  Prob(JB):          } &  0.00  \\\\\n", "\\textbf{Heteroskedasticity (H):} & 0.94 & \\textbf{  Skew:              } & -0.23  \\\\\n", "\\textbf{Prob(H) (two-sided):}    & 0.53 & \\textbf{  Kurtosis:          } &  3.67  \\\\\n", "\\bottomrule\n", "\\end{tabular}\n", "%\\caption{SARIMAX Results}\n", "\\end{center}\n", "\n", "Warnings: \\newline\n", " [1] Covariance matrix calculated using the outer product of gradients (complex-step)."], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:                AvgTemp   No. Observations:                 1456\n", "Model:                 ARIMA(1, 0, 1)   Log Likelihood               -3358.352\n", "Date:                Sat, 05 Jul 2025   AIC                           6724.704\n", "Time:                        15:08:57   BIC                           6745.838\n", "Sample:                             0   HQIC                          6732.589\n", "                               - 1456                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const         46.9215      0.309    151.807      0.000      46.316      47.527\n", "ar.L1          0.7673      0.021     37.239      0.000       0.727       0.808\n", "ma.L1          0.1226      0.031      3.951      0.000       0.062       0.183\n", "sigma2         5.8973      0.191     30.823      0.000       5.522       6.272\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.02   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JB):                40.47\n", "Prob(Q):                              0.90   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               0.94   Skew:                            -0.23\n", "Prob(H) (two-sided):                  0.53   Kurtosis:                         3.67\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "\"\"\""]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}], "source": ["model=ARIMA(train['AvgTemp'],order=(1,0,1))\n", "model=model.fit()\n", "model.summary()"]}, {"cell_type": "code", "execution_count": 115, "id": "59d4a46d-941b-4e54-99d1-8e9bffef0aeb", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Length mismatch: Expected axis has 365 elements, new values have 30 elements", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[115], line 6\u001b[0m\n\u001b[1;32m      4\u001b[0m index_future_dates\u001b[38;5;241m=\u001b[39mpd\u001b[38;5;241m.\u001b[39mdate_range(start\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2018-12-01\u001b[39m\u001b[38;5;124m'\u001b[39m,end\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2018-12-30\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      5\u001b[0m pred\u001b[38;5;241m=\u001b[39mmodel\u001b[38;5;241m.\u001b[39mpredict(start\u001b[38;5;241m=\u001b[39mstart,end\u001b[38;5;241m=\u001b[39mend,typ\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlevels\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mrename(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mARIMA predictions\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m----> 6\u001b[0m pred\u001b[38;5;241m.\u001b[39mindex\u001b[38;5;241m=\u001b[39mindex_future_dates\n\u001b[1;32m      7\u001b[0m pred\u001b[38;5;241m.\u001b[39mplot(legend\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m      8\u001b[0m test[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAvgTemp\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mplot(legend\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/pandas/core/generic.py:6313\u001b[0m, in \u001b[0;36mNDFrame.__setattr__\u001b[0;34m(self, name, value)\u001b[0m\n\u001b[1;32m   6311\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   6312\u001b[0m     \u001b[38;5;28mobject\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__getattribute__\u001b[39m(\u001b[38;5;28mself\u001b[39m, name)\n\u001b[0;32m-> 6313\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mobject\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__setattr__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   6314\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAttribut<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m   6315\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m\n", "File \u001b[0;32mproperties.pyx:69\u001b[0m, in \u001b[0;36mpandas._libs.properties.AxisProperty.__set__\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/pandas/core/generic.py:814\u001b[0m, in \u001b[0;36mNDFrame._set_axis\u001b[0;34m(self, axis, labels)\u001b[0m\n\u001b[1;32m    809\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    810\u001b[0m \u001b[38;5;124;03mThis is called from the cython code when we set the `index` attribute\u001b[39;00m\n\u001b[1;32m    811\u001b[0m \u001b[38;5;124;03mdirectly, e.g. `series.index = [1, 2, 3]`.\u001b[39;00m\n\u001b[1;32m    812\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    813\u001b[0m labels \u001b[38;5;241m=\u001b[39m ensure_index(labels)\n\u001b[0;32m--> 814\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_mgr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mset_axis\u001b[49m\u001b[43m(\u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlabels\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    815\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_clear_item_cache()\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/pandas/core/internals/managers.py:238\u001b[0m, in \u001b[0;36mBaseBlockManager.set_axis\u001b[0;34m(self, axis, new_labels)\u001b[0m\n\u001b[1;32m    236\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mset_axis\u001b[39m(\u001b[38;5;28mself\u001b[39m, axis: AxisInt, new_labels: Index) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    237\u001b[0m     \u001b[38;5;66;03m# <PERSON><PERSON> is responsible for ensuring we have an Index object.\u001b[39;00m\n\u001b[0;32m--> 238\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_set_axis\u001b[49m\u001b[43m(\u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnew_labels\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    239\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxes[axis] \u001b[38;5;241m=\u001b[39m new_labels\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/pandas/core/internals/base.py:98\u001b[0m, in \u001b[0;36mDataManager._validate_set_axis\u001b[0;34m(self, axis, new_labels)\u001b[0m\n\u001b[1;32m     95\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[1;32m     97\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m new_len \u001b[38;5;241m!=\u001b[39m old_len:\n\u001b[0;32m---> 98\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m     99\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLength mismatch: Expected axis has \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mold_len\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m elements, new \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    100\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalues have \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnew_len\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m elements\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    101\u001b[0m     )\n", "\u001b[0;31mValueError\u001b[0m: Length mismatch: Expected axis has 365 elements, new values have 30 elements"]}], "source": ["\n", "start=len(train)\n", "end=len(train)+len(test)-1\n", "#if the predicted values dont have date values as index, you will have to uncomment the following two commented lines to plot a graph\n", "index_future_dates=pd.date_range(start='2018-12-01',end='2018-12-30')\n", "pred=model.predict(start=start,end=end,typ='levels').rename('ARIMA predictions')\n", "pred.index=index_future_dates\n", "pred.plot(legend=True)\n", "test['AvgTemp'].plot(legend=True)\n", "\n", "     "]}, {"cell_type": "code", "execution_count": 97, "id": "1b8b0b68-822b-41ce-9647-d5fab75d728e", "metadata": {}, "outputs": [{"data": {"text/plain": ["1428"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["end"]}, {"cell_type": "code", "execution_count": null, "id": "ad79834f-8726-434e-a612-7ef7773a39b5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 92, "id": "8e689969-a68d-4cd0-ade5-b6150458996c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='datetime'>"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred.plot(legend='ARIMA Predictions')\n", "test['AvgTemp'].plot(legend=True)\n", "     "]}, {"cell_type": "code", "execution_count": null, "id": "0b3d9b2f-59ed-4d99-ac4d-3c6492343cbf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}