{"cells": [{"cell_type": "code", "execution_count": 7, "id": "edd704b1", "metadata": {"scrolled": true}, "outputs": [{"ename": "KeyError", "evalue": "'Natural Gas Price'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/pandas/core/indexes/base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 3805\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[0;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mpandas/_libs/hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mpandas/_libs/hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: 'Natural Gas Price'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 34\u001b[0m\n\u001b[1;32m     28\u001b[0m data_folder \u001b[38;5;241m=\u001b[39m Path(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mC:/TimeSeries/\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     30\u001b[0m PORTFOY \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_excel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdataset_TS.xlsx\u001b[39m\u001b[38;5;124m'\u001b[39m, sheet_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mTUR Capacity util and IND Prod\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m---> 34\u001b[0m DAX\u001b[38;5;241m=\u001b[39m\u001b[43mPORTFOY\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mNatural Gas Price\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[1;32m     35\u001b[0m DAX\u001b[38;5;241m.\u001b[39mplot(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m15\u001b[39m,\u001b[38;5;241m4\u001b[39m))\n\u001b[1;32m     36\u001b[0m data2\u001b[38;5;241m=\u001b[39mDAX\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/pandas/core/frame.py:4102\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   4100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m   4101\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[0;32m-> 4102\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   4103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[1;32m   4104\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[0;32m~/miniconda3/envs/tf-cpu-env/lib/python3.9/site-packages/pandas/core/indexes/base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[1;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[1;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[1;32m   3810\u001b[0m     ):\n\u001b[1;32m   3811\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[0;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[1;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[1;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[1;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[1;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[1;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: 'Natural Gas Price'"]}], "source": ["import warnings\n", "import pandas as pd\n", "#from pandas import ExcelWriter\n", "#from pandas import ExcelFile\n", "import statsmodels.api as sm\n", "import  matplotlib.pylab as plt\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "from statsmodels.graphics.tsaplots import plot_pacf\n", "plt.style.use('fivethirtyeight')\n", "from pathlib import Path\n", "\n", "#pip install arch\n", "#ya da\n", "#\"Open Anaconda Promt and write:conda install -c bashtage arch \"\n", "# import arch\n", "# from arch.unitroot import ADF\n", "# from arch.unitroot import DFGLS\n", "# from arch.unitroot import PhillipsPerron\n", "# from arch.unitroot import KPSS\n", "# import statsmodels\n", "# from statsmodels.tsa.stattools import adfuller\n", "# from pathlib import Path\n", "\n", "\n", "\n", "\n", "\n", "data_folder = Path(\"C:/TimeSeries/\")\n", "\n", "PORTFOY = pd.read_excel('dataset_TS.xlsx', sheet_name='TUR Capacity util and IND Prod')\n", "\n", "\n", "\n", "DAX=PORTFOY['Natural Gas Price']\n", "DAX.plot(figsize=(15,4))\n", "data2=DAX\n", "#<PERSON>inin durağanlığın kontrolü\n", "\n", "\n", "def test_stationarity(data_n):    # adf_test = ADF(data2, trend='ct', max_lags=10, method='AIC') \n", "    adfTest = adfuller(data_n, autolag='AIC')\n", "    p_value=adfTest[1]\n", "    if p_value< 0.01:\n", "            print('se<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>; I(0)') \n", "            d=0\n", "    else:\n", "          dif_ADF=data_n.diff()\n", "          dif_ADF=dif_ADF.dropna()\n", "          dif_ADF = adfuller(dif_ADF, autolag='AIC')  \n", "          p_value2=dif_ADF[1]\n", "          if p_value2< 0.01:\n", "                print('seri far<PERSON><PERSON>; I(I)') \n", "                d=1\n", "    return d\n", "\n", "\n", "d=test_stationarity(data2) "]}, {"cell_type": "code", "execution_count": null, "id": "951c1847", "metadata": {}, "outputs": [], "source": ["test_size=21\n", "training_size=len(data2)-test_size\n", "test_sample=data2[training_size:len(data2)]\n", "test_sample=test_sample.reset_index()\n", "del test_sample['index']\n", "training_sample=data2[0:training_size]\n", "mod1=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(1,d,0))\n", "\n", "results1=mod1.fit(disp=False)\n", "print(results1.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals1=results1.resid\n", "residuals1=residuals1.iloc[1:-1]\n", "plot_acf(residuals1,lags=10)"]}, {"cell_type": "code", "execution_count": 13, "id": "57eabe3b", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "could not broadcast input array from shape (2587,) into shape (3001,)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_21184\\1874612925.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mplot_acf\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mtraining_sample\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mlags\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;36m3000\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\pandas\\util\\_decorators.py\u001b[0m in \u001b[0;36mwrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    205\u001b[0m                 \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    206\u001b[0m                     \u001b[0mkwargs\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mnew_arg_name\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mnew_arg_value\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 207\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    208\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    209\u001b[0m         \u001b[1;32mreturn\u001b[0m \u001b[0mcast\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mF\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\statsmodels\\graphics\\tsaplots.py\u001b[0m in \u001b[0;36mplot_acf\u001b[1;34m(x, ax, lags, alpha, use_vlines, adjusted, fft, missing, title, zero, auto_ylims, bartlett_confint, vlines_kwargs, **kwargs)\u001b[0m\n\u001b[0;32m    226\u001b[0m         \u001b[0macf_x\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mconfint\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0macf_x\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;36m2\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    227\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 228\u001b[1;33m     _plot_corr(\n\u001b[0m\u001b[0;32m    229\u001b[0m         \u001b[0max\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    230\u001b[0m         \u001b[0mtitle\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\statsmodels\\graphics\\tsaplots.py\u001b[0m in \u001b[0;36m_plot_corr\u001b[1;34m(ax, title, acf_x, confint, lags, irregular, use_vlines, vlines_kwargs, auto_ylims, **kwargs)\u001b[0m\n\u001b[0;32m     48\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     49\u001b[0m     \u001b[1;32mif\u001b[0m \u001b[0muse_vlines\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 50\u001b[1;33m         \u001b[0max\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvlines\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mlags\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m[\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0macf_x\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mvlines_kwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     51\u001b[0m         \u001b[0max\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0maxhline\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     52\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\matplotlib\\__init__.py\u001b[0m in \u001b[0;36minner\u001b[1;34m(ax, data, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1410\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0minner\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mdata\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1411\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mdata\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1412\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m*\u001b[0m\u001b[0mmap\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0msanitize_sequence\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0margs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1413\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1414\u001b[0m         \u001b[0mbound\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mnew_sig\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbind\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0max\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\anaconda3\\lib\\site-packages\\matplotlib\\axes\\_axes.py\u001b[0m in \u001b[0;36mvlines\u001b[1;34m(self, x, ymin, ymax, colors, linestyles, label, **kwargs)\u001b[0m\n\u001b[0;32m   1132\u001b[0m         \u001b[0mmasked_verts\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m0\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m1\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mymin\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1133\u001b[0m         \u001b[0mmasked_verts\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m1\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m0\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mx\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1134\u001b[1;33m         \u001b[0mmasked_verts\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m1\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m1\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mymax\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1135\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1136\u001b[0m         lines = mcoll.LineCollection(masked_verts, colors=colors,\n", "\u001b[1;32m~\\AppData\\Roaming\\Python\\Python39\\site-packages\\numpy\\ma\\core.py\u001b[0m in \u001b[0;36m__setitem__\u001b[1;34m(self, indx, value)\u001b[0m\n\u001b[0;32m   3375\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0m_mask\u001b[0m \u001b[1;32mis\u001b[0m \u001b[0mnomask\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   3376\u001b[0m             \u001b[1;31m# Set the data, then the mask\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 3377\u001b[1;33m             \u001b[0m_data\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mindx\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mdval\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   3378\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[0mmval\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[0mnomask\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   3379\u001b[0m                 \u001b[0m_mask\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_mask\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mmake_mask_none\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mshape\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0m_dtype\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mValueError\u001b[0m: could not broadcast input array from shape (2587,) into shape (3001,)"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": []}, {"cell_type": "code", "execution_count": 9, "id": "5bd84b33", "metadata": {}, "outputs": [], "source": ["#pseudo forecast\n", "pred1=results1.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo1=pred1.predicted_mean\n", "pred_pseudo1=pred_pseudo1.reset_index()\n", "del pred_pseudo1['index']\n", "pred_pseudo1.columns = ['predicted']\n", "ypredict1=pred_pseudo1.values\n", "yactual=test_sample.values\n", "mae1=abs(yactual-ypredict1).mean()\n", "mape1=100*(abs(yactual-ypredict1)/yactual).mean()"]}, {"cell_type": "code", "execution_count": 17, "id": "533ce709", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:      Natural Gas Price   No. Observations:                 2587\n", "Model:               SARIMAX(1, 1, 1)   Log Likelihood                1235.715\n", "Date:                Wed, 05 Jun 2024   AIC                          -2463.430\n", "Time:                        17:54:06   BIC                          -2439.998\n", "Sample:                             0   HQIC                         -2454.938\n", "                               - 2587                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     -0.0005      0.002     -0.194      0.846      -0.005       0.004\n", "ar.L1          0.1168      0.161      0.727      0.467      -0.198       0.431\n", "ma.L1         -0.1825      0.159     -1.145      0.252      -0.495       0.130\n", "sigma2         0.0225      0.000    108.064      0.000       0.022       0.023\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   Jarque<PERSON><PERSON>ra (JB):             28229.18\n", "Prob(Q):                              0.98   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               4.92   Skew:                            -0.25\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        19.18\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:      Natural Gas Price   No. Observations:                 2587\n", "Model:               SARIMAX(2, 1, 1)   Log Likelihood                1235.550\n", "Date:                Wed, 05 Jun 2024   AIC                          -2461.100\n", "Time:                        17:54:06   BIC                          -2431.811\n", "Sample:                             0   HQIC                         -2450.485\n", "                               - 2587                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     -0.0010      0.005     -0.197      0.844      -0.011       0.009\n", "ar.L1         -0.7945      0.501     -1.586      0.113      -1.776       0.187\n", "ar.L2         -0.0542      0.028     -1.959      0.050      -0.108    2.42e-05\n", "ma.L1          0.7299      0.501      1.456      0.145      -0.252       1.712\n", "sigma2         0.0225      0.000    107.124      0.000       0.022       0.023\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   Jar<PERSON><PERSON><PERSON>ra (JB):             28363.77\n", "Prob(Q):                              0.96   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               4.92   Skew:                            -0.26\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        19.22\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:      Natural Gas Price   No. Observations:                 2587\n", "Model:               SARIMAX(3, 1, 1)   Log Likelihood                1236.360\n", "Date:                Wed, 05 Jun 2024   AIC                          -2460.721\n", "Time:                        17:54:09   BIC                          -2425.574\n", "Sample:                             0   HQIC                         -2447.983\n", "                               - 2587                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     -0.0005      0.002     -0.194      0.846      -0.005       0.004\n", "ar.L1          0.1771      0.502      0.353      0.724      -0.806       1.160\n", "ar.L2         -0.0028      0.032     -0.089      0.929      -0.065       0.060\n", "ar.L3          0.0229      0.015      1.550      0.121      -0.006       0.052\n", "ma.L1         -0.2422      0.501     -0.483      0.629      -1.225       0.741\n", "sigma2         0.0225      0.000    107.180      0.000       0.022       0.023\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   Jar<PERSON><PERSON><PERSON>ra (JB):             28536.22\n", "Prob(Q):                              0.99   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               4.91   Skew:                            -0.26\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        19.27\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:      Natural Gas Price   No. Observations:                 2587\n", "Model:               SARIMAX(4, 1, 1)   Log Likelihood                1237.688\n", "Date:                Wed, 05 Jun 2024   AIC                          -2461.376\n", "Time:                        17:54:11   BIC                          -2420.371\n", "Sample:                             0   HQIC                         -2446.515\n", "                               - 2587                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     -0.0007      0.004     -0.189      0.850      -0.008       0.006\n", "ar.L1         -0.2554      0.292     -0.874      0.382      -0.828       0.318\n", "ar.L2         -0.0306      0.021     -1.472      0.141      -0.071       0.010\n", "ar.L3          0.0131      0.012      1.091      0.275      -0.010       0.037\n", "ar.L4          0.0362      0.011      3.210      0.001       0.014       0.058\n", "ma.L1          0.1900      0.293      0.649      0.517      -0.384       0.764\n", "sigma2         0.0225      0.000    105.140      0.000       0.022       0.023\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   Jar<PERSON><PERSON><PERSON>ra (JB):             27956.58\n", "Prob(Q):                              0.98   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               4.89   Skew:                            -0.28\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        19.10\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:      Natural Gas Price   No. Observations:                 2587\n", "Model:               SARIMAX(1, 1, 2)   Log Likelihood                1235.810\n", "Date:                Wed, 05 Jun 2024   AIC                          -2461.620\n", "Time:                        17:54:12   BIC                          -2432.330\n", "Sample:                             0   HQIC                         -2451.005\n", "                               - 2587                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "intercept     -0.0008      0.004     -0.197      0.843      -0.009       0.007\n", "ar.L1         -0.5057      0.591     -0.856      0.392      -1.664       0.652\n", "ma.L1          0.4403      0.590      0.746      0.456      -0.717       1.597\n", "ma.L2         -0.0447      0.034     -1.298      0.194      -0.112       0.023\n", "sigma2         0.0225      0.000    107.677      0.000       0.022       0.023\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   Jar<PERSON><PERSON><PERSON>ra (JB):             28363.67\n", "Prob(Q):                              1.00   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               4.92   Skew:                            -0.25\n", "Prob(H) (two-sided):                  0.00   Kurtosis:                        19.22\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#model 2\n", "mod2=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(1,d,1))\n", "\n", "results2=mod2.fit(disp=False)\n", "print(results2.summary())\n", "#results.plot_diagnostics()\n", "residuals2=results2.resid\n", "residuals2=residuals2.iloc[1:-1]\n", "plot_acf(residuals2,lags=10)\n", "\n", "#pseudo forecast\n", "pred2=results2.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo2=pred2.predicted_mean\n", "pred_pseudo2=pred_pseudo2.reset_index()\n", "del pred_pseudo2['index']\n", "pred_pseudo2.columns = ['predicted']\n", "ypredict2=pred_pseudo2.values\n", "mae2=abs(yactual-ypredict2).mean()\n", "mape2=100*(abs(yactual-ypredict2)/yactual).mean()\n", "#model 3\n", "\n", "\n", "mod3=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(2,d,1)) \n", "                                  \n", "results3=mod3.fit(disp=False)\n", "print(results3.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals3=results3.resid\n", "residuals3=residuals3.iloc[1:-1]\n", "plot_acf(residuals3,lags=10)                                         \n", "\n", "\n", "pred3=results3.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo3=pred3.predicted_mean\n", "pred_pseudo3=pred_pseudo3.reset_index()\n", "del pred_pseudo3['index']\n", "pred_pseudo3.columns = ['predicted']\n", "ypredict3=pred_pseudo3.values\n", "mae3=abs(yactual-ypredict3).mean()\n", "mape3=100*(abs(yactual-ypredict3)/yactual).mean()\n", "#model 4\n", "\n", "mod4=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(3,d,1)) \n", "                                  \n", "results4=mod4.fit(disp=False)\n", "print(results4.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals4=results4.resid\n", "residuals4=residuals4.iloc[1:-1]\n", "plot_acf(residuals4,lags=10)                                         \n", "\n", "\n", "pred4=results4.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo4=pred4.predicted_mean\n", "pred_pseudo4=pred_pseudo4.reset_index()\n", "del pred_pseudo4['index']\n", "pred_pseudo4.columns = ['predicted']\n", "ypredict4=pred_pseudo4.values\n", "mae4=abs(yactual-ypredict4).mean()\n", "mape4=100*(abs(yactual-ypredict4)/yactual).mean()\n", "#model 5\n", "\n", "\n", "                                       \n", "\n", "mod5=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(4,d,1)) \n", "                                  \n", "results5=mod5.fit(disp=False)\n", "print(results5.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals5=results5.resid\n", "residuals5=residuals5.iloc[1:-1]\n", "plot_acf(residuals5,lags=10)                                         \n", "\n", "\n", "pred5=results5.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo5=pred5.predicted_mean\n", "pred_pseudo5=pred_pseudo5.reset_index()\n", "del pred_pseudo5['index']\n", "pred_pseudo5.columns = ['predicted']\n", "ypredict5=pred_pseudo5.values\n", "mae5=abs(yactual-ypredict5).mean()\n", "mape5=100*(abs(yactual-ypredict5)/yactual).mean()\n", "\n", "mod6=sm.tsa.statespace.SARIMAX(training_sample,trend='c',order=(1,d,2)) \n", "                                  \n", "results6=mod6.fit(disp=False)\n", "print(results6.summary())\n", "\n", "#results.plot_diagnostics()\n", "residuals6=results6.resid\n", "residuals6=residuals6.iloc[1:-1]\n", "plot_acf(residuals6,lags=10)   \n", "pred6=results6.get_prediction(start=training_size,end=len(data2)-1, dynamic=True)\n", "\n", "pred_pseudo6=pred6.predicted_mean\n", "pred_pseudo6=pred_pseudo6.reset_index()\n", "del pred_pseudo6['index']\n", "pred_pseudo6.columns = ['predicted']\n", "ypredict6=pred_pseudo6.values\n", "mae6=abs(yactual-ypredict6).mean()\n", "mape6=100*(abs(yactual-ypredict6)/yactual).mean()\n"]}, {"cell_type": "code", "execution_count": 18, "id": "975e4e3d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10.503253661988778 10.523943775884902 10.514359823086517 10.521716976115968 10.499400405816951 10.527991479425685\n"]}], "source": ["print(mape1, mape2, mape3,mape4,mape5,mape6)"]}, {"cell_type": "code", "execution_count": 12, "id": "db3c6540", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\statsmodels\\tsa\\statespace\\kalman_filter.py:2290: ValueWarning: Dynamic prediction specified to begin during out-of-sample forecasting period, and so has no effect.\n", "  warn('Dynamic prediction specified to begin during'\n"]}], "source": ["#real out of sample predictions \n", "tpred_real=results5.get_prediction(start=len(data2),end=len(data2)+test_size,dynamic=True)\n", "pred_ci=tpred_real.conf_int()\n", "pred_real=tpred_real.predicted_mean\n", "pred_real=pred_real.reset_index()\n", "del pred_real['index']\n", "pred_real.columns = ['real predicted']"]}, {"cell_type": "code", "execution_count": 16, "id": "219045bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    real predicted\n", "0         2.787499\n", "1         2.786939\n", "2         2.786380\n", "3         2.785821\n", "4         2.785262\n", "5         2.784703\n", "6         2.784144\n", "7         2.783585\n", "8         2.783025\n", "9         2.782466\n", "10        2.781907\n", "11        2.781348\n", "12        2.780789\n", "13        2.780230\n", "14        2.779670\n", "15        2.779111\n", "16        2.778552\n", "17        2.777993\n", "18        2.777434\n", "19        2.776875\n", "20        2.776316\n", "21        2.775756\n"]}], "source": ["print(pred_real)"]}, {"cell_type": "code", "execution_count": null, "id": "82545777", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}