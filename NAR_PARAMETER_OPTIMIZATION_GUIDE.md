# NAR Neural Network Parameter Optimization Guide

## Executive Summary

This guide provides a comprehensive approach to optimizing Non-Linear Autoregressive Neural Network parameters for better fitting and more accurate temperature predictions. The key is systematic parameter tuning based on your specific data characteristics.

## Key Parameters to Optimize

### 1. **Data Preprocessing Parameters**

#### Scaler Selection
```python
# Test different scalers
scalers = {
    'MinMax': MinMaxScaler(feature_range=(0, 1)),      # Range: 0-1
    'Standard': StandardScaler(),                      # Mean: 0, Std: 1
    'Robust': RobustScaler()                          # Robust to outliers
}
```

**Recommendations:**
- **MinMaxScaler**: Best for bounded data (temperature ranges)
- **StandardScaler**: Good for normal distributions
- **RobustScaler**: Best for data with outliers

#### Missing Value Handling
```python
# Options for missing values
df = df.interpolate(method='linear')      # Linear interpolation
df = df.fillna(method='ffill')            # Forward fill
df = df.fillna(method='bfill')            # Backward fill
```

### 2. **Sequence Creation Parameters**

#### Look-back Period (Critical Parameter)
```python
# Test different look-back periods
look_back_periods = [4, 8, 12, 16, 20, 24, 52]  # Weeks for weekly data
```

**Optimal Values:**
- **Weekly data**: 26-78 weeks (6 months to 1.5 years)
- **Daily data**: 30-365 days (1 month to 1 year)
- **Monthly data**: 12-36 months (1-3 years)

**Selection Criteria:**
- Capture seasonal patterns (52 weeks for annual)
- Include trend information
- Balance between context and overfitting

#### Step Size
```python
# For multi-step forecasting
step_size = 1  # Predict next period
# step_size = 7  # Predict next week (for daily data)
```

### 3. **Model Architecture Parameters**

#### LSTM Units
```python
units_options = [16, 32, 64, 128, 256]
```

**Recommendations:**
- **Small dataset** (< 1000 samples): 16-32 units
- **Medium dataset** (1000-5000 samples): 32-64 units
- **Large dataset** (> 5000 samples): 64-128 units

#### Number of LSTM Layers
```python
layers_options = [1, 2, 3, 4]
```

**Guidelines:**
- **Simple patterns**: 1-2 layers
- **Complex patterns**: 2-3 layers
- **Very complex patterns**: 3-4 layers

#### Dense Layers
```python
dense_units = [10, 25, 50, 100]
```

**Architecture Patterns:**
```python
# Pattern 1: Gradual reduction
Dense(50) → Dense(25) → Dense(10) → Dense(1)

# Pattern 2: Bottleneck
Dense(100) → Dense(10) → Dense(1)

# Pattern 3: Simple
Dense(25) → Dense(1)
```

### 4. **Regularization Parameters**

#### Dropout Rate
```python
dropout_rates = [0.1, 0.2, 0.3, 0.4, 0.5]
```

**Recommendations:**
- **Low overfitting risk**: 0.1-0.2
- **Medium overfitting risk**: 0.2-0.3
- **High overfitting risk**: 0.3-0.4

#### Batch Normalization
```python
# Apply after each layer
model.add(LSTM(64))
model.add(BatchNormalization())  # Stabilizes training
model.add(Dropout(0.3))
```

#### L2 Regularization
```python
from tensorflow.keras.regularizers import l2

Dense(64, activation='relu', kernel_regularizer=l2(0.01))
```

### 5. **Training Parameters**

#### Optimizer Selection
```python
optimizers = {
    'Adam': Adam(learning_rate=0.001),           # Most popular
    'Adam_low': Adam(learning_rate=0.0001),      # Slower, more stable
    'RMSprop': RMSprop(learning_rate=0.001),     # Good for RNNs
    'SGD': SGD(learning_rate=0.01, momentum=0.9) # Traditional
}
```

**Recommendations:**
- **Default choice**: Adam with lr=0.001
- **Unstable training**: Adam with lr=0.0001
- **Fine-tuning**: SGD with momentum

#### Learning Rate
```python
learning_rates = [0.0001, 0.0005, 0.001, 0.005, 0.01]
```

**Learning Rate Schedule:**
```python
# Reduce learning rate on plateau
reduce_lr = ReduceLROnPlateau(
    monitor='val_loss',
    factor=0.5,        # Reduce by half
    patience=10,       # Wait 10 epochs
    min_lr=0.00001     # Minimum learning rate
)
```

#### Batch Size
```python
batch_sizes = [16, 32, 64, 128, 256]
```

**Selection Criteria:**
- **Small dataset**: 16-32
- **Medium dataset**: 32-64
- **Large dataset**: 64-128
- **Memory constraints**: Smaller batches

### 6. **Callbacks and Monitoring**

#### Early Stopping
```python
early_stopping = EarlyStopping(
    monitor='val_loss',
    patience=20,              # Wait 20 epochs
    restore_best_weights=True, # Keep best model
    verbose=1
)
```

#### Model Checkpoint
```python
checkpoint = ModelCheckpoint(
    'best_model.h5',
    monitor='val_loss',
    save_best_only=True,
    verbose=1
)
```

### 7. **Hyperparameter Optimization Strategies**

#### Grid Search
```python
# Test all combinations
param_grid = {
    'units': [32, 64, 128],
    'layers': [2, 3],
    'dropout': [0.2, 0.3, 0.4],
    'batch_size': [32, 64]
}
```

#### Random Search
```python
# Test random combinations
from scipy.stats import uniform, randint

param_distributions = {
    'units': randint(16, 256),
    'dropout': uniform(0.1, 0.4),
    'learning_rate': uniform(0.0001, 0.01)
}
```

#### Bayesian Optimization
```python
# Use Optuna or similar
import optuna

def objective(trial):
    units = trial.suggest_int('units', 16, 256)
    dropout = trial.suggest_float('dropout', 0.1, 0.5)
    lr = trial.suggest_float('lr', 0.0001, 0.01)
    # Train model and return validation score
    return validation_rmse
```

### 8. **Cross-Validation for Time Series**

#### Time Series Split
```python
from sklearn.model_selection import TimeSeriesSplit

tscv = TimeSeriesSplit(n_splits=5)
for train_idx, val_idx in tscv.split(X):
    X_train, X_val = X[train_idx], X[val_idx]
    y_train, y_val = y[train_idx], y[val_idx]
    # Train and evaluate model
```

#### Walk-Forward Validation
```python
# Expanding window approach
for i in range(train_size, len(data)):
    train_data = data[:i]
    test_data = data[i:i+1]
    # Train on train_data, predict test_data
```

### 9. **Ensemble Methods**

#### Multiple Architectures
```python
models = []
architectures = [
    {'units': 32, 'layers': 2, 'dropout': 0.2},
    {'units': 64, 'layers': 3, 'dropout': 0.3},
    {'units': 128, 'layers': 3, 'dropout': 0.4}
]

for arch in architectures:
    model = build_model(**arch)
    model.fit(X_train, y_train)
    models.append(model)
```

#### Weighted Averaging
```python
# Weight by validation performance
weights = [0.3, 0.4, 0.3]  # Based on validation scores
ensemble_pred = sum(w * m.predict(X_test) for w, m in zip(weights, models))
```

### 10. **Performance Metrics**

#### Primary Metrics
```python
from sklearn.metrics import mean_squared_error, mean_absolute_error

rmse = np.sqrt(mean_squared_error(y_true, y_pred))
mae = mean_absolute_error(y_true, y_pred)
mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
```

#### Additional Metrics
```python
# Directional accuracy
directional_accuracy = np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred)))

# Theil's U statistic
theil_u = np.sqrt(np.mean((y_pred - y_true)**2)) / np.sqrt(np.mean(y_true**2))
```

## Optimization Workflow

### Phase 1: Baseline Model
1. Use default parameters
2. Establish baseline performance
3. Identify major issues

### Phase 2: Critical Parameters
1. **Look-back period** (most important)
2. **Model complexity** (units, layers)
3. **Regularization** (dropout, batch norm)

### Phase 3: Fine-tuning
1. **Learning rate** optimization
2. **Batch size** selection
3. **Optimizer** comparison

### Phase 4: Advanced Techniques
1. **Ensemble methods**
2. **Cross-validation**
3. **Hyperparameter optimization**

### Phase 5: Validation
1. **Out-of-sample testing**
2. **Robustness checks**
3. **Performance monitoring**

## Recommended Parameter Ranges

### For Weekly Temperature Data:
```python
optimal_params = {
    'look_back': 52,           # 1 year
    'units': 64,               # Medium complexity
    'layers': 3,               # 3 LSTM layers
    'dropout': 0.3,            # Moderate regularization
    'batch_size': 32,          # Standard batch size
    'learning_rate': 0.001,    # Adam default
    'epochs': 100,             # With early stopping
    'validation_split': 0.2    # 20% validation
}
```

### For Daily Temperature Data:
```python
optimal_params = {
    'look_back': 365,          # 1 year
    'units': 128,              # Higher complexity
    'layers': 3,               # 3 LSTM layers
    'dropout': 0.4,            # Higher regularization
    'batch_size': 64,          # Larger batch size
    'learning_rate': 0.001,    # Adam default
    'epochs': 150,             # More epochs
    'validation_split': 0.2    # 20% validation
}
```

## Expected Performance Improvements

### Parameter Optimization Impact:
- **Look-back period**: 10-20% improvement
- **Model architecture**: 15-25% improvement
- **Regularization**: 5-15% improvement
- **Training parameters**: 5-10% improvement
- **Ensemble methods**: 10-20% improvement

### Overall Expected Improvement:
- **RMSE**: 20-40% reduction
- **MAPE**: 15-30% reduction
- **Training stability**: Significant improvement
- **Generalization**: Better out-of-sample performance

## Implementation Checklist

- [ ] **Data preprocessing**: Choose appropriate scaler
- [ ] **Sequence creation**: Optimize look-back period
- [ ] **Architecture design**: Select units and layers
- [ ] **Regularization**: Add dropout and batch norm
- [ ] **Training setup**: Choose optimizer and learning rate
- [ ] **Callbacks**: Implement early stopping and LR reduction
- [ ] **Cross-validation**: Use time series split
- [ ] **Hyperparameter tuning**: Grid/random/Bayesian search
- [ ] **Ensemble methods**: Combine multiple models
- [ ] **Performance evaluation**: Multiple metrics
- [ ] **Robustness testing**: Out-of-sample validation

## Conclusion

The key to optimizing NAR neural network performance is systematic parameter tuning based on your specific data characteristics. Focus on:

1. **Look-back period** (most critical)
2. **Model complexity** (balance fit vs. overfitting)
3. **Regularization** (prevent overfitting)
4. **Training parameters** (ensure convergence)
5. **Ensemble methods** (improve robustness)

Start with the recommended parameter ranges and iteratively optimize based on validation performance. The expected improvement in forecasting accuracy is 20-40% with proper optimization. 