# Step-by-Step Notebook Fixes

## Cell-by-Cell Fixes for Your `arima2.ipynb` Notebook

### Cell 12 (The Problematic Prediction Cell)
**Replace this cell:**
```python
start=len(train)
end=len(train)+len(test)-1
#if the predicted values dont have date values as index, you will have to uncomment the following two commented lines to plot a graph
#index_future_dates=pd.date_range(start='2018-12-01',end='2018-12-30')
pred=model.predict(start=start,end=end,typ='levels').rename('ARIMA predictions')
#pred.index=index_future_dates
pred.plot(legend=True)
test['AvgTemp'].plot(legend=True)
```

**With this fixed version:**
```python
start = len(train)
end = len(train) + len(test) - 1

# Make predictions
predictions = model.predict(start=start, end=end, typ='levels')

# CRITICAL FIX: Assign proper date index to predictions
predictions.index = test.index

print('Predictions shape:', predictions.shape)
print('Test data shape:', test['AvgTemp'].shape)
print('Index match:', predictions.index.equals(test.index))

# Plot with proper syntax
plt.figure(figsize=(15, 8))
plt.plot(train.index, train['AvgTemp'], label='Training Data', color='blue', alpha=0.7)
plt.plot(test.index, test['AvgTemp'], label='Actual Test Data', color='green', linewidth=2)
plt.plot(predictions.index, predictions, label='ARIMA Predictions', color='red', linewidth=2, linestyle='--')

plt.title('ARIMA Model: Training, Test, and Predictions', fontsize=16)
plt.xlabel('Date', fontsize=12)
plt.ylabel('Average Temperature (°F)', fontsize=12)
plt.legend(fontsize=12)  # FIXED: removed quotes around legend
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()
```

### Cell 13 (The Second Plotting Cell)
**Replace this cell:**
```python
pred.plot(legend='ARIMA Predictions')
test['AvgTemp'].plot(legend=True)
```

**With this fixed version:**
```python
# Calculate performance metrics
from sklearn.metrics import mean_absolute_error, mean_squared_error
import numpy as np

mae = mean_absolute_error(test['AvgTemp'], predictions)
rmse = np.sqrt(mean_squared_error(test['AvgTemp'], predictions))

print(f"MAE: {mae:.2f}°F")
print(f"RMSE: {rmse:.2f}°F")

# Show comparison
comparison = pd.DataFrame({
    'Actual': test['AvgTemp'],
    'Predicted': predictions,
    'Difference': test['AvgTemp'] - predictions
})

print("\nFirst 10 predictions vs actual:")
print(comparison.head(10))
```

### Add a New Cell for Residuals Analysis
**Add this new cell:**
```python
# Residuals analysis
residuals = test['AvgTemp'] - predictions

plt.figure(figsize=(12, 4))
plt.subplot(1, 2, 1)
plt.plot(residuals.index, residuals, color='purple')
plt.title('Residuals Over Time')
plt.axhline(y=0, color='red', linestyle='--')
plt.grid(True, alpha=0.3)

plt.subplot(1, 2, 2)
plt.hist(residuals, bins=15, color='skyblue', edgecolor='black', alpha=0.7)
plt.title('Residuals Distribution')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
```

## Key Changes Made

### 1. **Removed `.rename()` Method**
- **Before:** `pred=model.predict(...).rename('ARIMA predictions')`
- **After:** `predictions = model.predict(...)`

### 2. **Fixed Index Assignment**
- **Before:** Predictions had integer indices
- **After:** `predictions.index = test.index` (assigns proper date indices)

### 3. **Fixed Legend Syntax**
- **Before:** `plt.legend('ARIMA Predictions')` (incorrect)
- **After:** `plt.legend(fontsize=12)` (correct)

### 4. **Added Proper Plotting**
- **Before:** Separate plot calls that didn't align
- **After:** Single figure with all three lines properly aligned

### 5. **Added Performance Metrics**
- MAE and RMSE calculations
- Comparison dataframe
- Residuals analysis

## What These Fixes Solve

1. **Empty Plots**: Fixed by ensuring predictions and actual data have the same date index
2. **Model.predict Issues**: Fixed by removing the problematic `.rename()` call and properly indexing predictions
3. **Plotting Problems**: Fixed by using correct matplotlib syntax and ensuring data alignment
4. **Missing Analysis**: Added performance metrics and residual analysis for better model evaluation

## Testing the Fixes

After applying these fixes, you should see:
- ✅ A plot showing training data (blue), actual test data (green), and predictions (red dashed)
- ✅ Performance metrics printed (MAE and RMSE)
- ✅ A comparison table showing actual vs predicted values
- ✅ Residuals plots showing model performance

The main issue was that `model.predict()` returns predictions with integer indices, but your test data has date indices. The fix ensures both have the same date index for proper plotting. 